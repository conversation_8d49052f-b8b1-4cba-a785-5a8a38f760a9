package com.sss.fatora.domain.issuing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_PERSON_ID")
public class Person {

    @Id
    @Column(name = "ID")
    Long id;

    @Column(name = "FIRST_NAME")
    String firstName;

    @Column(name = "SURNAME")
    String lastName;

    @Column(name = "PERSON_ID")
    Long personId;

    @Column(name = "ID_TYPE")
    String idType;

    @Column(name = "ID_SERIES")
    String idSeries;

    @Column(name = "ID_NUMBER")
    String idNumber;
}
