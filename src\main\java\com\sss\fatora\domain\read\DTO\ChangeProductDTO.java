package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class ChangeProductDTO {
    @JsonProperty(value = "Customer_Number")
    String Customer_Number;
    @JsonProperty(value = "Contract_Number")
    String Contract_Number;
    @JsonProperty(value = "Product_Number")
    String Product_Number;
    @JsonProperty (value = "Product_Id")
    Long Product_Id;
    @JsonProperty (value= "Agent_Id")
    String Agent_Id;

}
