package com.sss.fatora.service.local;

import com.sss.fatora.dao.local.ApplicationUserDao;
import com.sss.fatora.dao.local.BankDao;
import com.sss.fatora.domain.local.*;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional("localDBTransactionManager")
public class ApplicationUserService extends GenericService<ApplicationUserDao, ApplicationUser, Integer> {

    @Autowired
    private ApplicationContext context;

    final PaginationService paginationService;
    final LogService logService;
    final ApplicationUserPrivilegeService applicationUserPrivilegeService;
    final MessageSource messageSource;
    final BankDao bankDao;

    public ApplicationUserService(PaginationService paginationService, LogService logService,
            ApplicationUserPrivilegeService applicationUserPrivilegeService, Environment env,
            MessageSource messageSource, BankDao bankDao) {
        this.paginationService = paginationService;
        this.logService = logService;
        this.applicationUserPrivilegeService = applicationUserPrivilegeService;
        this.messageSource = messageSource;
        this.bankDao = bankDao;
    }

    @Override
    public ApplicationUser merge(ApplicationUser domain) throws Exception {
        if (domain.getId() == null) {
            BCryptPasswordEncoder encoder = (BCryptPasswordEncoder) context.getBean("passwordEncoder");
            domain.setPassword(encoder.encode(domain.getPassword()));
            checkUserEmailValidity(domain.getEmail());
            domain.setRecordStatus(1);
        } else {
            Optional<ApplicationUser> oldUser = dao.findById(domain.getId());
            if (oldUser.isPresent() && !oldUser.get().getEmail().equals(domain.getEmail())) {
                checkUserEmailValidity(domain.getEmail());
            }
        }
        /*
         * if(domain.getPassword()==null){
         * domain.setPassword(this.getById(domain.getId()).getPassword());
         * }
         */
        return super.merge(domain);
    }

    /**
     * @param id This Parameter Is The User Id That The Front-End Gave Us
     *           <p>
     *           This Function Disable The Specified User ( Can't Work On The System
     *           )
     */
    public Boolean disableUser(Integer id) {
        ApplicationUser applicationUser = this.getById(id);
        if (applicationUser != null) {
            dao.disableUser(id);
            return true;
        } else {
            return null;
        }
    }

    /**
     * @param id This Parameter Is The User Id That The Front-End Gave Us
     *           <p>
     *           This Function Enable The Specified User ( Can Work On The System )
     */
    public Boolean enableUser(Integer id) {
        ApplicationUser applicationUser = this.getById(id);
        if (applicationUser != null) {
            dao.enableUser(id);
            return true;
        } else {
            return null;
        }
    }

    /**
     * @param applicationUser This Parameter is For The Information Of The User
     * @param oldPassword     This Parameter is For The Old Password
     * @param newPassword     This Parameter is For The New Password
     *                        <p>
     *                        This Function Update The Password Of The User After
     *                        First Log In
     */
    public ResponseObject forceUpdateUser(ApplicationUser applicationUser, String oldPassword, String newPassword)
            throws Exception {
        BCryptPasswordEncoder encoder = (BCryptPasswordEncoder) context.getBean("passwordEncoder");
        if (encoder.matches(oldPassword, applicationUser.getPassword())) {
            String encryptedNewPass = encoder.encode(newPassword);
            applicationUser.setPassword(encryptedNewPass);
            applicationUser.setForcePasswordUpdated(true);
            dao.updatePassword(encryptedNewPass, applicationUser.getId());
            return ResponseObject.UPDATED_SUCCESS(applicationUser, null);
        } else
            return null;
    }

    /*
     * public Page search(Pagination pagination, ApplicationUser applicationUser) {
     * try {
     * return dao.search(paginationService.getPagination(pagination),
     * applicationUser.getFirstName(),
     * applicationUser.getLastName(),
     * applicationUser.getDomainName(),
     * applicationUser.getEmail(),
     * applicationUser.getMobile(),
     * applicationUser.getUserType(),
     * applicationUser.getStatus());
     * 
     * } catch (Exception exception) {
     * throw exception;
     * }
     * }
     */

    /**
     * @param applicationUser This Parameter Is For The Value Of Filters Send From
     *                        The Front-End
     *                        <p>
     *                        This Function Add Certain Conditions To Where Clause
     *                        That Can't Be Added To The Filter (ApplicationUser)
     *                        Directly And Then Request The Dynamic Search Function
     *                        ( dynamicSearch() )
     */

    public Page<ApplicationUser> search(Pagination pagination, Map<String, Operator> filterOperator,
            ApplicationUser applicationUser) throws InvocationTargetException, IntrospectionException,
            NoSuchFieldException, IllegalAccessException, ParseException {
        Page<ApplicationUser> applicationUsers;
        String additionalConstraint = "";
        ApplicationUser currentUser = CustomUserDetails.getCurrentInstance().getApplicationUser();

        if (applicationUser == null)
            applicationUser = new ApplicationUser();
        if (applicationUser.getBank() == null) {
            applicationUser.setBank(new Bank());
            applicationUser.getBank().setRecordStatus(null);
        }
        applicationUser.setRecordStatus(1);

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("ApplicationUser.creationDate");
            pagination.setOrderType("DESC");
        }

        applicationUser.setForcePasswordUpdated(null);

        applicationUsers = this.dynamicSearch(applicationUser, pagination, additionalConstraint, filterOperator, null,false);

        return applicationUsers;
    }

    /**
     * @param applicationUserId This Parameter is For The Id Of The User
     * @param oldPassword       This Parameter is For The Old Password
     * @param newPassword       This Parameter is For The New Password
     *                          <p>
     *                          This Function Update The Password Of A Specific User
     */
    public Boolean resetPassword(Integer applicationUserId, String newPassword, String oldPassword) {
        try {

            BCryptPasswordEncoder encoder = (BCryptPasswordEncoder) context.getBean("passwordEncoder");
            if (applicationUserId != null) {
                dao.resetPassword(applicationUserId, encoder.encode(newPassword));
            } else {
                ApplicationUser user = CustomUserDetails.getCurrentInstance().getApplicationUser();
                if (encoder.matches(oldPassword, user.getPassword())) {
                    dao.resetPassword(user.getId(), encoder.encode(newPassword));
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * @param userId      This Parameter is For The Id Of The User
     * @param forceDelete This Parameter is For Deciding Whether To Delete The User
     *                    And It's Relations Or Not
     *                    <p>
     *                    This Function Delete The User And It's Relations ( Logs,
     *                    Privileges )
     */
    public Boolean delete(Integer userId, Boolean forceDelete) {
        try {
            // List<Log> logs = logService.getLogsByUserId(userId);
            List<ApplicationUserPrivilege> privileges = applicationUserPrivilegeService.getByUserId(userId);
            if (forceDelete != null && forceDelete) {
                // for (Log log : logs) {
                // logService.delete(log.getId());
                // }

                for (ApplicationUserPrivilege privilege : privileges) {
                    applicationUserPrivilegeService.delete(privilege.getId());
                }
            } else {
                if (privileges.size() > 0) {
                    CustomUserDetails.getCurrentInstance().getErrorsList()
                            .add(messageSource.getMessage("delete_with_correlations", null, null));
                    return false;
                }
            }
            // this.delete(userId);
            dao.disableUser(userId);
            dao.softDelete(userId);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * @param bankId This Parameter Is For The Bank Id
     *               <p>
     *               This Function Get All The Users By Bank Id
     */
    public List<ApplicationUser> getAllUsersByBankId(Integer bankId) {
        return dao.findByBankId(bankId);
    }

    /**
     * This Function Get All The Users Based On User Type
     */
    public List<ApplicationUser> getAllUsers() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<ApplicationUser> users = new ArrayList<>();
        users.add(applicationUser);
        return users;

    }

    /**
     * @param forSearch This Parameter Is For Using The Response In Search Filters
     *                  <p>
     *                  This Function Get The Prefix Of The Bank Depending On User
     *                  Type {@link com.sss.fatora.utils.constants.UserType}
     */
    public List<Map> getUserPrefix(Boolean forSearch) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<Map> bank;
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            bank = bankDao.getPrefixById(applicationUser.getBank().getId());
        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType())) {
            bank = bankDao.getAllPrefixesById();
            if (forSearch) {
                Map<String, String> allBanks = new HashMap<>();
                allBanks.put("Prefix", "*");
                allBanks.put("Code", "");
                allBanks.put("BankName", "All");
                bank.add(allBanks);
            }
        } else {
            bank = null;
        }
        return bank;
    }

    public void checkUserEmailValidity(String email) throws Exception {
        ApplicationUser applicationUser = dao.findByEmail(email);
        if (applicationUser != null)
            throw new Exception("Email Already Exist Please Try Again");
    }

    public List<Map<String,Object>> getExternalAndInternalUsers() {
        List<Object[]> results;
            List<Map<String, Object>> userList = new ArrayList<>();

        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            Integer bankId = applicationUser.getBank().getId();
            results = dao.getExternalAndInternalUsers(bankId);

        } else {
            results = dao.findAllByStatus();
        }
        for (Object[] row : results) {
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("id", row[0]);
        userMap.put("fullName", row[1]);
        userList.add(userMap);
        }
        return userList;
    }

}
