package com.sss.fatora.service.local;

import com.sss.fatora.dao.local.ConfigDao;
import com.sss.fatora.domain.local.Config;
import com.sss.fatora.service.generic.GenericService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@PropertySource("classpath:generalProps/config.properties")
@Transactional("localDBTransactionManager")
public class ConfigService extends GenericService<ConfigDao, Config, Long> {

    @Autowired
    Environment env;

    /**
     * All Functions In This Service Get A Column Full Of Data From Config Table
     * And Make A List From This Column
     *
     * NOTE : For Further Understanding Check The Description Column In Config Table
     * */

    public Integer getMaxExportSize(){
        Integer maxSize=Integer.parseInt(dao.getColumns(env.getProperty("MaxExportSize")));
        return maxSize;
    }
//    @Cacheable("CardHeaders")
    public List<String> getCardHeaders(){
        String headers=dao.getColumns(env.getProperty("CardHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }
//    @Cacheable("CardColumns")
    public List<String> getCardColumns(){
        String headers=dao.getColumns(env.getProperty("CardColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

//    @Cacheable("CardHeadersWithDetails")
    public List<String> getCardHeadersWithDetails(){
        String headers=dao.getColumns(env.getProperty("CardHeadersWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }
//    @Cacheable("CardColumnsWithDetails")
    public List<String> getCardColumnsWithDetails(){
        String headers=dao.getColumns(env.getProperty("CardColumnsWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

//    @Cacheable("TransactionHeaders")
    public List<String> getTransactionHeaders(){
        String headers=dao.getColumns(env.getProperty("TransactionHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }
//    @Cacheable("TransactionColumns")
    public List<String> getTransactionColumns(){
        String headers=dao.getColumns(env.getProperty("TransactionColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }


//    @Cacheable("TransactionHeadersWithDetails")
    public List<String> getTransactionHeadersWithDetails(){
        String headers=dao.getColumns(env.getProperty("TransactionHeadersWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }
//    @Cacheable("TransactionColumnsWithDetails")
    public List<String> getTransactionColumnsWithDetails(){
        String headers=dao.getColumns(env.getProperty("TransactionColumnsWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    //@Cacheable("LogsHeaders")
    public List<String> getLogsHeaders(){
        String headers=dao.getColumns(env.getProperty("LogsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }
    //@Cacheable("LogsColumns")
    public List<String> getLogsColumns(){
        String headers=dao.getColumns(env.getProperty("LogsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsSearchesHeaders(){
        String headers=dao.getColumns(env.getProperty("LogSearchHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsSearchesColumns(){
        String headers=dao.getColumns(env.getProperty("LogSearchColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsDetailsHeaders(){
        String headers=dao.getColumns(env.getProperty("LogDetailsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsDetailsColumns(){
        String headers=dao.getColumns(env.getProperty("LogDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsActionsHeaders(){
        String headers=dao.getColumns(env.getProperty("LogActionHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsActionsColumns(){
        String headers=dao.getColumns(env.getProperty("LogActionColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsExportsHeaders(){
        String headers=dao.getColumns(env.getProperty("LogExportHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsExportsColumns(){
        String headers=dao.getColumns(env.getProperty("LogExportColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }
    public List<String> getLogsRequestsHeaders(){
        String headers=dao.getColumns(env.getProperty("LogRequestsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

    public List<String> getLogsRequestsColumns(){
        String headers=dao.getColumns(env.getProperty("LogRequestsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(headers.split(",")));
        return list;
    }

//    @Cacheable("TransactionMainColumns")
    public List<String> getTransactionMainColumns(){
        String columns = dao.getColumns(env.getProperty("TransactionMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
//    @Cacheable("TransactionDetailsColumns")
    public List<String> getTransactionDetailsColumns(){
        String columns = dao.getColumns(env.getProperty("TransactionDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
//    @Cacheable("CardMainColumns")
    public List<String> getCardMainColumns(){
        String columns = dao.getColumns(env.getProperty("CardMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;

    }
//    @Cacheable("CardDetailsColumns")
    public List<String> getCardDetailsColumns(){
        String columns = dao.getColumns(env.getProperty("CardDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

   // @Cacheable("Chunk")
    public Integer getChunk(){
        String chunk = dao.getColumns(env.getProperty("Chunk"));
        return Integer.parseInt(chunk);
    }

    public List<String> getDraftsColumns() {
        String columns = dao.getColumns(env.getProperty("Drafts"));
        return Arrays.asList(columns.split(","));
    }

    public List<String> getActionType() {
        String columns = dao.getColumns("ActionType");
        return Arrays.asList(columns.split(","));
    }
    public List<String> getAcquiringActionType(){
        String columns = dao.getColumns("ActionTypeForAcquiringRequest");
        return Arrays.asList(columns.split(","));
    }

    public List<String> getActionTypeForRequests() {
        String columns = dao.getColumns("ActionTypeForRequests");
        return Arrays.asList(columns.split(","));
    }

    public List<String> getApplicationDraftHeaders() {
        String columns = dao.getColumns(env.getProperty("ApplicationDraftHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getApplicationDraftColumns() {
        String columns = dao.getColumns(env.getProperty("ApplicationDraftColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getApplicationSubmitHeaders() {
        String columns = dao.getColumns(env.getProperty("ApplicationSubmitHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getApplicationSubmitColumns() {
        String columns = dao.getColumns(env.getProperty("ApplicationSubmitColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getApplicationArchivedHeaders() {
        String columns = dao.getColumns(env.getProperty("ApplicationArchivedHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getApplicationArchivedColumns() {
        String columns = dao.getColumns(env.getProperty("ApplicationArchivedColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }


    public List<String>  getBankSMSColumns() {
        String columns = dao.getColumns(env.getProperty("BankSMSColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getFNotifyColumns() {
        String columns = dao.getColumns(env.getProperty("FNotifyColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalColumns() {
        String columns = dao.getColumns(env.getProperty("TerminalMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getATMTerminalColumns() {
        String columns = dao.getColumns(env.getProperty("TerminalATMMainColumns"));

        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));

        return list;
    }
    public List<String>  getTerminalColumnsWithDetails() {
        String columns = dao.getColumns(env.getProperty("TerminalDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalExportMainColumns() {
        String columns = dao.getColumns(env.getProperty("TerminalExportMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String>  getTerminalExportMainHeaders() {
        String columns = dao.getColumns(env.getProperty("TerminalExportMainHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalPOSExportDetailsColumns() {
        String columns = dao.getColumns(env.getProperty("TerminalPOSExportDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalPOSExportDetailsHeaders() {
        String columns = dao.getColumns(env.getProperty("TerminalPOSExportDetailsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalEPOSExportDetailsColumns() {
        String columns = dao.getColumns(env.getProperty("TerminalEPOSExportDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalEPOSExportDetailsHeaders() {
        String columns = dao.getColumns(env.getProperty("TerminalEPOSExportDetailsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalATMExportDetailsColumns() {
        String columns = dao.getColumns(env.getProperty("TerminalATMExportDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String>  getTerminalATMExportDetailsHeaders() {
        String columns = dao.getColumns(env.getProperty("TerminalATMExportDetailsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getSettlementTransactionMainColumns() {
        String columns = dao.getColumns(env.getProperty("SettlementTransactionMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getSettlementTransactionDetailsColumns() {
        String columns = dao.getColumns(env.getProperty("SettlementTransactionDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getSettlementTransactionColumns() {
        String columns = dao.getColumns(env.getProperty("SettlementTransactionColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getSettlementTransactionHeaders() {
        String columns = dao.getColumns(env.getProperty("SettlementTransactionHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getSettlementTransactionColumnsWithDetails() {
        String columns = dao.getColumns(env.getProperty("SettlementTransactionColumnsWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getSettlementTransactionHeadersWithDetails() {
        String columns = dao.getColumns(env.getProperty("SettlementTransactionHeadersWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getIssueApplicationDraftHeaders() {
        String columns = dao.getColumns(env.getProperty("IssueApplicationDraftHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getIssueApplicationDraftColumns() {
        String columns = dao.getColumns(env.getProperty("IssueApplicationDraftColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getIssueApplicationSubmitHeaders() {
        String columns = dao.getColumns(env.getProperty("IssueApplicationSubmitHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getIssueApplicationSubmitColumns() {
        String columns = dao.getColumns(env.getProperty("IssueApplicationSubmitColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getIssueApplicationArchivedHeaders() {
        String columns = dao.getColumns(env.getProperty("IssueApplicationArchivedHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getIssueApplicationArchivedColumns() {
        String columns = dao.getColumns(env.getProperty("IssueApplicationArchivedColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getCustomerMainColumns(){
        String columns = dao.getColumns(env.getProperty("CustomerMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getCustomerDetailsColumns(){
        String columns = dao.getColumns(env.getProperty("CustomerDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getCustomerColumns(){
        String columns = dao.getColumns(env.getProperty("CustomerColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getCustomerHeaders(){
        String columns = dao.getColumns(env.getProperty("CustomerHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getCustomerColumnsWithDetails(){
        String columns = dao.getColumns(env.getProperty("CustomerColumnsWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getCustomerHeadersWithDetails(){
        String columns = dao.getColumns(env.getProperty("CustomerHeadersWithDetails"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getMerchantMainColumns(){
        String columns = dao.getColumns(env.getProperty("MerchantMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getMerchantMainExportColumns(){
        String columns = dao.getColumns(env.getProperty("MerchantExportMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getMerchantColumnsWithDetails(){
        String columns = dao.getColumns(env.getProperty("MerchantDetailColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getMerchantMainHeaders(){
        String columns = dao.getColumns(env.getProperty("MerchantMainHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getMerchantWithDetailsHeaders(){
        String columns = dao.getColumns(env.getProperty("MerchantWithDetailsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getMerchantExportDetailColumns(){
        String columns = dao.getColumns(env.getProperty("MerchantExportDetailColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getClientColumns(){
        String columns = dao.getColumns(env.getProperty("ClientColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getDeviceMainColumns(){
        String columns = dao.getColumns(env.getProperty("DeviceMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getDeviceMainExportColumns(){
        String columns = dao.getColumns(env.getProperty("DeviceMainExportColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getDeviceDetailsColumns(){
        String columns = dao.getColumns(env.getProperty("DeviceDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getDeviceMainHeaders(){
        String columns = dao.getColumns(env.getProperty("DeviceMainHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getDeviceWithDetailsHeaders(){
        String columns = dao.getColumns(env.getProperty("DeviceWithDetailsHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getDeviceWithDetailsColumns(){
        String columns = dao.getColumns(env.getProperty("DeviceWithDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getHostColumns(){
        String columns = dao.getColumns(env.getProperty("HostColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getSIMDetailsColumns(){
        String columns = dao.getColumns(env.getProperty("SIMDetailsColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getCapturedCardsMainColumns(){
        String columns = dao.getColumns(env.getProperty("CapturedCardsMainColumns"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }
    public List<String> getCapturedCardsMainExportHeaders(){
        String columns = dao.getColumns(env.getProperty("CapturedCardsMainExportHeaders"));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getByName(String type) {
        String columns = dao.getColumns(env.getProperty(type));
        List<String> list = new LinkedList<String>( Arrays.asList(columns.split(",")));
        return list;
    }

    public List<String> getCardInputMode() {
        String columns = dao.getColumns("CardInputMode");
        return Arrays.asList(columns.split(","));
    }

    public List<String> getCardHolderAuth() {
        String columns = dao.getColumns("CardHolderAuth");
        return Arrays.asList(columns.split(","));
    }
}
