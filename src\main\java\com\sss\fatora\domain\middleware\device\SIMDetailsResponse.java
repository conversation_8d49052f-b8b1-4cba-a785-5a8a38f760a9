package com.sss.fatora.domain.middleware.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sss.fatora.domain.middleware.response.MiddlewareResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class SIMDetailsResponse extends MiddlewareResponse {
    @JsonProperty("result")
    private Device result;
}
