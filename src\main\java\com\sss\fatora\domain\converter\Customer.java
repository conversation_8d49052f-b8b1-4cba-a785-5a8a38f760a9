package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "command","customer_number","customer_category","customer_relation","resident","nationality","contract","person","contact","address"})
public class Customer extends GenericConverter {
    @ExcelProperty(name = "id")
    private String id;
    @ExcelProperty(name = "ns1:command")
    private String command;
    @ExcelProperty(name = "ns1:customer_number")
    private String customer_number;
    @ExcelProperty(name = "ns1:customer_category")
    private String customer_category;
    @ExcelProperty(name = "ns1:customer_relation")
    private String customer_relation;
    @ExcelProperty(name = "ns1:resident")
    private String resident;
    @ExcelProperty(name = "ns1:nationality")
    private String nationality;
    private Contract contract;
    private Person person;
    private CustomerContact contact;
    private Address address;

    public String getId() {
        return id;
    }

    @XmlAttribute(name = "id")
    public void setId(String id) {
        this.id = id;
    }


}
