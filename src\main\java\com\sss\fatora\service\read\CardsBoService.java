package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.CardsBoDao;
import com.sss.fatora.domain.read.CardsBo;
import com.sss.fatora.domain.read.DTO.CardsBoDTO;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
@Service
@Transactional("readingDBTransactionManager")
public class CardsBoService extends GenericReadService<CardsBoDao, CardsBo, Long> {

    /**get Cards Bo By Card Id*/
    public CardsBo getCardBoByNumber(String cardId) {

        try {
            return dao.getCardBoByNumber(cardId);
        } catch (Exception exception) {
            throw exception;
        }
    }

    /**get list of Cards Bo By Customer Number*/
    public List <CardsBoDTO> getCardsByCustomerNumber(String customerNumber) {
        return dao.getCardsByCustomerNumber(customerNumber);
    }

    /**get Cards Bo By Customer Number*/
    public CardsBo checkCustomer(String customerNumber) {
        try {
            return dao.getCardsBoByCustomerNumber(customerNumber);
        } catch (Exception exception) {
            throw exception;
        }
    }

    public CardsBo checkPerson(String idSeries) {
        try {
            return dao.getCardsBoByCardHolderNumber(idSeries);
        } catch (Exception exception) {
            throw exception;
        }
    }
}
