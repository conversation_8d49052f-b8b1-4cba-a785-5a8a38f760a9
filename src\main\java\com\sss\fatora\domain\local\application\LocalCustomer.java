package com.sss.fatora.domain.local.application;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalCustomer {

    Long id;
    String customerNumber;
    String nationality;
    String branch;
    String mobile;
    String email;
    String country;
    String region;
    String city;
    String street;
    String house;
    String apartment;
    LocalPerson person;
}
