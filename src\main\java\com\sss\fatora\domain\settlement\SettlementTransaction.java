package com.sss.fatora.domain.settlement;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "Transactions_Them")
public class SettlementTransaction implements Loggable {

    @Id
    @Column(name = "Id")
    private Integer rowId;
    @Column(name = "Bank_of_Record")
    private String bankOfRecord;
    @Column(name = "RRN")
    private String rrn;
    @Column(name = "Card_Number")
    private String cardNumber;
    @Column(name = "Utrno")
    private String utrno;
    @Column(name = "Reversal")
    private Boolean reversalThem;
    @Column(name = "Matched")
    private Boolean matched;
    @Column(name = "Date")
    private Long date;
    @Column(name = "Time")
    private Long time;
    @Column(name = "Main_Terminal_Id")
    private String mainTerminal;
    @Column(name = "Terminal_Id")
    private String terminalId;
    @Column(name = "Terminal_Type")
    private String terminalType;
    @Column(name = "Channel")
    private String channel;
    @Column(name = "Merchant_Name")
    private String merchantName;
    @Column(name = "Merchant_Branch")
    private String merchantBranch;
    @Column(name = "Issuer_Bank")
    private String issuer;
    @Column(name = "Acquirer_Bank")
    private String acquirer;
    @Column(name = "Transaction_Type")
    private String transactionType;
    @Column(name = "Transaction_Description")
    private String transactionDescription;
    @Column(name = "Trace_Number")
    private String traceNumber;
    @Column(name = "Solved")
    private Boolean solved;
    @Column(name = "Amount")
    private String amount;
    @Column(name = "Closed")
    private Boolean closed;
    @Column(name = "Reconciliation_Date")
    private Date reconciliationDate;
    @Column(name = "Settlment_Date")
    private Date settlmentDate;
    @Transient
    @JsonIgnore
    String query;
    
    @Override
    @Transient
    public String fetchId() {
        if (this.getRrn() != null)
            return this.getRrn();
        return null;
    }

    @Override
    @Transient
    public String getRelatedEntity() {
        return "Transactions";
    }

    @Override
    public void setQuery(String query) {
        this.query = query;
    }

    @Override
    public String getQuery() {
        return this.query;
    }
}
