package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;
import java.util.Set;

@Entity
@Setter
@Getter

@NoArgsConstructor
@Table(name = "Privilege")
public class Privilege extends LocalDomain {

    public Privilege(Integer id) {
        this.id = id;
    }

    /*@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRIVILEGE_SEQ")
    @SequenceGenerator(sequenceName = "PRIVILEGE_SEQ",initialValue = 1, allocationSize = 1, name = "PRIVILEGE_SEQ")*/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "Name")
    @Nationalized
    private String name;
    @Column(name = "Arabic_Name")
    @Nationalized
    private String arabicName;
    @Column(name = "English_Name")
    @Nationalized
    private String englishName;
    @Column(name = "Action")
    @Nationalized
    private String action;
    @Column(name = "Reference_URL")
    @Nationalized
    private String referenceUrl;
    @Column(name = "Is_Menu_Item")
    private Boolean isMenuItem;

    @Column(name = "Icon")
    @Nationalized
    private String icon;

    @Column(name = "Type")
    @Nationalized
    private String type;

    @Column(name = "Privilege_Description")
    @Nationalized
    private String privilegeDescription;

    @Column(name = "Order_Item")
    private Integer orderItem;


    @OneToMany(mappedBy = "privilege")
    @JsonIgnoreProperties("privilege")
    private Set<ApplicationUserPrivilege> applicationUserPrivileges;

    @ManyToOne
    @JoinColumn(name = "Parent_Id", referencedColumnName = "id")
    @JsonIgnoreProperties("privileges")
    private Privilege parentPrivilege;

    @OneToMany(mappedBy = "parentPrivilege", cascade = CascadeType.ALL)
    @JsonIgnoreProperties("parentPrivilege")
    private Set<Privilege> privileges;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}
