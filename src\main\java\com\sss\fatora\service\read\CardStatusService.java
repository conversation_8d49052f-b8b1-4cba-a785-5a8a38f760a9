package com.sss.fatora.service.read;


import com.sss.fatora.dao.read.CardStatusDao;
import com.sss.fatora.domain.read.CardStatusVW;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("readingDBTransactionManager")
public class CardStatusService extends GenericReadService<CardStatusDao, CardStatusVW, Long> {


    /**
     * This Function Get All Card Status And Send Them To The Front-End
     * */
    public List<CardStatusVW> getCardsStatus() {
        try {
            return dao.findAll();
        } catch (Exception exception) {
            exception.printStackTrace();
            return null;
        }
    }
    /**
     * This Function Get Card Status Description By CdStat ( Like An Id )
     * */
    public String getByCdStat(Long cdStat) {
        try {
            return dao.getByCdStat(cdStat);
        } catch (Exception exception) {
            exception.printStackTrace();
            return null;
        }
    }

    /**
     * This Function Get All Active Card Status
     * */
    public List<CardStatusVW> getActiveChangeCardsStatus() {
        try {
            return dao.getActiveChangeStatus();
        } catch (Exception exception) {
            exception.printStackTrace();
            return null;
        }
    }

    /**
     * This Function Get All Active Card Status with valid card in logs
     * */
    public List<CardStatusVW> getActiveChangeCardsStatusWithValid() {
        try {
            return dao.getActiveChangeCardsStatusWithValid();
        } catch (Exception exception) {
            exception.printStackTrace();
            return null;
        }
    }

    public Integer getStatusByDesc(String desc) {
        return dao.getStatusByDesc(desc);
    }
}
