package com.sss.fatora.controller.local;

import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.RequestsLogDao;
import com.sss.fatora.domain.local.AcquiringActionRequest;
import com.sss.fatora.domain.local.AcquiringRequestsLog;
import com.sss.fatora.domain.local.RequestsLog;
import com.sss.fatora.domain.middleware.acquiring.dto.GetAcquiringRequestFilterDto;
import com.sss.fatora.domain.middleware.acquiring.dto.HandleAcquiringRequestDto;
import com.sss.fatora.service.local.RequestsLogService;
import com.sss.fatora.service.middleware.acquiring.AcquiringRequestService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.AcquiringActionType;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
@RestController
@RequestMapping("/acquiring-requests-log")

public class AcquiringRequestsLogController {

    private final AcquiringRequestService acquiringRequestService;

    public AcquiringRequestsLogController(AcquiringRequestService acquiringRequestService) {
        this.acquiringRequestService = acquiringRequestService;
    }
        @RequestMapping(value = "/by-request-acquiringId", method = RequestMethod.GET)
    public ResponseObject getAcquiringLogsByRequestId(@RequestParam(value = "id") Long id) {
        List<AcquiringRequestsLog> requestsAcquiringLogs = acquiringRequestService.getAcquiringLogsByRequestId(id);
        return requestsAcquiringLogs != null ?
                ResponseObject.FETCHED_SUCCESS(requestsAcquiringLogs, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}