package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class Account3 extends Account {

        private String id="account_3";
    private String command = "CMMDCRUP";
    @ExcelProperty(name = "account_3:account_number")
    private String account_number;
    @ExcelProperty(name = "account_3:currency")
    private String currency;
    @ExcelProperty(name = "account_3:account_type")
    private String account_type;
    private AccountObject account_object;

}
