package com.sss.fatora.dao.specification;

import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class SpecificationsBuilder {
    // private final List<SearchCriteria> params;
    private final List<String> joinColumns;
    private final Set<String> params;
    private final Class<?> tClass;

    public SpecificationsBuilder(Class<?> tClass) {
        joinColumns = new ArrayList<>();
        params = new HashSet<>();
        this.tClass = tClass;
    }

    /**
     * This Function Replace The Operators With String Clauses
     * convert the operators to its corresponds in SQL
     */
    public SpecificationsBuilder add(String attr, Object value, Operator operator) {
        if (operator == null) {
            if (value instanceof String) {
                value = ((String) value).toLowerCase();
                this.params.add("lower(" + attr + ") LIKE '%" + value + "%' ");
            } else if (value instanceof Integer || value instanceof Long) {
                this.params.add(attr + "=" + value + " ");
            } else if (value instanceof Boolean) {
                this.params.add(attr + " is " + value + " ");
            }
        } else {

            if (Operator.CONTAINS.getStringValue().equalsIgnoreCase(operator.getStringValue())) {
                value = ((String) value).toLowerCase();
                this.params.add("lower(" + attr + ") LIKE '%" + value + "%' ");
            } else if (Operator.CONTAINS_WITH_STAR.getStringValue().equalsIgnoreCase(operator.getStringValue())) {
                value = ((String) value).replace('*', '%');
                value = ((String) value).toLowerCase();
                this.params.add("lower(" + attr + ") LIKE '" + value + "' ");
            } else if (Operator.NOT_EQUALS_OR_NULL.getStringValue().equalsIgnoreCase((operator.getStringValue()))) {
                this.params.add("(" + attr + " <> " + value + " OR " + attr + " IS NULL)");
            } else if (Operator.EQUALS.getStringValue().equalsIgnoreCase((operator.getStringValue()))) {
                if (value instanceof String) {
                    value = ((String) value).toLowerCase();
                    this.params.add("lower(" + attr + ") LIKE '" + value + "' ");
                } else
                    this.params.add(attr + operator.getStringValue() + value + " ");
            } else
                this.params.add(attr + operator.getStringValue() + value + " ");
        }
        return this;
    }

    /**
     * This Function Add Constrains To The Query
     */
    public SpecificationsBuilder addConstraint(String additionalConstraint) {
        if (!("").equals(additionalConstraint) && additionalConstraint != null)
            this.params.add(additionalConstraint);
        return this;
    }

    /**
     * This Function Add Joins To The Query
     */
    public SpecificationsBuilder join(String joinColumn) {
        this.joinColumns.add(joinColumn);
        return this;
    }

    /**
     * This Function Build A Query With Distinct
     */
    public String build(Pagination pagination, List<String> projection) {
        StringBuilder query = new StringBuilder();

        String selectedObject = tClass.getSimpleName();
        if (projection != null)
            if (!projection.isEmpty())
                selectedObject = " new Map( " + String.join(",", projection) + ") ";

        query.append("SELECT DISTINCT " + selectedObject);
        query.append(" FROM " + tClass.getSimpleName() + " AS " + tClass.getSimpleName());
        if (joinColumns != null && !joinColumns.isEmpty()) {
            for (String joinColumn : joinColumns) {
                query.append(joinColumn);
            }
        }
        if (params != null && !params.isEmpty()) {
            query.append(" WHERE ");
            query.append(params.stream().collect(Collectors.joining(" AND ")));
        }
        if (pagination != null) {
            if (pagination.getOrderBy() != null && !pagination.getOrderBy().isEmpty()) {
                query.append(" ORDER BY " + pagination.getOrderBy());
                if (pagination.getOrderType() != null && !pagination.getOrderType().isEmpty()) {
                    query.append(" " + pagination.getOrderType());
                }
            }

        }
        return query.toString();
    }

    /**
     * This Function Build A Query Without Distinct
     */
    public String buildWithoutDistinct(Pagination pagination, List<String> projection) {
        StringBuilder query = new StringBuilder();

        String selectedObject = tClass.getSimpleName();
        if (projection != null)
            if (!projection.isEmpty())
                selectedObject = " new Map( " + String.join(",", projection) + ") ";

        query.append("SELECT " + selectedObject);
        query.append(" FROM " + tClass.getSimpleName() + " AS " + tClass.getSimpleName());
        if (joinColumns != null && !joinColumns.isEmpty()) {
            for (String joinColumn : joinColumns) {
                query.append(joinColumn);
            }
        }
        if (params != null && !params.isEmpty()) {
            query.append(" WHERE ");
            query.append(params.stream().collect(Collectors.joining(" AND ")));
        }
        if (pagination != null) {
            if (pagination.getOrderBy() != null && !pagination.getOrderBy().isEmpty()) {
                query.append(" ORDER BY " + pagination.getOrderBy());
                if (pagination.getOrderType() != null && !pagination.getOrderType().isEmpty()) {
                    query.append(" " + pagination.getOrderType());
                }
            }

        }
        return query.toString();
    }

    /**
     * This Function Build Count Query WithOut Distinct
     */
    public String buildCountWithoutDistinct() {
        StringBuilder query = new StringBuilder();
        query.append("SELECT count( " + tClass.getSimpleName() + ".id) ");
        query.append(" FROM " + tClass.getSimpleName() + " AS " + tClass.getSimpleName());
        if (joinColumns != null && !joinColumns.isEmpty()) {
            for (String joinColumn : joinColumns) {
                if (joinColumn.contains("Fetch"))
                    joinColumn = joinColumn.replace("Fetch", "");
                query.append(joinColumn);
            }
        }
        if (params != null && !params.isEmpty()) {
            query.append(" WHERE ");
            query.append(params.stream().collect(Collectors.joining(" AND ")));
        }
        return query.toString();
    }

    /**
     * This Function Build Count Query With Distinct
     */
    public String buildCount() {
        StringBuilder query = new StringBuilder();
        query.append("SELECT count( DISTINCT " + tClass.getSimpleName() + ".id) ");
        query.append(" FROM " + tClass.getSimpleName() + " AS " + tClass.getSimpleName());
        if (joinColumns != null && !joinColumns.isEmpty()) {
            for (String joinColumn : joinColumns) {
                if (joinColumn.contains("Fetch"))
                    joinColumn = joinColumn.replace("Fetch", "");
                query.append(joinColumn);
            }
        }
        if (params != null && !params.isEmpty()) {
            query.append(" WHERE ");
            query.append(params.stream().collect(Collectors.joining(" AND ")));
        }
        return query.toString();
    }
}
