package com.sss.fatora.domain.middleware.terminals;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class TerminalRequest {
    private String bank;
    private String customerNumber;
    private String terminalNumber;
    private String serialNumber;
    private String merchantName;
    private String merchantNumber;
    private String fromLastOnline;
    private String toLastOnline;
    private String fromLastReconciliation;
    private String toLastReconciliation;
    private String mcc;
    private String mccDesc;
    private String agentNumber;
    private String agentShortDesc;
    private String accountNumber;
    private String accountType;
    private String terminalGroup;
    private String country;
    private String region;
    private String street;
    private String house;
    private String feePercentage;
    private String ipAddress;
    private String localPort;
    private String luno;
    private String remoteAddress;
    private List<String> fields;

    private String sorting;
    private Integer maxResultCount;
    private Integer pid;
    private Integer skipCount;
}
