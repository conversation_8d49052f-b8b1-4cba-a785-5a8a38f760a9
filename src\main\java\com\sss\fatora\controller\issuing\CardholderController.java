package com.sss.fatora.controller.issuing;

import com.sss.fatora.domain.issuing.Cardholder;
import com.sss.fatora.service.issuing.CardholderService;
import com.sss.fatora.service.issuing.CustomerService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/cardholder")
public class CardholderController {
    @Autowired
    CardholderService cardholderService;

    @RequestMapping(method = RequestMethod.GET)
    public ResponseObject getCardHolderByCustomerNumber(@RequestParam(value = "customerId")Long customerId){
        List<Cardholder> requestedCardHolders = cardholderService.getCardHolderByCustomerNumber(customerId);
        return requestedCardHolders != null ?
                ResponseObject.FETCHED_SUCCESS(requestedCardHolders,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }
}
