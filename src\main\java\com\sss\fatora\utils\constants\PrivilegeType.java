package com.sss.fatora.utils.constants;

public enum PrivilegeType {
    CARD_COLUMN("CardColumn"),
    CUSTOMER_COLUMN("CustomerColumn"),
    TRANSACTION_COLUMN("TransactionColumn"),
    SETTLEMENT_TRANSACTION_COLUMN("SettlementTransactionColumn"),
    BANK_SMS_COLUMN("BankSMSColumn"),
    TERMINAL_ATM_COLUMN("TerminalATMColumn"),
    TERMINAL_EPOS_COLUMN("TerminalePOSColumn"),
    TERMINAL_POS_COLUMN("TerminalPOSColumn"),
    DEVICE_COLUMN("DeviceColumn"),
    MERCHANT_COLUMN("MerchantColumn"),
    CAPT_CARD_COLUMN("CaptCardColumn"),
    FNotifyColumns("FNotifyColumns"),
    LOG_PRIVILEGE("LogPrivilege"),
    ACTION("Action"),
    MENUITEM("MenuItem");
    private String type;

    PrivilegeType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
