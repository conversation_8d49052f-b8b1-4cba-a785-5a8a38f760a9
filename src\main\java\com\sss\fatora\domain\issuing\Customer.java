package com.sss.fatora.domain.issuing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_CUSTOMERS")
public class Customer implements Loggable {

    @Id
    @Column(name = "ID")
    private Long id;

    @Column(name = "CUSTOMER_NUMBER")
    private String customerNumber;

    @Column(name = "FIRST_NAME")
    private String customerFirstName;

    @Column(name = "SURNAME")
    private String customerLastName;

    @Column(name = "NATIONALITY")
    private String customerNationality;

    @Column(name = "PERSON_ID")
    private Long customerPersonId;

    @Column(name = "AGENT_NUMBER")
    private String customerBranch;

    @Column(name = "AGENT_SHORT_DESC")
    private String customerAgentDesc;

    @Column(name = "MOBILE_NUMBER")
    private String customerMobile;

    @Column(name = "EMAIL_ADDRESS")
    private String customerEmail;

    @Column(name = "COUNTRY")
    private String customerCountry;

    @Column(name = "REGION")
    private String customerRegion;

    @Column(name = "CITY")
    private String customerCity;

    @Column(name = "STREET")
    private String customerStreet;

    @Column(name = "HOUSE")
    private String customerHouse;

    @Column(name = "APARTMENT")
    private String customerApartment;

    @Column(name = "ID_TYPE")
    private String customerIdType;

    @Column(name = "ID_NUMBER")
    private String customerIdNumber;

    @Transient
    @JsonIgnore

    String query;
    @Override
    @Transient
    public String fetchId() {
        if (this.customerNumber != null)
            return this.getCustomerNumber();
        return null;
    }

    @Override
    @Transient
    public String getRelatedEntity() {

//        return this.getClass().getSimpleName();
        return "Customers";
    }

    @Override
    public void setQuery(String query) {
        this.query = query;
    }

    @Override
    public String getQuery() {
        return this.query;
    }

}





