package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.CardsBo;
import com.sss.fatora.domain.read.DTO.CardsBoDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.List;
public interface CardsBoDao extends GenericReadDao<CardsBo, Long> {

    @Query("SELECT C  FROM CardsBo C " +
            "WHERE " +
            "C.cardNumber =:cardNumber"
    )
    CardsBo getCardBoByNumber(@Param("cardNumber") String cardNumber);

    @Query("SELECT C  FROM CardsBo C " +
            "WHERE " +
            "C.customerNumber =:customerNumber"
    )
    CardsBo getCardsBoByCustomerNumber(@Param("customerNumber") String customerNumber);

    @Query("SELECT C  FROM CardsBo C " +
            "WHERE " +
            "C.cardHolderNumber =:idSeries"
    )
    CardsBo getCardsBoByCardHolderNumber(@Param("idSeries") String idSeries);

    @Query("SELECT new com.sss.fatora.domain.read.DTO.CardsBoDTO " +
            "(C.cardNumber, C.cardHolderName ,C.status , C.statusDesc , C.state ,C.stateDesc,C.issueDate,C.expiryDate) " +
            "FROM CardsBo C " +
            "WHERE " +
            "C.customerNumber =:customerNumber"
    )
    List <CardsBoDTO> getCardsByCustomerNumber(@Param("customerNumber") String customerNumber);
}
