package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_TRANS_TYPE")
public class TransType {
    @Id
    @Column(name = "TRANS_TYPE")
    Long transType;
    @Column(name = "DESCX")
    String descx;
    @Column(name = "IS_FINANCIAL")
    Integer isFinancial;
}
