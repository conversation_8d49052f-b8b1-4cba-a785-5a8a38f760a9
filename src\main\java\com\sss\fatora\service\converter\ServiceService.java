package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.Card;
import com.sss.fatora.domain.converter.Service;
import com.sss.fatora.domain.converter.ServiceObject;
import org.springframework.context.MessageSource;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@org.springframework.stereotype.Service
public class ServiceService extends GenericConverterService<Service> {


    public List<Service> prepareServiceObject(List<Account> accountList, Card card) {
        List<Service> serviceList = new ArrayList<>();
        for (Account account : accountList) {
            if (account != null) {
                Service service = new Service();
                service.setValue(********);
                ServiceObject serviceObject = new ServiceObject();
                serviceObject.setRef_id(account.getId());
                //    serviceObject.setStart_date(new Date().toString());
                service.setService_object(serviceObject);
                serviceList.add(service);
            }
        }
        Service service1 = new Service();
        ServiceObject serviceObject1 = new ServiceObject();
        service1.setValue(********);
        serviceObject1.setRef_id(card.getId());
        //  serviceObject1.setStart_date(new Date().toString());
        service1.setService_object(serviceObject1);

        Service service2 = new Service();
        ServiceObject serviceObject2 = new ServiceObject();
        service2.setValue(********);
        serviceObject2.setRef_id(card.getId());
        // serviceObject2.setStart_date(new Date().toString());
        service2.setService_object(serviceObject2);

        serviceList.add(service1);
        serviceList.add(service2);
        return serviceList;
    }

    public List<Service> prepareServiceFromPanel(List<Account> accountList, Card card) {
        List<Service> serviceList = new ArrayList<>();
        Service cardService1 = new Service(********, new ServiceObject(card.getId()));
        Service cardService2 = new Service(********, new ServiceObject(card.getId()));
        serviceList.add(cardService1);
        serviceList.add(cardService2);
        for (Account account : accountList) {
            Service accountService = new Service(********, new ServiceObject(account.getId()));
            serviceList.add(accountService);
        }
        return serviceList;
    }


    public List<Service> prepareServiceForAccount(List<Account> accountList, Card card) {
        List<Service> serviceList = new ArrayList<>();

        for (Account account : accountList) {
            Service accountService = new Service(********, new ServiceObject(account.getId()));
            serviceList.add(accountService);
        }
        return serviceList;
    }
}
