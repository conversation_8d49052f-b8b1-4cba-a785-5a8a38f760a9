package com.sss.fatora.domain.read.DTO.Payment;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class Description {
    @JsonProperty(value = "merchant_id")
    String merchantId;
    @JsonProperty(value = "transaction_type")
    String transactionType;
    @JsonProperty(value = "card_number")
    String cardNumber;
    @JsonProperty(value = "transaction_datetime")
    String transactionDatetime;

}
