package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.ApplicationUserPrivilege;
import com.sss.fatora.domain.local.Privilege;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;

public interface PrivilegeDao extends GenericDao<Privilege,Integer> {


    List<Privilege> getAllPrivilegesByRecordStatusOrderByOrderItem(@Param("recordStatus") int status);

    @Query("SELECT DISTINCT P FROM Privilege P " +
            "WHERE P.id not in :privilegeIds " +
            "order by P.orderItem ")
    List<Privilege> getPrivilegesByList(@Param("privilegeIds") List<Integer> privilegeIds);

    @Query("SELECT P FROM Privilege P " +
            "LEFT JOIN FETCH P.applicationUserPrivileges AUP " +
            "WHERE P.type=:type AND AUP.applicationUser.id=:id " +
            "order by P.orderItem ")
    List<Privilege> getPrivilegesById(@Param("type") String type,
                                      @Param("id") Integer id);

    @Query("SELECT DISTINCT P FROM Privilege P " +
            "LEFT JOIN FETCH ApplicationUserPrivilege AP ON AP.privilege.id = P.id " +
            "WHERE P.name =:privilegeName AND AP.applicationUser.id =:userId ")
    Privilege findByIdAndPrivilegeName(@Param("userId")Integer userId, @Param("privilegeName")String privilegeName);
}
