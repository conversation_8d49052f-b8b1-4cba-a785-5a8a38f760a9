package com.sss.fatora.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "readingDBEntityManagerFactory",
        transactionManagerRef = "readingDBTransactionManager",
        basePackages = "com.sss.fatora.dao.read"
)
public class ReadingDBConfig {

    @Autowired
    Environment env;

    @Bean(name = "readingDBDataSourceProperties")
    //@Primary
    @ConfigurationProperties("spring.datasource-reading-db")
    public DataSourceProperties readingDBDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "readingDBDataSource")
    // @Primary
    public DataSource readingDBDataSource() {
        DataSourceProperties readingDBDataSourceProperties = readingDBDataSourceProperties();
        return DataSourceBuilder.create()
                .driverClassName(readingDBDataSourceProperties.getDriverClassName())
                .url(readingDBDataSourceProperties.getUrl())
                .username(readingDBDataSourceProperties.getUsername())
                .password(readingDBDataSourceProperties.getPassword())
                .build();
    }

    //@Primary
    @Bean(name = "readingDBEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder readingDBEntityManagerFactoryBuilder) {

        Map<String, String> JpaProperties = new HashMap<>();

        JpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.properties.hibernate.format_sql"));
        JpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
        JpaProperties.put("hibernate.hbm2ddl.auto", "update");
        JpaProperties.put("hibernate.dialect", env.getProperty("spring.jpa.datasource-reading-db.hibernate.dialect"));
        if (env.getProperty("spring.datasource-reading-db.hibernate.schema") != null ||
                !"".equals(env.getProperty("spring.datasource-reading-db.hibernate.schema")))
            JpaProperties.put("hibernate.default_schema", env.getProperty("spring.datasource-reading-db.hibernate.schema"));

        return readingDBEntityManagerFactoryBuilder
                .dataSource(readingDBDataSource())
                .packages("com.sss.fatora.domain.read")
                //.persistenceUnit("secondaryDataSource")
                .properties(JpaProperties)
                .build();
    }

    // @Primary
    @Bean(name = "readingDBTransactionManager")
    public PlatformTransactionManager readingDBTransactionManager(
            @Qualifier("readingDBEntityManagerFactory") EntityManagerFactory readingDBEntityManagerFactory) {
        return new JpaTransactionManager(readingDBEntityManagerFactory);
    }

    @Bean("readingDBEntityManagerFactoryBuilder")
    public EntityManagerFactoryBuilder entityManagerFactoryBuilder() {
        return new EntityManagerFactoryBuilder(new HibernateJpaVendorAdapter(), new HashMap<>(), null);
    }
}

