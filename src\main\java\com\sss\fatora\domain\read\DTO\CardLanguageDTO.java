package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CardLanguageDTO {
    @JsonProperty(value = "Card_Number")
    String Card_Number;
    @JsonProperty(value = "Language")
    String Language;
    @JsonProperty(value = "Expiry_Date")
    String Expiry_Date;
    @JsonProperty(value = "Customer_Number")
    String Customer_Number;
}
