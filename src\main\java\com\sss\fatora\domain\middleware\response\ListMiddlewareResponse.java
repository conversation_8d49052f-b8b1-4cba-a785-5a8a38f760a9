package com.sss.fatora.domain.middleware.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class ListMiddlewareResponse<T> extends MiddlewareResponse {
    @JsonProperty("result")
    private List<T> result;
}
