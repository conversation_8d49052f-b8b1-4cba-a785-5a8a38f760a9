package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Card;
import com.sss.fatora.domain.converter.CardHolder;
import com.sss.fatora.domain.converter.Contact;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CardService extends GenericConverterService<Card> {
    @Autowired
    CardHolderServiceConverter cardHolderServiceConverter;

    public Card prepareCardObject(Card card, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(card, excelIndexes, row);
        card.setCardholder(cardHolderServiceConverter.prepareCardHolderObject(new CardHolder(), excelIndexes, row));
        return card;
    }

    public Card prepareCardObjectFromPanel(Card card, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(card, fixedValues);
        card.setCardholder(cardHolderServiceConverter.prepareCardHolderObjectFromPanel(card.getCardholder(), fixedValues));
        return card;
    }

    public Card prepareCardObjectForChangePrimaryAccount(Card card, Map<String, String> fixedValues) throws IllegalAccessException {
        card.setCommand("CMMDEXPR");
        getPropertyValueByObject(card, fixedValues);
        //   card.setCardholder(cardHolderService.prepareCardHolderObjectFromPanel(new CardHolder(), fixedValues));
        return card;
    }


    public Card prepareCardObjectForAccount(Card card, Map<String, String> fixedValues) throws IllegalAccessException {
        card.setCommand("CMMDEXPR");
        getPropertyValueByObject(card, fixedValues);
        return card;
    }

    public Card prepareCardObjectForChangeMobileNumber(Card card, Map<String, String> fixedValues, List<Contact> contactList) throws IllegalAccessException {
        card.setCommand("CMMDEXPR");
        getPropertyValueByObject(card, fixedValues);
        card.setCardholder(cardHolderServiceConverter.prepareCardHolderObjectForChangeMobileNumber(new CardHolder(), fixedValues, contactList));
        return card;
    }

}
