Unique_Column=Cannot insert duplicate
arabic_name.notBlank= Arabic name is mandatory
english_name.notBlank=English name is mandatory
not_found=requested resource not found
upload_null_file=File to be Uploaded can't be null
delete_with_correlations=You can't delete this user because he has correlations with other entities
account_exist = Account is already exist
sgb_account_longer_than_required=SGB Account Longer Than Required
customer_exist = Customer is already exist
unlink_primary_account = You can not unlink this account because it is a primary account
wsdl_service_off = We can't connect to WSDL Service
mobile_exist = Mobile number is already linked with card
org.springframework.dao.EmptyResultDataAccessException=Server Error
java.lang.NullPointerException=You send null property
