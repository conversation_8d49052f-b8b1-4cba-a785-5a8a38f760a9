package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Getter
@Setter
@XmlType(propOrder = { "command","contact_type","preferred_lang","contact_data"})
public class CustomerContact extends GenericConverter{

    @ExcelProperty(name = "ns1:command13")
    private String command;
    @ExcelProperty(name = "ns1:contact_type14")
    private String contact_type;
    @ExcelProperty(name = "ns1:preferred_lang15")
    private String preferred_lang;
    private CustomerContactData contact_data;
}
