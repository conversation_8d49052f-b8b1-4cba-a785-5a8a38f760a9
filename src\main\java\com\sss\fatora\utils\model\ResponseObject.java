package com.sss.fatora.utils.model;


import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.security.model.CustomUserDetails;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseObject<Domain> implements Serializable {
    public enum ReturnCode {
        INFO("Info"),
        SUCCESS("Success"),
        FAILED("Failed"),
        WARNING("Warning");

        private String returnCode;

        ReturnCode(String returnCode) {
            this.returnCode = returnCode;
        }

        public String getReturnCode() {
            return returnCode;
        }
    }


    public enum Text {
        BADCREDENTIALS("Bad Request, 'domainName' and 'password' and 'systemName' are required"),
        LOGGEDIN("Logged in successfully"),
        LOGINFAILED("Log in failed"),
        ADDEDSUCCESSFULY("addedSuccessfully"),
        ADDINGFAILED("addingFAILED"),
        UPDATEDSUCCESSFULY("updatedSuccessfully"),
        UPDATINGFAILED("updatingFAILED"),
        VALIDATIONFAILD("validation failed"),
        FETCHEDSUCCESSFULY("Fetched Successfully"),
        FETCHINGFAILED("Fetching failed"),
        DELETEDSUCCESSFULLY("Deleted Successfully"),
        DELETINGFAILED("Deleting failed"),
        DISPATCHEDSUCCESSFULLY("DISPATCHED SUCCESSFULLY"),
        DISPATCHEDFAILED("DISPATCHED FAILED"),
        LOCKEDSUCCESSFULY("Locked Successfully"),
        LOCKINGFAILED("There is a merge operation you can not update right now"),
        MERGINGFAILED("You can not merge right now try again"),
        IMAGEANDPDF("Image and Pdf"),
        ERROR("Submit completed with some errors, please review submitted cards");

        private String text;

        Text(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    private String text;
    private String returnCode;
    private List<Domain> list;
    private Domain model;
    private Map<String, Object> extraData;
    private List<String> errorList;

    public ResponseObject() {
        super();
        this.extraData = new HashMap<String, Object>();
    }

    private ResponseObject(Object responseToBereturned, Map<String, Object> extraData) {
        List list = null;
        Object model = null;
        if (responseToBereturned != null && responseToBereturned instanceof List)
            list = (List) responseToBereturned;
        else
            model = responseToBereturned;
        this.setList(list);
        this.setModel((Domain) model);
        this.setExtraData(extraData);

    }

    public static ResponseObject ADDED_SUCCESS(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.ADDEDSUCCESSFULY.getText());
        responseObject.setReturnCode(ReturnCode.SUCCESS.getReturnCode());
        return responseObject;
    }

    public static ResponseObject LOGGED_IN(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.LOGGEDIN.getText());
        responseObject.setReturnCode(ReturnCode.SUCCESS.getReturnCode());
        return responseObject;
    }

    public static ResponseObject LOGIN_FAILED(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.LOGINFAILED.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        return responseObject;
    }

    public static ResponseObject ADDING_FAILED(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.ADDINGFAILED.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        return responseObject;
    }


    public static ResponseObject UPDATED_SUCCESS(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.UPDATEDSUCCESSFULY.getText());
        responseObject.setReturnCode(ReturnCode.SUCCESS.getReturnCode());
        return responseObject;
    }

    public static ResponseObject UPDATING_FAILED(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.UPDATINGFAILED.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        return responseObject;
    }

    public static ResponseObject VALIDATION_FAILED(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.VALIDATIONFAILD.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        return responseObject;
    }

    public static ResponseObject DELETED_SUCCESS(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.DELETEDSUCCESSFULLY.getText());
        responseObject.setReturnCode(ReturnCode.SUCCESS.getReturnCode());
        return responseObject;
    }

    public static ResponseObject DELETING_FAILED(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.DELETINGFAILED.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        return responseObject;
    }

    public static ResponseObject DISPATCHED_SUCCESS(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.DISPATCHEDSUCCESSFULLY.getText());
        responseObject.setReturnCode(ReturnCode.SUCCESS.getReturnCode());
        return responseObject;
    }

    public static ResponseObject DISPATCHING_FAILED(Object response, Map<String, Object> extraData) {
        ResponseObject responseObject = new ResponseObject(response, extraData);
        responseObject.setText(Text.DISPATCHEDFAILED.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        return responseObject;
    }

    public static ResponseObject FETCHED_SUCCESS(Object response, Map<String, Object> extraData){
        ResponseObject responseObject = new ResponseObject(response,extraData);
        responseObject.setText(Text.FETCHEDSUCCESSFULY.getText());
        responseObject.setReturnCode(ReturnCode.SUCCESS.getReturnCode());
        return responseObject;
    }

    public static ResponseObject FETCHING_FAILED(Object response, Map<String, Object> extraData){
        ResponseObject responseObject = new ResponseObject(response,extraData);
        responseObject.setText(Text.FETCHINGFAILED.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        return responseObject;
    }

    public static ResponseObject RESPONSE_WITH_ERRORS(){
        ResponseObject responseObject = new ResponseObject();
        responseObject.setText(Text.ERROR.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        responseObject.setErrorList(Objects.requireNonNull(CustomUserDetails.getCurrentInstance()).getErrorsList());
        return responseObject;
    }
    public static ResponseObject FETCHED_SUCCESS_WITH_ERRORS(Object response, Map<String, Object> extraData){
        ResponseObject responseObject = new ResponseObject(response,extraData);
        responseObject.setText(Text.ERROR.getText());
        responseObject.setReturnCode(ReturnCode.FAILED.getReturnCode());
        responseObject.setErrorList(Objects.requireNonNull(CustomUserDetails.getCurrentInstance()).getErrorsList());
        return responseObject;
    }
    public ResponseObject(String text, ReturnCode returnCode) {
        this.text = text;
        this.returnCode = returnCode.getReturnCode();
        this.extraData = new HashMap<String, Object>();
    }

    public ResponseObject(Text text, ReturnCode returnCode) {
        this.text = text.getText();
        this.returnCode = returnCode.getReturnCode();
        this.extraData = new HashMap<String, Object>();
    }

    public ResponseObject(Text text, ReturnCode returnCode, List<Domain> list, Domain model) {
        this.text = text.getText();
        this.returnCode = returnCode.getReturnCode();
        this.list = list;
        this.model = model;
        this.extraData = new HashMap<String, Object>();
    }

    // print as json
    @Override
    public String toString() {
        return
                "\"message\" : {" +
                        "\"text\":\"" + text + '\"' +
                        ", \"returnCode\":\"" + returnCode + '\"' +
                        "} ";

    }


    public void addExtraData(String key, Object value) {
        if (value != null)
            this.extraData.put(key, value);
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    @JsonIgnoreProperties
    public List<Domain> getList() {
        return list;
    }

    public void setList(List<Domain> list) {
        this.list = list;
    }

    public Domain getModel() {
        return model;
    }

    public void setModel(Domain model) {
        this.model = model;
    }


    public Map<String, Object> getExtraData() {
        return extraData;
    }

    public void setExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
    }

    public List<String> getErrorList() {
        return errorList;
    }

    public void setErrorList(List<String> errorList) {
        this.errorList = errorList;
    }
}
