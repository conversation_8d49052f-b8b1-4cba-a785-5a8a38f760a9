package com.sss.fatora.service.middleware.acquiring;

import com.sss.fatora.dao.local.AcquiringRequestDao;
import com.sss.fatora.dao.local.ActionRequestDao;
import com.sss.fatora.domain.local.AcquiringActionRequest;
import com.sss.fatora.domain.local.AcquiringRequestsLog;
import com.sss.fatora.domain.local.ActionRequest;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.RequestsLog;
import com.sss.fatora.domain.middleware.acquiring.dto.GetAcquiringRequestFilterDto;
import com.sss.fatora.domain.middleware.acquiring.dto.HandleAcquiringRequestDto;
import com.sss.fatora.exception.ResourceNotFoundException;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.service.local.ApplicationUserService;
import com.sss.fatora.utils.model.Pagination;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.sss.fatora.utils.constants.*;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
//@Transactional("localDBTransactionManager")
public class AcquiringRequestService extends GenericService<AcquiringRequestDao, AcquiringActionRequest, Long>{
    //@Qualifier(value = "localDBTransactionManager")
    private final AcquiringRequestDao acquiringRequestDao;
    
    // AcquiringRequestsLog acquiringRequestsLog = new AcquiringRequestsLog();
        @Autowired
    ApplicationUserService applicationUserService;

    public AcquiringRequestService( AcquiringRequestDao acquiringRequestDao) {
        this.acquiringRequestDao = acquiringRequestDao;
    }
        /**
     * @param actionRequest   This Parameter Is For The Value Of Filters Send From
     *                        The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To
     *                        Search In
     * @param filterOperator  There Is An Specific Operator For Every Filter
     *                        {@link Operator}
     * @param fullname        This Parameter Is Added To Where Clause
     *                        <p>
     *                        This Function Add Certain Conditions To Where Clause
     *                        That Can't Be Added To The Filter (ActionRequest)
     *                        Directly And Then Request The Dynamic Search Function
     *                        ( dynamicSearch() )
     */

 public Page<AcquiringActionRequest> search(AcquiringActionRequest acquiringActionRequest, Long fromDate, Long toDate, Long fromModifiedDate,
            Long toModifiedDate,
            Long fromApprovalDate, Long toApprovalDate, Pagination pagination, Map<String, Operator> filterOperator,
            Integer userId)
            throws NoSuchFieldException, IllegalAccessException, IntrospectionException, InvocationTargetException {
        Page<AcquiringActionRequest> actionRequests;
        String additionalConstraint = "";
        String conditionForJoin = "";
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();

        if (acquiringActionRequest == null)
            acquiringActionRequest = new AcquiringActionRequest();

        if (fromApprovalDate != null || toApprovalDate != null) {
            if (fromApprovalDate == null)
                fromApprovalDate = new Date(1612178075113L).getTime();
            if (toApprovalDate == null)
                toApprovalDate = new Date(7258118400L * 1000).getTime();
            additionalConstraint = "AcquiringActionRequest.approvalDate between '" + f.format(fromApprovalDate) + "' AND '"
                    + f.format(toApprovalDate) + "'";
        }
        if (fromModifiedDate != null || toModifiedDate != null) {
            if (fromModifiedDate == null)
                fromModifiedDate = new Date(1612178075113L).getTime();
            if (toModifiedDate == null)
                toModifiedDate = new Date(7258118400L * 1000).getTime();
            additionalConstraint = "AcquiringActionRequest.modifiedDate between '" + f.format(fromModifiedDate) + "' AND '"
                    + f.format(toModifiedDate) + "'";
        }
        if (fromModifiedDate == null && toModifiedDate == null && fromApprovalDate == null && toApprovalDate == null) {
            if (fromDate == null) {
                fromDate = new Date(1612178075113L).getTime();
            }
            if (toDate == null) {
                toDate = new Date(7258118400L * 1000).getTime();
            }
            additionalConstraint = "AcquiringActionRequest.requestDate between '" + f.format(fromDate) + "' AND '"
                    + f.format(toDate) + "'";
        } else if (fromDate != null || toDate != null) {
            if (fromDate == null) {
                fromDate = new Date(1612178075113L).getTime();
            }
            if (toDate == null) {
                toDate = new Date(7258118400L * 1000).getTime();
            }
            additionalConstraint += " And AcquiringActionRequest.requestDate between '" + f.format(fromDate) + "' AND '"
                    + f.format(toDate) + "'";
        }
        // if (CustomUserDetails.getCurrentInstance().getApplicationUser().getBank() !=
        // null) {
        // actionRequest.setProductNumber(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank().getCode()
        // + "*");
        // filterOperator.put("productNumber", Operator.CONTAINS_WITH_STAR);
        // }

        if (userId != null) {
            conditionForJoin = " AND ApplicationUser.id = '" + userId + "' ";
            additionalConstraint += conditionForJoin;
        }

        if (acquiringActionRequest.getUserRequestStatus() != null
                && acquiringActionRequest.getUserRequestStatus().equalsIgnoreCase("Processed")) {
            additionalConstraint += " AND (AcquiringActionRequest.userRequestStatus IN ('Approved','Rejected'))";
            acquiringActionRequest.setUserRequestStatus(null);
        }
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("AcquiringActionRequest.creationDate");
            pagination.setOrderType("DESC");
        }

        // if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
        //     List<Integer> users = applicationUserService.getAllUsersByBankId(applicationUser.getBank().getId())
        //             .stream()
        //             .map(ApplicationUser::getId)
        //             .collect(Collectors.toList());
        //     additionalConstraint += " AND ( AcquiringActionRequest.applicationUser IN "
        //             + users.toString().replace("[", "(").replace("]", ")") +
        //             " OR " + getRequestsByUserBankBins(applicationUser) + " ) ";
        // }

        actionRequests = this.dynamicSearch(acquiringActionRequest, pagination, additionalConstraint, filterOperator, null,true);

        return actionRequests;

    }

    public void handleRequest(HandleAcquiringRequestDto handleAcquiringRequestDto) {
        Optional<AcquiringActionRequest> requestObject = acquiringRequestDao.findById(handleAcquiringRequestDto.getId());

        if (!requestObject.isPresent()) {
            throw new ResourceNotFoundException();
        }

        AcquiringActionRequest acquiringRequestEntity = requestObject.get();

        if (handleAcquiringRequestDto.getApproved()) {
            acquiringRequestEntity.setRequestStatus("Approve");
            acquiringRequestEntity.setApprovalDate(new Date());
            acquiringRequestDao.save(acquiringRequestEntity);
        } else {
            acquiringRequestEntity.setRequestStatus("Reject");
            acquiringRequestEntity.setDescription(handleAcquiringRequestDto.getDescription());
            acquiringRequestDao.save(acquiringRequestEntity);
        }
    }
        public List<AcquiringRequestsLog> getAcquiringLogsByRequestId(Long id) {
            return dao.getAcquiringLogsByRequestId(id);
        }

}
