package com.sss.fatora.exception;


import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.*;
import java.util.stream.Collectors;

@ControllerAdvice
public class ControllerAdvisor extends ResponseEntityExceptionHandler {
    private final MessageSource messageSource;
    Map<String, Object> extra = new LinkedHashMap<>();
    Map<String, String> constraints = new HashMap<>();

    public ControllerAdvisor(MessageSource messageSource) {
        constraints.put("Unique_Column", "Unique_Column");
        constraints.put("Unique_Section_In_Year", "Unique_Column");
        constraints.put("Unique_Sub_Section_In_section", "Unique_Column");
        constraints.put("Unique_Unit_In_Subject", "Unique_Column");
        constraints.put("Unique_Subject_In_section", "Unique_Column");
        constraints.put("Unique_Study_Year_In_Year", "Unique_Column");
        this.messageSource = messageSource;
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResponseObject> handleException(
            Exception ex, Locale locale)  {
            ex.printStackTrace();
            extra.put("message",ex.getMessage());
            return new ResponseEntity<>(ResponseObject.FETCHING_FAILED(null, extra), HttpStatus.OK);

    }

    //@ExceptionHandler(MethodArgumentNotValidException.class)
//    @Override
//    public ResponseEntity<ResponseObject> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex,
//                                                                                HttpHeaders headers,
//                                                                                HttpStatus status,
//                                                                                WebRequest request) {
//        //BindingResult bindingResult = ex.getBindingResult();
//        ex.printStackTrace();
//        // You can now process bindingResult to get information about validation errors
//
//        // For simplicity, let's just return a response with a 400 Bad Request status and error details
//        //ResponseObject errorResponse = new ValidationErrorResponse("Validation failed", bindingResult.getAllErrors());
//        return new ResponseEntity<>(ResponseObject.UPDATING_FAILED(null,null), HttpStatus.BAD_REQUEST);
//    }


    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        BindingResult bindingResult = ex.getBindingResult();
        return new ResponseEntity<>(ResponseObject.VALIDATION_FAILED(bindingResult.getAllErrors().get(0).getDefaultMessage(),null), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ResponseObject> handleNotFoundResourceException(
            ResourceNotFoundException ex, WebRequest request, Locale locale) {
        extra.put("message", messageSource.getMessage(ex.getMessage(), null, locale));
        return new ResponseEntity<>(ResponseObject.FETCHING_FAILED( null, extra), HttpStatus.OK);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ResponseObject> handleConstraintViolationException(
            ConstraintViolationException ex, WebRequest request, Locale locale) {
        Set<ConstraintViolation<?>> constraintViolations = ex.getConstraintViolations();
        Set<String> errorMessages = new HashSet<>(constraintViolations.size());
        errorMessages.addAll(constraintViolations.stream()
                .map(constraintViolation -> messageSource.getMessage(constraintViolation.getMessage(), null, locale))
                .collect(Collectors.toList()));
        extra.put("message", errorMessages);
        return new ResponseEntity<>(ResponseObject.FETCHING_FAILED( null, extra), HttpStatus.OK);
    }

    @ExceptionHandler(HttpServerErrorException.class)
    public ResponseEntity<ResponseObject> handleHttpServerErrorException(
            HttpServerErrorException ex, Locale locale, WebRequest request) {
        extra.put("message", messageSource.getMessage(ex.getMessage(), null, locale));
        return new ResponseEntity<>(ResponseObject.FETCHING_FAILED( null, extra), HttpStatus.OK);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ResponseObject> handleAccessDenied(
            HttpServerErrorException ex, Locale locale, WebRequest request) {
        extra.put("message", "Access Denied");
        return new ResponseEntity<>(ResponseObject.FETCHING_FAILED( null, extra), HttpStatus.UNAUTHORIZED);
    }




    /*@ExceptionHandler(SqlExceptionHelper.class)
    public ResponseEntity<ResponseObject> handleDataIntegrityViolationException(
            DataIntegrityViolationException ex, WebRequest request, Locale locale) {

        Throwable rootMsg = ex.getRootCause();
        if (rootMsg != null) {
            String lowerCaseMsg = rootMsg.getMessage();
            for (Map.Entry<String, String> entry : constraints.entrySet()) {
                if (lowerCaseMsg.contains(entry.getKey())) {
                    extra.put("message", messageSource.getMessage(entry.getValue(), null, locale));
                }
            }
        }
        return new ResponseEntity<>(ResponseObject.FETCHING_FAILED( null, extra), HttpStatus.OK);
    }*/


}
