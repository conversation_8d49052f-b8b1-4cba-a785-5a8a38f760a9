package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.Config;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ConfigDao extends GenericDao<Config, Long> {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Query("SELECT C.value FROM Config C " +
            "WHERE C.name=:configName")
    String getColumns(@Param("configName")String configName);
}
