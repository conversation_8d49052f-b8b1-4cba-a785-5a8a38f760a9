package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.CardDataVW;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface ReadCardDao extends GenericReadDao<CardDataVW, Long> {

    @Query("SELECT DISTINCT C FROM CardDataVW C " +
            "WHERE " +
            "( C.crefNo LIKE %:#{#card.crefNo}% ) " +
            "AND ( C.agentCode LIKE %:#{#card.agentCode}% ) " +
            "AND ( C.status = :#{#card.status} ) " +
            "AND ( C.crefNo LIKE %:#{#card.crefNo}% ) AND (substring(C.crefNo,0,6) IN :bins) " +
            "AND ( C.city LIKE %:#{#card.city}% ) " +
            "AND ( C.name LIKE %:#{#card.name}% ) " +
            "AND ( C.embosName LIKE %:#{#card.embosName}% ) " +
            "AND ( C.surname LIKE %:#{#card.surname}% ) " +
            "AND ( C.firstAddress LIKE %:#{#card.firstAddress}%) " +
            "AND ( C.secondAddress LIKE %:#{#card.secondAddress}% ) " +
            "AND ( C.mobilePhone LIKE %:#{#card.mobilePhone}% ) " +
            "AND ( C.pinCnt = :#{#card.pinCnt} ) " +
            "AND ( C.issueDate BETWEEN :fromIssueDate AND :toIssueDate ) " +
            "AND ( C.expDate BETWEEN :fromExpDate AND :toExpDate ) ")
    Page<CardDataVW> search(@Param("card") CardDataVW card,
                            @Param("bins") List<String> bins,
                            @Param("fromIssueDate") Date fromIssueDate,
                            @Param("toIssueDate") Date toIssueDate,
                            @Param("fromExpDate") Date fromExpDate,
                            @Param("toExpDate") Date toExpDate,
                            Pageable pagination);


    @Query("SELECT DISTINCT C FROM CardDataVW C " +
            "WHERE " +
            "( C.crefNo LIKE %:#{#card.crefNo}% ) " +
            "AND ( C.agentCode LIKE %:#{#card.agentCode}% ) " +
            /*"AND ( C.status = :#{#card.status} ) " +*/
            "AND ( C.crefNo LIKE %:#{#card.crefNo}% ) AND (substring(C.crefNo,0,6) IN :bins) " +
            "AND ( C.city LIKE %:#{#card.city}% ) " +
            "AND ( C.name LIKE %:#{#card.name}% ) " +
            "AND ( C.embosName LIKE %:#{#card.embosName}% ) " +
            "AND ( C.surname LIKE %:#{#card.surname}% ) " +
            "AND ( C.firstAddress LIKE %:#{#card.firstAddress}%) " +
            "AND ( C.secondAddress LIKE %:#{#card.secondAddress}% ) " +
            "AND ( C.mobilePhone LIKE %:#{#card.mobilePhone}% ) "
            /*"AND ( C.pinCnt = :#{#card.pinCnt} ) "*/
    )
    Page<CardDataVW> searchWithoutDate(CardDataVW card, List<String> bins, Pageable pagination);

    @Query("SELECT DISTINCT C FROM CardDataVW C " +
            "WHERE " +
            "( C.crefNo LIKE %:#{#card.crefNo}% ) " +
            "AND ( C.agentCode LIKE %:#{#card.agentCode}% ) " +
            /*"AND ( C.status = :#{#card.status} ) " +*/
            "AND ( C.crefNo LIKE %:#{#card.crefNo}% ) " +
            "AND ( C.city LIKE %:#{#card.city}% ) " +
            "AND ( C.name LIKE %:#{#card.name}% ) " +
            "AND ( C.embosName LIKE %:#{#card.embosName}% ) " +
            "AND ( C.surname LIKE %:#{#card.surname}% ) " +
            "AND ( C.firstAddress LIKE %:#{#card.firstAddress}%) " +
            "AND ( C.secondAddress LIKE %:#{#card.secondAddress}% ) " +
            "AND ( C.mobilePhone LIKE %:#{#card.mobilePhone}% ) "
            /*"AND ( C.pinCnt = :#{#card.pinCnt} ) "*/
    )
    Page<CardDataVW> searchByInternalUser(CardDataVW card, Pageable pagination);


    @Query("SELECT C  FROM CardDataVW C " +
            "WHERE " +
            "C.crefNo =:cardNo"
    )
    CardDataVW getCardByNumber(@Param("cardNo") String cardNo);


}
