package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.AccountLimit;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AccountLimitDao extends GenericReadDao<AccountLimit,String>{

    @Query("select AL from AccountLimit AL " +
           "where AL.accountNumber like :accountNumber ")
    List<AccountLimit> getByAccountNumber(@Param("accountNumber")String accountNumber);
}
