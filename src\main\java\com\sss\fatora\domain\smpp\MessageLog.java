package com.sss.fatora.domain.smpp;
/**
* Not Used Any More
*
* */
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.GenericDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "Message_Log")
public class MessageLog extends GenericDomain implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", unique = true, nullable = false)
    private Long id;
    @Column(name = "Card_No")
    private String cardNo;
    @Column(name = "Message_Id", length = 500)
    private String msgId;

    @Column(name = "To_Address", length = 500)
    private String to;

    @Column(name = "Message", length = 1000)
    private String message;

    @Column(name = "Received_Date_To_SMPP")
    private Date receivedDateToSMPP;

    @Column(name = "Send_Date_To_MTN")
    private Date sendDateToMTN;

    @Column(name = "SMS_Message", length = 1000)
    private String smsMessage;

    @Column(name = "Received_DateFrom_MTN")
    private Date receivedDateFromMTN;

    @Column(name = "SMS_Service_Response", length = 1000)
    private String serviceResponse;

    @Column(name = "Success")
    private Boolean success;
    @Column(name = "Type")
    private String type;

    public MessageLog(String msgId, String to, String message) {
        this.msgId = msgId;
        this.to = to;
        this.message = message;
        this.receivedDateToSMPP = new Date();
        this.success = false;
    }

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}

