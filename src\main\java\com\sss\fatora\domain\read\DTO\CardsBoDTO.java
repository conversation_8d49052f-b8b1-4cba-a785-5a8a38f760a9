package com.sss.fatora.domain.read.DTO;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CardsBoDTO {

    String cardNumber;
    String cardHolderName;
    String status;
    String statusDesc;

    String state;

    String stateDesc;

    Date issueDate;
    Date expiryDate;
}
