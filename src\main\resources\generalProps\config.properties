MaxExportSize=MaxExportSize
CardHeaders=CardHeaders
CardColumns=CardColumns
TransactionHeaders=TransactionHeaders
TransactionColumns=TransactionColumns
ClientColumns=ClientColumns
DeviceMainColumns=DeviceMainColumns
DeviceMainExportColumns=DeviceMainExportColumns
DeviceDetailsColumns=DeviceDetailsColumns
DeviceMainHeaders=DeviceMainHeaders
DeviceWithDetailsHeaders=DeviceWithDetailsHeaders
DeviceWithDetailsColumns=DeviceWithDetailsColumns
HostColumns=HostColumns
SIMDetailsColumns=SIMDetailsColumns
CapturedCardsMainColumns=CapturedCardsMainColumns
CapturedCardsMainExportHeaders=CapturedCardHeaders
LogsHeaders=LogsHeaders
LogsColumns=LogsColumns
LogSearchColumns=LogSearchColumns
LogSearchHeaders=LogSearchHeaders
LogDetailsColumns=LogDetailsColumns
LogDetailsHeaders=LogDetailsHeaders
LogActionColumns=LogActionColumns
LogActionHeaders=LogActionHeaders
LogExportColumns=LogExportColumns
LogExportHeaders=LogExportHeaders
LogRequestsHeaders=LogRequestsHeaders
LogRequestsColumns=LogRequestsColumns
ApplicationDraftHeaders=ApplicationDraftHeaders
ApplicationDraftColumns=ApplicationDraftColumns
ApplicationSubmitHeaders=ApplicationSubmitHeaders
ApplicationSubmitColumns=ApplicationSubmitColumns
ApplicationArchivedHeaders=ApplicationArchivedHeaders
ApplicationArchivedColumns=ApplicationArchivedColumns
TransactionMainColumns=TransactionMainColumns
TransactionDetailsColumns=TransactionDetailsColumns
CardMainColumns=CardMainColumns
CardDetailsColumns=CardDetailsColumns
CustomerMainColumns=CustomerMainColumns
CustomerDetailsColumns=CustomerDetailsColumns
CustomerColumns=CustomerColumns
CustomerHeaders=CustomerHeaders
CustomerColumnsWithDetails=CustomerColumnsWithDetails
CustomerHeadersWithDetails=CustomerHeadersWithDetails
BankSMSColumns=BankSMSColumns
FNotifyColumns=FNotifyColumns
TerminalMainColumns=TerminalMainColumns
TerminalATMMainColumns=TerminalATMMainColumns
TerminalDetailsColumns=TerminalDetailsColumns
TerminalExportMainColumns=TerminalExportMainColumns
TerminalExportMainHeaders=TerminalExportMainHeaders
TerminalPOSExportDetailsColumns=TerminalPOSExportDetailsColumns
TerminalPOSExportDetailsHeaders=TerminalPOSExportDetailsHeaders
TerminalEPOSExportDetailsColumns=TerminalEPOSExportDetailsColumns
TerminalEPOSExportDetailsHeaders=TerminalEPOSExportDetailsHeaders
TerminalATMExportDetailsColumns=TerminalATMExportDetailsColumns
TerminalATMExportDetailsHeaders=TerminalATMExportDetailsHeaders
MerchantMainColumns=MerchantMainColumns
MerchantExportMainColumns=MerchantExportMainColumns
MerchantDetailColumns=MerchantDetailColumns
MerchantMainHeaders=MerchantMainHeaders
MerchantWithDetailsHeaders=MerchantWithDetailsHeaders
MerchantExportDetailColumns=MerchantExportDetailColumns
SettlementTransactionMainColumns=SettlementTransactionMainColumns
SettlementTransactionDetailsColumns=SettlementTransactionDetailsColumns
SettlementTransactionHeaders=SettlementTransactionHeaders
SettlementTransactionColumns=SettlementTransactionColumns
SettlementTransactionHeadersWithDetails=SettlementTransactionHeadersWithDetails
SettlementTransactionColumnsWithDetails=SettlementTransactionColumnsWithDetails
CardHeadersWithDetails=CardHeadersWithDetails
CardColumnsWithDetails=CardColumnsWithDetails
TransactionHeadersWithDetails=TransactionHeadersWithDetails
TransactionColumnsWithDetails=TransactionColumnsWithDetails
IssueApplicationDraftColumns=IssueApplicationDraftColumns
IssueApplicationDraftHeaders=IssueApplicationDraftHeaders
IssueApplicationSubmitColumns=IssueApplicationSubmitColumns
IssueApplicationSubmitHeaders=IssueApplicationSubmitHeaders
IssueApplicationArchivedColumns=IssueApplicationArchivedColumns
IssueApplicationArchivedHeaders=IssueApplicationArchivedHeaders
Drafts=Drafts
Chunk=Chunk
#Production Url For Xml Based Api
# svboWebServiceProd=http://10.187.0.116:7007/sv/ApplicationService
#UAT Url For Xml Based Api
# svboWebServiceUAT=http://10.187.0.136:7007/sv/ApplicationService
#UAT Url For Rest Based Api
FatoraCreateCardUrlUAT=http://10.187.0.99:7777/create_card
#Production Url For Rest Based Api
FatoraCreateCardUrlProd=http://10.187.0.119:7777/create_card
#Fatora Service for actions Basic Auth
BasicAuthPassword=F@t123456
BasicAuthUsername=fatora
#Fatora Service for F-Notify Basic Auth
BasicAuthPasswordFNotify=fatora123456
BasicAuthUsernameFNotify=fatora
#Fatora APIs
createCard=create_card
destroyCardApi=destroy_card
reissueCardApi=reissue_card
changeCardLanguage=change_lang
changeCardMobile=change_mobile
changeCardProduct=change_product
manageAccountsApi=manage_accounts
payment=payment
allMessages=message/get
messageType=message-type
#Terminals
GetPOSTerminalById=api/services/app/Terminal/GetPOSTerminalById
GetPOSTerminals=api/services/app/Terminal/GetPOSTerminals
GetEPOSTerminals=api/services/app/Terminal/GetEPOSTerminals
GetEPOSTerminalById=api/services/app/Terminal/GetEPOSTerminalById
GetATMs=api/services/app/Terminal/GetATMs
GetMCCList=api/services/app/Terminal/GetMCCList
#(send terminalNumber + bank + fields ) get customerNumber
GetTerminalCustomer=api/services/app/Terminal/GetTerminalCustomer
#Merchants
GetMerchants=api/services/app/Merchant/GetMerchants
GetTerminalsByMerchant=api/services/app/Terminal/GetTerminalsByMerchant
#Clients
GetClients=api/services/app/Client/GetClients
#Devices
GetPOSMachines=api/services/app/Device/GetPOSMachines
GetHosts=api/services/app/Device/GetHosts
GetSimById=api/services/app/Device/GetSimById
GetAllStatus=api/services/app/Device/GetAllStatus
GetAllBanks=api/services/app/Device/GetAllBanks
GetAllOwners=api/services/app/Device/GetAllOwners
GetAllCustodians=api/services/app/Device/GetAllCustodian
UpdateUrl=/api/services/app/Device/Change
AssignSIM=/api/services/app/Device/AssignSIM
#Sims
GetSims=/api/services/app/Sim/GetSims
#Captured Cards
GetCapturedCards=api/services/app/Card/GetCapturedCards
GetCapturedReasons=api/services/app/Card/GetCapturedReasons
#Accounts
GetCustomerAccounts=api/services/app/AcqCustomer/GetCustomerAccounts
GetCustomerByCustomerNumber=api/services/app/AcqCustomer/GetCustomer
GetCustomers=/api/services/app/AcqCustomer/GetCustomers

#WideImage
WhidthWideImage=135
HeightWideImage=72.5

#SquaredImage
WhidthSquaredImage=72.5
HeightSquaredImage=72.5