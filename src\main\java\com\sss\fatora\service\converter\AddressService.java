package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Address;
import com.sss.fatora.domain.converter.AddressName;
import com.sss.fatora.domain.converter.SecWord;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class AddressService extends GenericConverterService<Address> {
    @Autowired
    AddressNameService addressNameService;
    public Address prepareAddressObject(Address address, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(address,excelIndexes,row);
        address.setAddress_name(addressNameService.prepareAddressNameObject(new AddressName(),excelIndexes,row));
        return address;
    }

    public Address prepareAddressObjectFromPanel(Address address, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(address,fixedValues);
        address.setAddress_name(addressNameService.prepareAddressNameObjectFromPanel(address.getAddress_name(),fixedValues));
        return address;
    }
}
