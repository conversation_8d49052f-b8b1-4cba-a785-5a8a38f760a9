package com.sss.fatora.controller.issuing;

import com.sss.fatora.domain.issuing.Account;
import com.sss.fatora.service.issuing.AccountService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/user-account")
public class AccountController {

    @Autowired
    AccountService accountService;

    @RequestMapping(method = RequestMethod.GET)
    public ResponseObject getAccountByCustomer(@RequestParam(value = "customerId")Long customerId){
        List<Account> requestedAccounts = accountService.getAccountByCustomerId(customerId);
        return requestedAccounts != null ?
                ResponseObject.FETCHED_SUCCESS(requestedAccounts,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }

    @RequestMapping(method = RequestMethod.GET,value = "/by-number")
    public ResponseObject getAccountByCustomerNumber(@RequestParam(value = "customerNumber") String customerNumber,@RequestParam(value = "status",required = false)Integer status) {

        Map<String, Object> response = status==null? accountService.getAccountByCustomerNumber(customerNumber,0):accountService.getAccountByCustomerNumber(customerNumber,1);

        return response != null ?
                ResponseObject.FETCHED_SUCCESS(response, null) :
                ResponseObject.RESPONSE_WITH_ERRORS();
    }

}
