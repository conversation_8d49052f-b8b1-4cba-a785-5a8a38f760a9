package com.sss.fatora.domain.converter;

import lombok.Getter;
import lombok.Setter;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class ExcelFile {

    Map<String, Integer> excelIndexes = new HashMap<>();
    XSSFWorkbook workbook;
    HSSFWorkbook hssfWorkbook;
    Sheet sheet;
}
