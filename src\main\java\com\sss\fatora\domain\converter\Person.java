package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "command","person_name","identity_card"})
public class Person extends GenericConverter{
    @ExcelProperty(name = "ns1:command11")
    private String command;
    private PersonName person_name;
    private IdentityCard identity_card;
    
}
