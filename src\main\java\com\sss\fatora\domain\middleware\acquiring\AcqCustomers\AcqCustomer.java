package com.sss.fatora.domain.middleware.acquiring.AcqCustomers;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AcqCustomer {
    private String customerNumber;
    private String bank;
    private String customerName;
    private String agentNumber;
    private String agentName;

}
