package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.*;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class CardHolderServiceConverter extends GenericConverterService<CardHolder> {

    @Autowired
    ContactService contactService;

    @Autowired
    SecWordService secWordService;

    public CardHolder prepareCardHolderObject(CardHolder cardHolder, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(cardHolder, excelIndexes, row);
        List<Contact> contactList = new ArrayList<>();
        contactList.add(contactService.prepareContactObject(new Contact(), excelIndexes, row));
        cardHolder.setContact(contactList);
        cardHolder.setSec_word(secWordService.prepareSecWordObject(new SecWord(), excelIndexes, row));
        return cardHolder;
    }

    public CardHolder prepareCardHolderObjectFromPanel(CardHolder cardHolder, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(cardHolder, fixedValues);
        List<Contact> contactList = new ArrayList<>();
        contactList.add(contactService.prepareContactObjectFromPanel(cardHolder.getContact().get(0), fixedValues));
        cardHolder.setContact(contactList);
        //cardHolder.setContact(contactService.prepareContactObjectFromPanel(cardHolder.getContact(), fixedValues));
        cardHolder.setSec_word(secWordService.prepareSecWordObjectFromPanel(cardHolder.getSec_word(), fixedValues));
        return cardHolder;
    }

    public CardHolder prepareCardHolderObjectForChangeMobileNumber(CardHolder cardHolder, Map<String, String> fixedValues, List<Contact> contactList) throws IllegalAccessException {
        cardHolder.setCommand("CMMDEXUP");
        getPropertyValueByObject(cardHolder, fixedValues);

        cardHolder.setContact(contactService.prepareContactObjectForChangeMobileNumber(contactList, fixedValues));
       // cardHolder.setSecondContact(contactService.prepareContactObjectForChangeMobileNumber(new Contact(), fixedValues));


        //   cardHolder.setSec_word(secWordService.prepareSecWordObjectFromPanel(cardHolder.getSec_word(),fixedValues));
        return cardHolder;
    }
}
