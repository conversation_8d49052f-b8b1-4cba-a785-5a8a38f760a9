package com.sss.fatora.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "localDBEntityManagerFactory",
        transactionManagerRef = "localDBTransactionManager",
        basePackages = "com.sss.fatora.dao.local"
)
public class LocalDBConfig {

    @Autowired
    Environment env;

    @Bean(name = "localDBDataSourceProperties")
    @ConfigurationProperties("spring.datasource-local-db")
    public DataSourceProperties localDBDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "localDBDataSource")
    public DataSource localDBDataSource() {
        DataSourceProperties localDBDataSourceProperties = localDBDataSourceProperties();
        return DataSourceBuilder.create()
                .driverClassName(localDBDataSourceProperties.getDriverClassName())
                .url(localDBDataSourceProperties.getUrl())
                .username(localDBDataSourceProperties.getUsername())
                .password(localDBDataSourceProperties.getPassword())
                .build();
    }

    @Bean(name = "localDBEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            @Qualifier("readingDBEntityManagerFactoryBuilder") EntityManagerFactoryBuilder localDBEntityManagerFactoryBuilder) {

        Map<String, String> JpaProperties = new HashMap<>();

        JpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.properties.hibernate.format_sql"));
        JpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
        JpaProperties.put("hibernate.hbm2ddl.auto", "update");
        JpaProperties.put("hibernate.dialect", env.getProperty("spring.datasource-local-db.hibernate.dialect"));

        return localDBEntityManagerFactoryBuilder
                .dataSource(localDBDataSource())
                .packages("com.sss.fatora.domain.local")
                //.persistenceUnit("secondaryDataSource")
                .properties(JpaProperties)
                .build();
    }

    @Bean(name = "localDBTransactionManager")
    public PlatformTransactionManager localDBTransactionManager(
            @Qualifier("localDBEntityManagerFactory") EntityManagerFactory localDBEntityManagerFactory) {
        return new JpaTransactionManager(localDBEntityManagerFactory);
    }

}
