package com.sss.fatora.security.service;

import com.sss.fatora.dao.local.ApplicationUserDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.utils.constants.UserType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional("localDBTransactionManager")
public class ApplicationUserDetailsService implements UserDetailsService {

    @Autowired
    private ApplicationUserDao applicationUserDao;

    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {

        // Fetch the appUser corresponding to the given username
        ApplicationUser appUser = applicationUserDao.findByEmail(email);

        // If the appUser doesn't exist
        if (appUser == null) {
            throw new UsernameNotFoundException("Database User" + email + " not found");
        }

        // If the LDAP User doesn't exist
        String password = appUser.getPassword();

        List<String> privileges = appUser.getApplicationUserPrivileges()
                .stream()
                .filter(p->p.getPrivilege().getRecordStatus()==1)
                .map(applicationUserPrivilege -> applicationUserPrivilege.getPrivilege().getName())
                .collect(Collectors.toList());
        List<GrantedAuthority> authorities = new ArrayList<>();
        for (String privilege : privileges) {
            authorities.add(new SimpleGrantedAuthority(privilege));
        }
        if ("internal".equalsIgnoreCase(appUser.getUserType())) {
            authorities.add((new SimpleGrantedAuthority("Admin")));
        }
        User user = new CustomUserDetails(appUser.getEmail(), (String) password,
                true, true, true,
                true, authorities);

        ((CustomUserDetails) user).setApplicationUser(appUser);
        if (appUser.getBank() != null)
            RequestContextHolder.getRequestAttributes().setAttribute("bank", appUser.getBank().getAbbreviatedName(), 1);
        if (RequestContextHolder.getRequestAttributes() != null && RequestContextHolder.getRequestAttributes
                ().getAttribute("mobile", 0) != null) {
            ((CustomUserDetails) user).setMobile((Boolean) RequestContextHolder.getRequestAttributes
                    ().getAttribute("mobile", 0));
        }
        return user;
    }
}