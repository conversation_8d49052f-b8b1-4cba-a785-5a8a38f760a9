package com.sss.fatora.utils.service;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;

@Service
public class HttpService {

    @Autowired
    Environment env;

    public String doPost(String url, Map<String, String> headerParams, MultiValueMap<String, String> httpParams, Class responseType) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.APPLICATION_FORM_URLENCODED, MediaType.APPLICATION_XML));
        HttpMessageConverter formHttpMessageConverter = new FormHttpMessageConverter();
        HttpMessageConverter stringHttpMessageConverternew = new StringHttpMessageConverter();
        restTemplate.getMessageConverters().add(mappingJackson2HttpMessageConverter);
        restTemplate.getMessageConverters().add(formHttpMessageConverter);
        restTemplate.getMessageConverters().add(stringHttpMessageConverternew);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        for (String header : headerParams.keySet())
            headers.set(header, headerParams.get(header));
        HttpEntity entity = new HttpEntity(headers);

        if (httpParams == null)
            httpParams = new LinkedMultiValueMap<String, String>();

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(httpParams, headers);
        String response = null;
        try {
             response = restTemplate.postForObject(url, request, String.class);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    public Object doGet(String url, Map<String, String> headerParams, Map<String, String> httpParams, Class responseType) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();

        // Setting Headers
        for (String header : headerParams.keySet())
            headers.set(header, headerParams.get(header));
        HttpEntity entity = new HttpEntity(headers);

        restTemplate.getMessageConverters()
                .add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));

        // Setting http params
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (httpParams != null && httpParams.size() > 0) {
            for (String httpParam : httpParams.keySet()) {
                builder.queryParam(httpParam, httpParams.get(httpParam));
            }
        }
        try {
            ResponseEntity<Object> response = restTemplate.getForEntity("http://messaging.ooredoo.qa/bms/soap/Messenger.asmx/HTTP_SendSms?userPassword= k85qlo4r&recipientPhone=97466622435&messageType=0&defDate=&customerID=2369&Private=false&originator=Al Meera&blink=false&userName=almeera&smsText=HereMeera&flash=false",
                    responseType);

            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public Object doAuthenticate(String url, Map<String, String> headerParams, Map<String, String> httpParams, Class responseType, String sms) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();

        // Setting Headers
        for (String header : headerParams.keySet())
            headers.set(header, headerParams.get(header));
        HttpEntity entity = new HttpEntity(headers);

        restTemplate.getMessageConverters()
                .add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));

        // Setting http params
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (httpParams != null && httpParams.size() > 0) {
            for (String httpParam : httpParams.keySet()) {
                builder.queryParam(httpParam, httpParams.get(httpParam));
            }
        }
        try {
            ResponseEntity<Object> response = restTemplate.getForEntity(builder.toUriString()+"&userPassword=Meeraqa@2018&originator=Al Meera&userName=almeera"+"&smsText="+sms,
                    responseType);

            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public String getUrlByProfile(String prod,String uat){
        String url;
        String[] activeProfile = env.getActiveProfiles();
        if (activeProfile[0].equalsIgnoreCase("PRODUCTION")){
            url = env.getProperty(prod);
        }
        else {
            url = env.getProperty(uat);
        }
        return url;
    }

}
