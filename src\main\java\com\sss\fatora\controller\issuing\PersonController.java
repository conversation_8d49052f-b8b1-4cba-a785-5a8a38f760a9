package com.sss.fatora.controller.issuing;

import com.sss.fatora.domain.issuing.Person;
import com.sss.fatora.service.issuing.PersonService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/person")
public class PersonController {

    @Autowired
    PersonService personService;

    @RequestMapping(method = RequestMethod.GET)
    public ResponseObject getPerson(@RequestParam(value = "personIdType")String personIdType,
                                    @RequestParam(value = "personIdNumber")String personIdNumber,
                                    @RequestParam(value = "bankPrefix")String bankPrefix){
        Map<String, Object> requestedPerson = personService.getPersonByTypeAndNumber(personIdType, personIdNumber, bankPrefix);
        return requestedPerson != null ?
                ResponseObject.FETCHED_SUCCESS(requestedPerson,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }

    @RequestMapping(value = "/by-id", method = RequestMethod.GET)
    public ResponseObject getPersonById(@RequestParam(value = "personId")Long personId){
        Person requestedPerson = personService.getPersonById(personId);
        return requestedPerson != null ?
                ResponseObject.FETCHED_SUCCESS(requestedPerson,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }
}
