package com.sss.fatora.utils.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;

import java.util.HashMap;
import java.util.Map;

public class MapWrapper<T> {

    private Map<String, T> map;

    public MapWrapper() {
        this.map = new HashMap<String, T>();
    }

    @JsonAnyGetter
    public Map<String, T> getMap() {
        return map;
    }

    @JsonAnySetter
    public void setMap(String key, T value) {
        if (value != null)
            this.map.put(key, value);
    }
}
