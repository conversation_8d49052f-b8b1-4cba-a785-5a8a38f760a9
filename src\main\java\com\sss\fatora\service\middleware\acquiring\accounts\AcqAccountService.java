package com.sss.fatora.service.middleware.acquiring.accounts;

import com.sss.fatora.domain.middleware.acquiring.AcqAccounts.AcqAccount;
import com.sss.fatora.domain.middleware.acquiring.AcqAccounts.AcqAccountRequest;
import com.sss.fatora.domain.middleware.response.ListMiddlewareResponse;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Service
public class AcqAccountService {

    private final Environment environment;

    private final RestTemplate restTemplate;

    public AcqAccountService(Environment environment,RestTemplate restTemplate) {
        this.environment = environment;
        this.restTemplate = restTemplate;
    }

    public List<AcqAccount> getCustomerAccounts(AcqAccountRequest acqAccountRequest) throws Exception {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetCustomerAccounts");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(acqAccountRequest, headers);

        ResponseEntity<ListMiddlewareResponse> response = restTemplate.postForEntity(url, request,ListMiddlewareResponse.class );

        if (response.getBody() != null && !response.getBody().getSuccess()) {
            throw new Exception("error in fetching customer accounts");
        }

        return response.getBody().getResult();
/*
        List<String> fields = new ArrayList<>();

        Map<String, List<String>> fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH.getType(), null, fields);
        return setUpDeviceRequest(acqAccountRequest, url, fieldsMap.get("fields"));*/
    }
}
