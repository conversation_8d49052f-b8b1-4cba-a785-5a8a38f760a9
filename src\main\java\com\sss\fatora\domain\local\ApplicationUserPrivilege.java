package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;


@Entity
@Setter
@Getter

@NoArgsConstructor
@Table(name = "Application_User_Privilege")
public class ApplicationUserPrivilege extends LocalDomain {



    /*@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "APPLICATION_USER_PRIVILEGE_SEQ")
    @SequenceGenerator(sequenceName = "APPLICATION_USER_PRIVILEGE_SEQ",initialValue = 1, allocationSize = 1, name = "APPLICATION_USER_PRIVILEGE_SEQ")*/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties("applicationUserPrivileges")
    @JoinColumn(name = "Application_User_Id", referencedColumnName = "id")
    private ApplicationUser applicationUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties("applicationUserPrivileges")
    @JoinColumn(name = "Privilege_Id", referencedColumnName = "id")
    private Privilege privilege;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}
