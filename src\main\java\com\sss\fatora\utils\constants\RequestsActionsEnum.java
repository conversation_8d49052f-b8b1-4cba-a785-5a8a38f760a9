package com.sss.fatora.utils.constants;

public enum RequestsActionsEnum {

    CREATE("CREATE"),
    APPROVE("APPROVE"),
    REJECT("REJECT"),
    REPROCESS("REPROCESS"),
    MOVE_TO_PROCESS("MOVE TO PROCESS"),
    ARCHIVE("ARCHIVE");

    private final String stringValue;

    RequestsActionsEnum(String stringValue) {
        this.stringValue = stringValue;
    }

    public String getStringValue() {
        return stringValue;
    }
}
