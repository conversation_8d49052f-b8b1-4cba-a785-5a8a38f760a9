package com.sss.fatora.domain.issuing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_CARDHOLDERS")
public class Cardholder {
    @Id
    @Column(name = "CARDHOLDER_ID")
    Long id;

    @Column(name = "CUSTOMER_ID")
    Long customerId;

    @Column(name = "PERSON_ID")
    Long personId;

    @Column(name = "CARDHOLDER_NUMBER")
    String cardholderNumber;

    @Column(name = "CARDHOLDER_NAME")
    String cardholderName;

    @Column(name = "FIRST_NAME")
    String firstName;

    @Column(name = "SURNAME")
    String lastName;

    @Column(name = "MOBILE_NUMBER")
    String mobile;
}
