package com.sss.fatora.utils.service;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.GenericDomain;
import org.hibernate.collection.internal.PersistentSet;
import org.springframework.stereotype.Service;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class UpdateService<Domain extends GenericDomain, IdClass extends Serializable> {

    public Domain update(Domain domain, Domain domainFromDB) throws Exception {
        for (PropertyDescriptor pd : Introspector.getBeanInfo(domain.getClass()).getPropertyDescriptors()) {
            if (pd.getReadMethod() != null && !"class".equals(pd.getName())) {
                Class<?> classType = pd.getPropertyType();
                Method getter = pd.getReadMethod();
                if (classType.equals(Set.class) || classType.equals(List.class)) {

                    Set listFromRequest = (Set) getter.invoke(domain);
                    if (listFromRequest != null) {
                        if (listFromRequest.isEmpty()) {
                            this.deleteList(getter, domainFromDB);
                        } else {
                            this.editList(pd, classType, getter, domainFromDB, listFromRequest, domain);

                        }
                    } else {
                        continue;
                    }

                }
            }
        }
        return domain;
    }

    public void deleteList(Method getter, Domain domainFromDB) throws InvocationTargetException, IllegalAccessException {
        Set listFromDB = (Set) getter.invoke(domainFromDB);
        if (!listFromDB.isEmpty()) {
            GenericDomain currentEntity = (GenericDomain) listFromDB.stream().findFirst().get();
            GenericDao entityDao = currentEntity.evaluateEntityDao();
            for (Object object : (PersistentSet) listFromDB) {
                entityDao.delete(object);

            }
        }

    }

    public void editList(PropertyDescriptor pd, Class<?> classType, Method getter, Domain domainFromDB, Set listFromRequest, Domain domainFromRequest) throws Exception {
        Set listFromDB = (Set) getter.invoke(domainFromDB);


        GenericDomain currentEntity = (GenericDomain) listFromDB.stream().findFirst().get();
        GenericDao entityDao = currentEntity.evaluateEntityDao();
        for (Object object : listFromDB) {

            entityDao.delete(object);

        }
        for (Object object : listFromRequest) {
            Method setParentMethod = this.getParentSetterMethod(domainFromRequest.getClass(), currentEntity.getClass());
            setParentMethod.invoke(object, domainFromRequest);
            pd.getWriteMethod().invoke(domainFromRequest, classType.equals(Set.class) ? new HashSet() : new ArrayList());
            entityDao.save(object);
        }
    }

    private Method getParentSetterMethod(Class<?> domain, Class<?> childClassType) throws Exception {
        for (PropertyDescriptor pd : Introspector.getBeanInfo(childClassType).getPropertyDescriptors()) {
            Class<?> classType = pd.getPropertyType();
            if (classType.equals(domain)) {
                return pd.getWriteMethod();
            }
        }

        return null;
    }
}
