package com.sss.fatora.controller.read;

import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.TransactionDataVW;
import com.sss.fatora.service.read.TransactionService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/transaction")
public class TransactionController {

    @Autowired
    TransactionService transactionService;

    @PreAuthorize("hasAnyAuthority('Admin','US','myOnline')")
    @PostMapping(value = "/search")
    public ResponseObject getTransactions(@ParameterName(value = "filter", required = false) TransactionDataVW transaction,
                                          @ParameterName(value = "fromUDate", required = false) Long fromUDate,
                                          @ParameterName(value = "toUDate", required = false) Long toUDate,
                                          @ParameterName(value = "pagination", required = false) Pagination pagination,
                                          @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                          @ParameterName(value = "withBins", required = false) Boolean withBins) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        if (transaction == null)
            transaction = new TransactionDataVW();
        Page<TransactionDataVW> transactions = transactionService.mainSearch(transaction, fromUDate, toUDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),withBins,false);
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", transactions != null ? transactions.getTotalElements() : 0);
        if (transactions == null){
            return ResponseObject.FETCHED_SUCCESS(0, extra);
        }
        return ResponseObject.FETCHED_SUCCESS(transactions.getContent(), extra);
    }
@PostMapping(value = "/search-count")
    public ResponseObject getTransactionsCount(@ParameterName(value = "filter", required = false) TransactionDataVW transaction,
                                          @ParameterName(value = "fromUDate", required = false) Long fromUDate,
                                          @ParameterName(value = "toUDate", required = false) Long toUDate,
                                          @ParameterName(value = "pagination", required = false) Pagination pagination,
                                          @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                          @ParameterName(value = "withBins", required = false) Boolean withBins) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        if (transaction == null)
            transaction = new TransactionDataVW();
        Page<TransactionDataVW> transactions = transactionService.mainSearch(transaction, fromUDate, toUDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),withBins,true);
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", transactions != null ? transactions.getTotalElements() : 0);
        if (transactions == null){
            return ResponseObject.FETCHED_SUCCESS(0, extra);
        }
        return ResponseObject.FETCHED_SUCCESS(transactions.getContent(), extra);
    }
    @RequestMapping(value = "/get-with-details", method = RequestMethod.POST)
    public ResponseObject getWithDetails(@RequestParam("recordId") Long recordId,
                                         @RequestParam(value = "withBins", required = false) Boolean withBins)
            throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {
        TransactionDataVW filter = new TransactionDataVW();
        TransactionDataVW transaction = transactionService.getWithDetails(filter, recordId,withBins,false);
        return transaction != null ?
                ResponseObject.FETCHED_SUCCESS(transaction, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    /*@PostMapping(value = "/export")
    public ResponseObject export(@ParameterName(value = "filter", required = false) TransactionDataVW transaction,
                                 @ParameterName(value = "fromUDate",required = false)Long fromUDate,
                                 @ParameterName(value = "toUDate",required = false) Long toUDate,
                                 @ParameterName(value = "details",required = false) Boolean details,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @ParameterName(value ="filterOperator",required = false) MapWrapper<Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        if (transaction == null)
            transaction = new TransactionDataVW();
        Page<TransactionDataVW> transactions = transactionService.export(transaction,fromUDate,toUDate,pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),details);
        HashMap<String,Object> extra=new HashMap<>();
        extra.put("count",transactions.getTotalElements());
        return ResponseObject.FETCHED_SUCCESS(transactions.getContent(), extra);
    }*/
}
