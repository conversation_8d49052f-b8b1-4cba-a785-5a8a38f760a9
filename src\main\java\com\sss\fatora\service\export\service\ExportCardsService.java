package com.sss.fatora.service.export.service;

import com.sss.fatora.dao.read.ReadCardDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.export.service.ExportService;
import com.sss.fatora.service.local.ApplicationUserPrivilegeService;
import com.sss.fatora.service.local.BankService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.beans.IntrospectionException;
import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
//@Transactional("readingDBTransactionManager")
public class ExportCardsService {
    final ReadCardDao readCardDao;
    final BankService bankService;
    final ApplicationUserPrivilegeService applicationUserPrivilegeService;
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;
    final ExportService exportService;

   /* List<String> mainProjection;
    List<String> detailsProjection;*/

    @Autowired
    @Qualifier("readingDBEntityManagerFactory")
    private EntityManager entityManager;
    @Autowired
    ContentUtilService contentUtilService;

    public ExportCardsService(ReadCardDao readCardDao, BankService bankService, ApplicationUserPrivilegeService applicationUserPrivilegeService, PaginationService paginationService, PrivilegeService privilegeService, ConfigService configService, ExportService exportService) {
        this.readCardDao = readCardDao;
        this.bankService = bankService;
        this.applicationUserPrivilegeService = applicationUserPrivilegeService;
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.exportService = exportService;

        /*mainProjection = Stream.of("crefNoInt", "crefNo", "issueDate", "expDate", "status", "embosName", "agentCode", "pinCnt"
        )
                .collect(Collectors.toList());
        detailsProjection = Stream.of("name", "surname", "mobilePhone", "firstAddress", "secondAddress", "city")
                .collect(Collectors.toList());*/
    }


    /**
     * @param card  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromIssueDate,toIssueDate This Two Parameters Represent The Period We Want To Search In (Card Issuing Date)
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection This Is A List Of Columns For Select Statement In Search Query
     * @param details This Parameter Is For Deciding If The Excel Is With Details
     *
     * This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (CardDataVW)
     * Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     *
     * */

    public byte[] search(CardDataVW card, Long fromIssueDate, Long toIssueDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection, Boolean details) throws Exception {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            String additionalConstraint = "";
            String date1 = "";
            String date2 = "";
            String oracleFormat = "yyyy-MM-dd HH24:MI:ss";
            String javaFormat2 = "yyyy-MM-dd HH:mm:ss";
            Page<CardDataVW> cards;
            String bins = "";

            if (card == null)
                card = new CardDataVW();
            if (fromIssueDate == null)
                fromIssueDate = new Date(631152000).getTime();
            if (toIssueDate == null)
                toIssueDate = new Date(System.currentTimeMillis() * 100).getTime();

            if (card.getExpDate() != null) {
                card.setExpDate(Long.valueOf(DateUtils.getYearMonthFromCurrentDate()));
                filterOperator.put("expDate", Operator.EQUALS_OR_GREATER_THAN);
            }
            if (card.getCrefNo() != null) {
                //card.setCrefNo(card.getCrefNo().replace('*', '%'));
                filterOperator.put("crefNo", Operator.CONTAINS_WITH_STAR);
            }
            DateFormat f = new SimpleDateFormat(javaFormat2);
            date1 = "to_timestamp('" + f.format(DateUtils.getStartDayOfDate(fromIssueDate)) + "','" + oracleFormat + "')";
            date2 = "to_timestamp('" + f.format(DateUtils.getEndDayOfDate(toIssueDate)) + "','" + oracleFormat + "')";

            if (applicationUser.getBank() != null)
                bins = applicationUser.getBank().getBinCodes().stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));


            if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType()))
                additionalConstraint = "substring(CardDataVW.crefNo,0,6) IN (" + bins + ") " +
                        "AND CardDataVW.issueDate BETWEEN " + date1 + " AND " + date2;
            else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()))
                additionalConstraint = "CardDataVW.issueDate BETWEEN " + date1 + " AND " + date2;

            if (pagination == null)
                pagination = new Pagination();
            if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
                pagination.setOrderBy("CardDataVW.issueDate DESC , CardDataVW.cardId ");
                pagination.setOrderType("DESC");
            }



            File file = contentUtilService.makeExcelFile();
            TempContentModel tempContentModel = contentUtilService.makeTempModel(file);
            if (details == false) {
                cards = exportService.dynamicSearch(card
                        , pagination
                        , additionalConstraint
                        , filterOperator
                        , projection
                        , file.getPath()
                        , configService.getCardHeaders()
                        , configService.getCardColumns());

                return contentUtilService.fetchAsBytes(tempContentModel);
            }
            else {
                cards = exportService.dynamicSearch(card
                        , pagination
                        , additionalConstraint
                        , filterOperator
                        , projection
                        , file.getPath()
                        , configService.getCardHeadersWithDetails()
                        , configService.getCardColumnsWithDetails());

                return contentUtilService.fetchAsBytes(tempContentModel);
            }
        } catch (Exception exception) {
            throw exception;
        }
    }

    /**
     * @param card  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromIssueDate,toIssueDate This Two Parameters Represent The Period We Want To Search In (Card Issuing Date)
     * @param filterOperators There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param details This Parameter Is For Deciding If The Excel Is With Details
     *
     * This Function Get UserPrivileges And Domain Columns And Then Request The Intersection Function
     * ( filterProjectionList() ) On Them And Then Request Search Function ( search() )
     *
     * */
    @Log(actionType = ActionType.CARD_EXPORT)
    public byte[] export(CardDataVW card, Long fromIssueDate, Long toIssueDate, Pagination pagination, Map<String, Operator> filterOperators, Boolean details) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getCardMainColumns());
        if (details)
            projection.addAll(configService.getCardDetailsColumns());
        projection = filterProjectionList(projection, privileges);
        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        return search(card, fromIssueDate, toIssueDate, pagination, filterOperators, projection,details);

    }


    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As Column) To Be Used In The
     *                   Select Section Of The Query
     * */

    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> {
                    if(s.equalsIgnoreCase("status")){
                        s =" CardStatusVW.descX AS " + s;
                    }
                    else if(s.equalsIgnoreCase("agentCode")){
                        s =" Agents.agentShortDesc AS " + s;
                    }
                    else {
                        s =" CardDataVW." + s + " AS " + s;
                    }
                    return s;
                })
                .collect(Collectors.toList());
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This Specific Domain (Customer)
     * */
    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.CARD_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }


}
