package com.sss.fatora.domain.local;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class Fault {
    String faultCode;
    String faultString;
    Detail detail;


    @JacksonXmlProperty(localName = "faultcode")
    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }
    @JacksonXmlProperty(localName = "faultstring")
    public String getFaultString() {
        return faultString;
    }

    public void setFaultString(String faultString) {
        this.faultString = faultString;
    }
    @JacksonXmlProperty(localName = "detail")
    public Detail getDetail() {
        return detail;
    }

    public void setDetail(Detail detail) {
        this.detail = detail;
    }
}
