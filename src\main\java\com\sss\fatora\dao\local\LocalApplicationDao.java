package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.LocalApplication;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface LocalApplicationDao extends GenericDao<LocalApplication,Integer> {

    @Query(value="SELECT DISTINCT A FROM LocalApplication A " +
            "WHERE A.status = :status")
    Page<LocalApplication> getApplicationByStatus(@Param("status") String status, Pageable pageRequest);

    @Query(value="SELECT DISTINCT A FROM LocalApplication A " +
            "WHERE A.status ='in_progress'" +
            "OR A.status = 'failed'" +
            "OR A.status = 'success' ")
    Page<LocalApplication> getSubmittedApplications(Pageable pageRequest);

    @Query(value="SELECT DISTINCT A FROM LocalApplication A " +
            "WHERE A.status = :status")
    Page<LocalApplication> getApplicationByNumber(@Param("status") String status, Pageable pageRequest);

    @Query(value="SELECT DISTINCT A FROM LocalApplication A " +
            "WHERE A.id in :idList")
    List<LocalApplication> getApplicationByIdList(@Param("idList") List<Long> idList);

    @Query(value="SELECT DISTINCT A FROM LocalApplication A " +
            "WHERE A.id in :id")
    LocalApplication getApplicationById(@Param("id") Long id);
}
