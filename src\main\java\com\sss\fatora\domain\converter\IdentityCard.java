package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "command","id_type","id_series","id_number"})
public class IdentityCard extends GenericConverter{
    @ExcelProperty(name = "ns1:command12")
    private String command;
    @ExcelProperty(name = "ns1:id_type")
    private String id_type;
    @ExcelProperty(name = "ns1:id_series")
    private String id_series;
    @ExcelProperty(name = "ns1:id_number")
    private String id_number;
}
