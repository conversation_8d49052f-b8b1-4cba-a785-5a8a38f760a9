package com.sss.fatora.controller.smpp;

import com.sss.fatora.domain.smpp.MessageLog;
import com.sss.fatora.domain.smpp.MessageLogType;
import com.sss.fatora.service.smpp.MessageLogService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "/message-log-old")
public class MessageLogController {

    private final MessageLogService messageLogService;

    public MessageLogController(MessageLogService messageLogService) {
        this.messageLogService = messageLogService;
    }

    @PreAuthorize("hasAnyAuthority('Admin','Search Bank SMS')")
    @PostMapping(value = "/search")
    public ResponseObject getBankMessages(@ParameterName(value = "filter", required = false) MessageLog messageLog,
                                          @ParameterName(value = "fromDate", required = false) Long fromDate,
                                          @ParameterName(value = "toDate", required = false) Long toDate,
                                          @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                          @ParameterName(value = "pagination", required = false) Pagination pagination) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, ParseException {
        Page<MessageLog> messageLogs = messageLogService.mainSearch(messageLog, fromDate, toDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", messageLogs != null ? messageLogs.getTotalElements() : 0);
        return messageLogs != null ?
                ResponseObject.FETCHED_SUCCESS(messageLogs.getContent(), extra) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @GetMapping(value = "/types")
    public ResponseObject getMessageTypes(){
        List<MessageLogType> messageLogTypes=messageLogService.getMessageTypes();
        return messageLogTypes != null ?
                ResponseObject.FETCHED_SUCCESS(messageLogTypes, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
