package com.sss.fatora.domain.read;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "FAT_FP_CARD_ACCOUNT_BO")
public class AccountBo {
    @Id
    @Column(name = "CARD_NO")
    private String cardNo;
    @Column(name = "ACCT_NO")
    private String acctNo;
    @Column(name = "CURRENCY_TYPE")
    private String currencyType;
    @Column(name = "IS_ATM_PRIMARY")
    private Boolean atmPrimary;
    @Column(name = "IS_POS_PRIMARY")
    private Boolean posPrimary;
    @Column(name = "ACCT_TYPE")
    private String acctType;
}
