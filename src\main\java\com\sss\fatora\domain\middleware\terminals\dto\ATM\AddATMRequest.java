package com.sss.fatora.domain.middleware.terminals.dto.ATM;

import com.sss.fatora.domain.middleware.terminals.dto.TerminalDto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AddATMRequest  extends TerminalDto{
    private String ipAddress;
    private String localPort;
    private String cassetteOne;
    private String cassetteTwo;
    private String cassetteThree;
    private String cassetteFour;
}
