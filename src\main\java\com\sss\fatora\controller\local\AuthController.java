package com.sss.fatora.controller.local;

import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.security.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    AuthService authService;

    @PostMapping(value = "/fetch-details")
    public void save(HttpServletRequest request, HttpServletResponse servletResponse,
                     CustomUserDetails userDetails) {
        authService.buildLoginSuccessResponse(request, servletResponse, userDetails);
    }
}
