package com.sss.fatora.utils.constants;

public enum BulkTemplateType {
    Close_Card("close"),
    <PERSON><PERSON><PERSON>_Card("validate"),
    Change_Status("change"),
    Reissue_Card("reissue"),
    Renew_Card("renew"),
    Instant_Reissue_Card("instant-reissue"),
    Instant_Renew_Card("instant-renew"),
    Change_Language("change-language"),
    Change_Product("change-product");
        private String type;


    BulkTemplateType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
