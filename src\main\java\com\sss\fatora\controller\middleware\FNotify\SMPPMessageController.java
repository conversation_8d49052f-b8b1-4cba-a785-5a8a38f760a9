package com.sss.fatora.controller.middleware.FNotify;

import com.sss.fatora.domain.middleware.FNotify.*;
import com.sss.fatora.service.middleware.FNotify.MessageService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

@RestController
@RequestMapping(value = "/message-log")
public class SMPPMessageController {
    private final MessageService messageService;

    public SMPPMessageController(MessageService messageService) {
        this.messageService = messageService;
    }


    @PreAuthorize("hasAnyAuthority('Admin','F-Notify_Search')")
    @PostMapping(value = "/search")
    public ResponseObject getBankMessages(@ParameterName(value = "filter", required = false) MessageRequest message,
                                          @ParameterName(value = "pagination", required = false) Pagination pagination) {
        MessagesAPIResponse messages = messageService.search(message, pagination);
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", messages != null ? messages.getTotalSize() : 0);
        extra.put("pages", messages != null ? (Math.floor(messages.getTotalSize()  / pagination.getSize()) == 0 ? 1 : Math.floor(messages.getTotalSize()  / pagination.getSize())) : 0);
        return messages != null ?
                ResponseObject.FETCHED_SUCCESS(messages, extra) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @GetMapping(value = "/types")
    public ResponseObject getMessageTypes() {
        MessagesTypeResponse messageLogTypes = messageService.getMessageTypes();
        return messageLogTypes != null ?
                ResponseObject.FETCHED_SUCCESS(messageLogTypes, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


}
