package com.sss.fatora.domain.local.application;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalAccount {

    private Long id;
    private String accountNumber;
    private String accountType;
    private String currency;
    private String is_atm_default;
    private String is_pos_default;
    private String statusName;

}
