package com.sss.fatora.service.issuing;

import com.sss.fatora.dao.issuing.CustomerDao;

import com.sss.fatora.dao.issuing.PersonDao;
import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.issuing.Person;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.ObjectConverter;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CustomerService extends GenericReadService<CustomerDao, Customer, Long> {

    final CustomerDao customerDao;
    final PersonDao personDao;
    final PrivilegeService privilegeService;
    final ConfigService configService;

    public CustomerService(CustomerDao customerDao, PersonDao personDao, PrivilegeService privilegeService, ConfigService configService) {
        this.customerDao = customerDao;
        this.personDao = personDao;
        this.privilegeService = privilegeService;
        this.configService = configService;
    }

    /**
     * @param customerNumber This Parameter Is Like An Id For Customer Table
     *                       <p>
     *                       This Function Get One Specific Customer By Customer Number If The Costumer Is Founded
     *                       Then It Search For The Person Related To The Customer
     */
    public Map getCustomerByNumber(String customerNumber) {
        Map<String, Object> map = new HashMap<>();
        Customer requestedCustomer = customerDao.getByCustomerNumber(customerNumber);
        if (requestedCustomer != null) {
            map.put("Customer", requestedCustomer);
            Person relatedPerson = personDao.getByPersonId(requestedCustomer.getCustomerPersonId());
            if (relatedPerson != null) {
                map.put("Person", relatedPerson);
            }
        }
        return map;
    }


    public Map getFirstByCustomerNumber(String customerNumber) {
        Map<String, Object> map = new HashMap<>();
        List<Customer> requestedCustomerList = customerDao.getFirstByCustomerNumber(customerNumber);
        if (requestedCustomerList != null && !requestedCustomerList.isEmpty()) {
            Customer requestedCustomer = requestedCustomerList.get(0);
            if (requestedCustomer != null) {
                map.put("Customer", requestedCustomer);
                if (requestedCustomer.getCustomerPersonId() != null) {
                    List<Person> relatedPersonList = personDao.getFirstByPersonId(requestedCustomer.getCustomerPersonId());
                    if (relatedPersonList != null && !relatedPersonList.isEmpty()) {
                        Person relatedPerson = relatedPersonList.get(0);
                        if (relatedPerson != null) {
                            map.put("Person", relatedPerson);
                        }
                    }
                }
            }
        } else return null;
        return map;
    }

    /**
     * @param cust           This Parameter Is For The Value Of Filters Send From The Front-End
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection     This Is A List Of Columns For Select Statement In Search Query
     *                       <p>
     *                       This Function Can Add Certain Conditions To Where Clause That Can't Be Added To The Filter (Customer)
     *                       Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     */

    public Page<Customer> search(Customer cust, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection) throws ParseException, InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            String additionalConstraint = "";
            Page<Customer> customer;
            String bins = "";

            if (cust == null) {
                cust = new Customer();
            }
            if (pagination == null) {
                pagination = new Pagination();
            }
            if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
                String bankPrefix = applicationUser.getBank().getPrefix();
                if(!bankPrefix.equals("SIIB"))
                additionalConstraint += " substring(Customer.customerNumber,0,4) LIKE '" + bankPrefix + "' ";
            }
            if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
                pagination.setOrderBy("Customer.id");
                pagination.setOrderType("DESC");}
            customer = this.dynamicSearch(cust
                    , pagination
                    , additionalConstraint
                    , filterOperator
                    , projection
                    ,true);

            if (customer == null) {
                return Page.empty();
            }
            return customer;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * @param customer        This Parameter Is For The Value Of Filters Send From The Front-End
     * @param filterOperators There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *                        <p>
     *                        This Function Get UserPrivileges And Domain Columns And Then Request The Intersection Function
     *                        ( filterProjectionList() ) On Them And Then Request Search Function ( search() )
     */

    @Log(actionType = ActionType.CUSTOMER_SEARCH)
    public Page<Customer> mainSearch(Customer customer, Pagination pagination, Map<String, Operator> filterOperators) throws NoSuchFieldException, IntrospectionException, IllegalAccessException, ParseException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getCustomerMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
        if (projection == null || projection.isEmpty()) {
            return Page.empty();
        }
        Page<Customer> customers = search(customer, pagination, filterOperators, projection);
        return customers;
    }

    /**
     * @param filter         This Parameter Is For The Value Of Filters Send From The Front-End
     * @param customerNumber This Parameter Is Set In The Filter Param For Where Clause Of The Query
     *                       <p>
     *                       This Function Get UserPrivileges And Domain Columns And Then Request The Intersection Function
     *                       ( filterProjectionList() ) On Them , Set The Desired Filter Values To The Filter Object And Then
     *                       Request Search Function ( search() )
     */

    @Log(actionType = ActionType.CUSTOMER_DETAILS)
    public Customer getWithDetails(Customer filter, String customerNumber) throws NoSuchFieldException, IntrospectionException, IllegalAccessException, ParseException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getCustomerDetailsColumns());
//        projection.addAll(configService.getCustomerDetailsColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
        if (projection == null || projection.isEmpty()) {
            return new Customer();
        }
        filter.setCustomerNumber(customerNumber);
        Page<Customer> customers = search(filter, null, new HashMap<>(), projection);
        if (customers != null) {
            return !customers.isEmpty() ? ObjectConverter.convertToObject(customers.getContent().get(0), Customer.class) : null;
        }
        return null;
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This Specific Domain (Customer)
     */

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.CUSTOMER_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As Column) To Be Used In The
     *                   Select Section Of The Query
     */

    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        List<String> projections = projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> "Customer." + s + " AS " + s)
                .collect(Collectors.toList());
        return projections;
    }

}
