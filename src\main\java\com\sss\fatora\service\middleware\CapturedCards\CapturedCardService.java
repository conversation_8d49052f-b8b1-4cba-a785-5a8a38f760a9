package com.sss.fatora.service.middleware.CapturedCards;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.middleware.CapturedCards.CaptureReasonsResponse;
import com.sss.fatora.domain.middleware.CapturedCards.CapturedCardRequest;
import com.sss.fatora.domain.middleware.CapturedCards.CapturedCardResponse;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.export.service.middleware.ExportCapturedCardService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.service.middleware.terminals.TerminalsService;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class CapturedCardService {
    private final Environment environment;
    private final ConfigService configService;
    final RestTemplate restTemplate;
    private final ExportCapturedCardService exportCapturedCardService;
    private final PrivilegeService privilegeService;
    private static final Logger LOGGER = Logger.getLogger(TerminalsService.class.getName());

    public CapturedCardService(Environment environment, ConfigService configService, RestTemplate restTemplate, ExportCapturedCardService exportCapturedCardService, PrivilegeService privilegeService) {
        this.environment = environment;
        this.configService = configService;
        this.restTemplate = restTemplate;
        this.exportCapturedCardService = exportCapturedCardService;
        this.privilegeService = privilegeService;
    }

    public CapturedCardResponse search(CapturedCardRequest capturedCardRequest, Pagination pagination, Boolean export, HttpServletResponse response) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            capturedCardRequest.setBank(bankCode);
        }
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetCapturedCards");
        Map<String, List<String>> fieldsMap;
        if (pagination == null) {
            pagination = new Pagination();
        }
        pagination.setOrderBy("captDate DESC");
        if (export) {
            Pagination pagination1 = new Pagination();
            pagination1.setMaxResult(0);
            pagination1.setSize(0);
            pagination1.setStart(0);
            fieldsMap = setFieldsByPrivilege(configService.getCapturedCardsMainExportHeaders(),
                    configService.getCapturedCardsMainColumns());
            CapturedCardResponse capturedCardResponse = setUpCaptureCardsRequest(capturedCardRequest, pagination1, url,
                    fieldsMap.get("fields"));
            try {
                exportCapturedCardService.export(capturedCardResponse.getResult().getItems(), response,
                        fieldsMap.get("headers"), fieldsMap.get("fields"));
            } catch (IOException e) {
                e.printStackTrace();
            }
            return null;
        } else {
            fieldsMap = setFieldsByPrivilege(null, configService.getCapturedCardsMainColumns());
            return setUpCaptureCardsRequest(capturedCardRequest, pagination, url, fieldsMap.get("fields"));
        }
    }

    private CapturedCardResponse setUpCaptureCardsRequest(CapturedCardRequest capturedCardRequest,
                                                          Pagination pagination, String url, List<String> fields) {
        HttpHeaders headers = new HttpHeaders();
        if (capturedCardRequest == null) {
            capturedCardRequest = new CapturedCardRequest();
        }
        if (pagination == null) {
            capturedCardRequest.setMaxResultCount(10);
            capturedCardRequest.setSkipCount(0);
        } else {
            capturedCardRequest.setSorting(pagination.getOrderBy());
            capturedCardRequest.setMaxResultCount(pagination.getSize());
            capturedCardRequest.setSkipCount(pagination.getStart());
        }
        capturedCardRequest.setFields(fields);
//        capturedCardRequest.setFields(configService.getCapturedCardsMainColumns());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(capturedCardRequest, headers);
        LOGGER.info(" url :" + url);
        LOGGER.info(" \nRequest :" + new ObjectMapper().valueToTree(request).toString());
        ResponseEntity<CapturedCardResponse> response = restTemplate.postForEntity(url, request, CapturedCardResponse.class);
        LOGGER.info(" \nResponse :" + new ObjectMapper().valueToTree(response).toString());
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }

        if (response.hasBody())
            validateResponse(response.getBody());
        return response.getBody();
    }
    private void validateResponse(CapturedCardResponse capturedCardResponse) {
        if (capturedCardResponse == null) return;
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        capturedCardResponse.getResult().getItems().forEach(capturedCard -> {
            String bins;
            if (applicationUser.getBank() != null) {
                bins = applicationUser.getBank().getBinCodes().stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
                if (!bins.contains(capturedCard.getCardNumber().substring(0, 6)))
                    capturedCard.setCardHolderName(null);
            }
        });
    }
    public CaptureReasonsResponse getCaptureReasons(HttpServletResponse response) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetCapturedReasons");
        ResponseEntity<CaptureReasonsResponse> reasons = restTemplate.postForEntity(url, null, CaptureReasonsResponse.class);
        if (reasons.getBody() != null && !reasons.getBody().getSuccess()) {
            return null;
        }
        return reasons.getBody();

    }

    private Map<String, List<String>> setFieldsByPrivilege(List<String> projectionHeaders, List<String> projection) {
        List<String> privileges = getPrivilegesNamesByUserId();
        return filterProjectionList(projectionHeaders, projection, privileges);
    }

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.CAPT_CARD_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    private Map<String, List<String>> filterProjectionList(List<String> headers, List<String> projection, List<String> privileges) {
        List<String> projections = new ArrayList<>();
        int index = 0;
        List<String> headersTemp = new ArrayList<>();

        for (String proj : projection) {
            if (privileges.contains("CapturedCards_" + proj)) {
                projections.add(proj);
                if (headers != null)
                    headersTemp.add(headers.get(index));
            }
            index++;
        }
        if (headers != null) {
            headers.clear();
            headers.addAll(headersTemp);
        }
        projection.clear();
        projection.addAll(projections);
        Map<String, List<String>> returnedMap = new HashMap<>();
        returnedMap.put("headers", headers);
        returnedMap.put("fields", projection);
        return returnedMap;
    }

}
