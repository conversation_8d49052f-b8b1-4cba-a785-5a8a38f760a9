package com.sss.fatora.service.local;

import com.sss.fatora.domain.local.ActionRequestJSON;
import com.sss.fatora.domain.read.DTO.ReissueCardDTO;
import com.sss.fatora.service.converter.GenericConverterService;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ActionRequestJSONService extends GenericConverterService<ActionRequestJSON> {

    public ActionRequestJSON prepareRequestJsonObject(ActionRequestJSON actionRequestJSON, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(actionRequestJSON,excelIndexes,row);
        return actionRequestJSON;
    }
    public ActionRequestJSON prepareRequestLanguagueJsonObject(ActionRequestJSON actionRequestJSON, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyLanguageValueByExcel(actionRequestJSON,excelIndexes,row);
        return actionRequestJSON;
    }
    public ActionRequestJSON prepareRequestProductJsonObject(ActionRequestJSON actionRequestJSON, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        // getPropertyProductValueByExcel(actionRequestJSON,excelIndexes,row);
        return actionRequestJSON;
    }
    public ActionRequestJSON prepareReissueRequestJsonObject(ActionRequestJSON actionRequestJSON,ReissueCardDTO dto, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        actionRequestJSON.setReissueCardDTO(new ReissueCardDTO());
        getReissuePropertyValueByExcel(actionRequestJSON,actionRequestJSON.getReissueCardDTO(),excelIndexes,row);
        return actionRequestJSON;
    }
        public ActionRequestJSON prepareRenewRequestJsonObject(ActionRequestJSON actionRequestJSON,ReissueCardDTO dto, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        actionRequestJSON.setReissueCardDTO(new ReissueCardDTO());
        getRenewPropertyValueByExcel(actionRequestJSON,actionRequestJSON.getReissueCardDTO(),excelIndexes,row);
        return actionRequestJSON;
    }
}
