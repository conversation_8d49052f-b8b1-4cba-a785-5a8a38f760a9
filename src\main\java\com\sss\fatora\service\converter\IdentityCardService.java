package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.IdentityCard;
import com.sss.fatora.domain.converter.SecWord;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class IdentityCardService extends GenericConverterService<IdentityCard> {
    public IdentityCard prepareIdentityCardObject(IdentityCard identityCard, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(identityCard,excelIndexes,row);
        return identityCard;
    }

    public IdentityCard prepareIdentityCardObjectFromPanel(IdentityCard identityCard, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(identityCard,fixedValues);
        return identityCard;
    }
}
