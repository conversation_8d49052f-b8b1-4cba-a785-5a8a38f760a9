package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.ReadCardDao;
import com.sss.fatora.dao.read.ResponseCodeDao;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.ResponseCode;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("readingDBTransactionManager")
public class ResponseCodeService extends GenericReadService<ResponseCodeDao, ResponseCode, Long> {


    /**
     * This Function Return All Response Codes To Front-End By Global Data
     * */
    public List<ResponseCode> getAllResponseCodes() {
        try {
            return dao.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

}
