package com.sss.fatora.service.export;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "springBatchEntityManagerFactory",
        transactionManagerRef = "springBatchTransactionManager",
        basePackages = "com.sss.fatora.service.export"
)
public class BatchDataSourceConfig {
    @Autowired
    Environment env;

    @Bean(name = "springBatchDataSourceProperties")
    //@Primary
    @ConfigurationProperties("spring.batch.datasource")
    public DataSourceProperties springBatchDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "springBatchDataSource")
    @Primary
    public DataSource springBatchDataSource() {
        DataSourceProperties springBatchDataSourceProperties = springBatchDataSourceProperties();
        return DataSourceBuilder.create()
                .driverClassName(springBatchDataSourceProperties.getDriverClassName())
                .url(springBatchDataSourceProperties.getUrl())
                .username(springBatchDataSourceProperties.getUsername())
                .password(springBatchDataSourceProperties.getPassword())
                .build();
    }

    @Primary
    @Bean(name = "springBatchEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder springBatchEntityManagerFactoryBuilder) {

        Map<String, String> JpaProperties = new HashMap<>();

        JpaProperties.put("hibernate.format_sql", "true");
        JpaProperties.put("hibernate.show_sql", "true");
        JpaProperties.put("hibernate.hbm2ddl.auto", "none");
        JpaProperties.put("hibernate.dialect", "org.hibernate.dialect.H2Dialect");
        return springBatchEntityManagerFactoryBuilder
                .dataSource(springBatchDataSource())
                .packages("com.sss.fatora.service.export")
                //.persistenceUnit("secondaryDataSource")
                .properties(JpaProperties)
                .build();
    }

    // @Primary
    @Bean(name = "springBatchTransactionManager")
    public PlatformTransactionManager springBatchTransactionManager(
            @Qualifier("springBatchEntityManagerFactory") EntityManagerFactory springBatchEntityManagerFactory) {
        return new JpaTransactionManager(springBatchEntityManagerFactory);
    }

    @Bean("springBatchEntityManagerFactoryBuilder")
    public EntityManagerFactoryBuilder entityManagerFactoryBuilder() {
        return new EntityManagerFactoryBuilder(new HibernateJpaVendorAdapter(), new HashMap<>(), null);
    }
   /* @Bean
    public BatchConfigurer configurer(@Qualifier("springBatchDataSource") DataSource batchDataSource) {

        return new DefaultBatchConfigurer(batchDataSource);
    }*/
}
