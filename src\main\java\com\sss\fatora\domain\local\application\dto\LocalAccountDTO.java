package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalAccountDTO {

    @JsonProperty(value = "Account_Number")
    String accountNumber;
    @JsonProperty(value = "Account_Type")
    String accountType;
    @JsonProperty(value = "Account_Currency")
    String accountCurrency;
    @JsonProperty(value = "Is_ATM_Default")
    String isATMDefault;
    @JsonProperty(value = "Is_POS_Default")
    String isPOSDefault;
}
