package com.sss.fatora.domain.middleware.acquiring.AcqCustomers;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class GetCustomersWithFilterRequest extends AcqCustomer {
    private String bank;
    protected List<String> fields;
    protected String sorting;
    protected int maxResultCount;
    protected int skipCount;
}
