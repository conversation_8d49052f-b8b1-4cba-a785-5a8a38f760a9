package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.IdentityCard;
import com.sss.fatora.domain.converter.Person;
import com.sss.fatora.domain.converter.PersonName;
import com.sss.fatora.domain.converter.SecWord;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PersonServiceConverter extends GenericConverterService<Person>{
    @Autowired
    PersonNameService personNameService;
    @Autowired
    IdentityCardService identityCardService;
    public Person preparePersonObject(Person person, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(person,excelIndexes,row);
        person.setPerson_name(personNameService.preparePersonNameObject(new PersonName(),excelIndexes,row));
        person.setIdentity_card(identityCardService.prepareIdentityCardObject(new IdentityCard(),excelIndexes,row));
        return person;
    }

    public Person preparePersonObjectFromPanel(Person person, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(person,fixedValues);
        person.setPerson_name(personNameService.preparePersonNameObjectFromPanel(person.getPerson_name(),fixedValues));
        person.setIdentity_card(identityCardService.prepareIdentityCardObjectFromPanel(person.getIdentity_card(),fixedValues));
        return person;
    }
}
