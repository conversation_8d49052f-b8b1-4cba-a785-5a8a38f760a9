package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.AccountObject;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class AccountObjectService extends GenericConverterService<AccountObject> {
    public AccountObject prepareAccountObjectObject(AccountObject accountObject, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(accountObject,excelIndexes,row);
        return accountObject;
    }

    public AccountObject prepareAccountObjectObjectFromPanel(AccountObject accountObject, Map<String, String> fixedValues) throws IllegalAccessException {
        accountObject.setRef_id("card_1");
        getPropertyValueByObject(accountObject,fixedValues);
        return accountObject;
    }


    public AccountObject prepareAccountObjectObjectForChangePrimaryAccount(AccountObject accountObject, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(accountObject,fixedValues);
        return accountObject;
    }

    public AccountObject prepareAccountObjectForAccount(AccountObject accountObject, Map<String, String> fixedValues) throws IllegalAccessException {
        accountObject.setRef_id("card_1");
        getPropertyValueByObject(accountObject,fixedValues);
        return accountObject;
    }
}
