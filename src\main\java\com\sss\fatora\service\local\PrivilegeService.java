package com.sss.fatora.service.local;

import com.sss.fatora.dao.local.PrivilegeDao;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.service.generic.GenericService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("localDBTransactionManager")
public class PrivilegeService extends GenericService<PrivilegeDao, Privilege, Integer> {

    public List<Privilege> getAllPrivileges() {
         return dao.getAllPrivilegesByRecordStatusOrderByOrderItem(1);
    }

    /**
     * @param privilegesIds This Parameter Is A List Of Privilege Ids
     *                      <p>
     *                      This Function Return All The Privileges By List Of Privilege Ids
     */
    public List<Privilege> getPrivilegesByList(List<Integer> privilegesIds) {
        if (!privilegesIds.isEmpty()) {
            return dao.getPrivilegesByList(privilegesIds);
        } else {
            return dao.getAllPrivilegesByRecordStatusOrderByOrderItem(1);
        }
    }

    /**
     * @param userId        This Parameter Is The User Id
     * @param privilegeName This Parameter Is The Privilege Name
     *                      <p>
     *                      This Function Check If This User Has A Certain Privilege
     */
    public Boolean privilegeFoundByUserIdAndPrivilegeName(Integer userId, String privilegeName) {
        return dao.findByIdAndPrivilegeName(userId, privilegeName) != null;
    }


    /**
     * @param type This Parameter Is The Type Of Privileges {@link com.sss.fatora.utils.constants.PrivilegeType }
     * @param id   This Parameter Is The User Id
     *             <p>
     *             This Function Get A List Of Privileges By Privilege Type And User Id
     */
    public List<Privilege> getPrivilegesById(String type, Integer id) {
        return dao.getPrivilegesById(type, id);
    }
}
