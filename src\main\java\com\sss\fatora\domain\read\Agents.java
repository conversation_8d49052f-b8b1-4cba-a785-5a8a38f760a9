package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_AGENTS")
public class Agents {
    @Id
    @Column(name = "ID")
    Long id;
    @Column(name = "AGENT_NUMBER")
    String agentNumber;
    @Column(name = "AGENT_SHORT_DESC")
    String agentShortDesc;
    @Column(name = "AGENT_TYPE")
    String agentType;
    @Column(name = "PARENT_ID")
    Long parentId;
    @Column(name = "PARENT_AGENT_NUMBER")
    Long parentAgentNumber;
    @Column(name = "BANK_CODE")
    String bankCode;

}
