package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.Contact;
import com.sss.fatora.domain.converter.ContactData;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ContactService extends GenericConverterService<Contact>{
    final ContactDataService contactDataService;

    public ContactService(ContactDataService contactDataService) {
        this.contactDataService = contactDataService;
    }


    public Contact prepareContactObject(Contact contact, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(contact,excelIndexes,row);
        contact.setContact_data(contactDataService.prepareContactDataObject(new ContactData(),excelIndexes,row));
        return contact;
    }

    public Contact prepareContactObjectFromPanel(Contact contact, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(contact,fixedValues);
        contact.setContact_data(contactDataService.prepareContactDataObjectFromPanel(contact.getContact_data(),fixedValues));
        return contact;
    }

    public List<Contact> prepareContactObjectForChangeMobileNumber(List<Contact> contactList, Map<String, String> fixedValues) throws IllegalAccessException {
        for (Contact contact : contactList) {
            getPropertyValueByObject(contact,fixedValues);
            contact.setContact_data(contactDataService.prepareContactDataObjectForChangeMobileNumber(contact.getContact_data(),fixedValues));

        }
        return contactList;
    }

//
//    public List<Contact> prepareContactListFromExcel(List<Contact> contactList, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
//        for (Contact contact: contactList) {
//            getPropertyValueByExcel(contact,excelIndexes,row);
//            contact.setContact_data(contactDataService.prepareContactDataObject(new ContactData(),excelIndexes,row));
//
//        }
//        return contactList;
//    }

//    public List<Contact> prepareContactListFromPanel(List<Contact> contactList, Map<String, String> fixedValues) throws IllegalAccessException {
//
//        for (Contact contact : contactList) {
//            getPropertyValueByObject(contact, fixedValues);
//            contactDataService.prepareContactDataObjectFromPanel(contact.getContact_data(), fixedValues);
//        }
//        return contactList;
//
//    }
}
