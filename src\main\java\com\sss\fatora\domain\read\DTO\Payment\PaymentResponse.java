package com.sss.fatora.domain.read.DTO.Payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class PaymentResponse {
    @JsonProperty(value = "response_code")
    String responseCode;
    @JsonProperty(value = "response_description")
    String responseDescription;
    @JsonProperty(value = "terminalId")
    String terminalId;
    @JsonProperty(value = "description")
    Description description;

}
