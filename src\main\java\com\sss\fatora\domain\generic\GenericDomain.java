package com.sss.fatora.domain.generic;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.configuration.Config;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.security.model.CustomUserDetails;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
@DynamicUpdate
@NoArgsConstructor
@Setter
@Getter
public abstract class GenericDomain implements Serializable {

    @Column(name = "Record_Status")
    Integer recordStatus;


    //@JsonFormat(pattern = "yyyy-MM-dd")
    @Column(name = "Creation_Date", updatable = false)
    Date creationDate;


    @Column(name = "CreatorId", updatable = false)
    Integer creatorId;

//    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column(name = "Modified_Date")
    Date modifiedDate;

    @Column(name = "ModifierId")
    Integer modifierId;

    @PrePersist
    public void setDatePrePersist() {
        this.setCreationDate(new Date());
        if (CustomUserDetails.getCurrentInstance() != null)
            this.setCreatorId(CustomUserDetails.getCurrentInstance().getApplicationUser().getId());
    }

    @PreUpdate
    public void setDatePreUpdate() {
        this.setModifiedDate(new Date());
        if (CustomUserDetails.getCurrentInstance() != null)
            this.setModifierId(CustomUserDetails.getCurrentInstance().getApplicationUser().getId());
    }

    @JsonIgnore
    @Transient
    protected abstract <T extends GenericDao> Class<T> defineDao();

    public GenericDao evaluateEntityDao() {

        return Config.ApplicationContextHolder.getContext().getBean(defineDao());

    }
}
