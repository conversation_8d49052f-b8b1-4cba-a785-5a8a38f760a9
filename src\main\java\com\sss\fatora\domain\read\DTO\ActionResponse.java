package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
/**used for Manage Account and Close Card */
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class ActionResponse {
    @JsonProperty(value = "Success")
    Integer Success;
    @JsonProperty(value = "Error")
    String Error;
}
