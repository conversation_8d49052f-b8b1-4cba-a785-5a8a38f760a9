package com.sss.fatora.service.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.dao.local.ActionRequestDao;
import com.sss.fatora.domain.local.*;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.Products;
import com.sss.fatora.domain.read.DTO.ActionResponse;
import com.sss.fatora.domain.read.DTO.ReissueCardDTO;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.converter.ExcelFileService;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.service.read.CardStatusService;
import com.sss.fatora.service.read.ProductsService;
import com.sss.fatora.service.read.ReadAccountService;
import com.sss.fatora.service.read.ReadCardService;
import com.sss.fatora.service.write.CrefTabService;
import com.sss.fatora.utils.constants.*;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import org.apache.poi.ss.usermodel.Row;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sss.fatora.utils.constants.ActionType.*;

@Service
@Transactional("localDBTransactionManager")
public class ActionRequestService extends GenericService<ActionRequestDao, ActionRequest, Long> {

    @Autowired
    ReadCardService readCardService;
    @Autowired
    ReadAccountService readAccountService;
    @Autowired
    CrefTabService crefTabService;
    @Autowired
    ApplicationUserService applicationUserService;
    @Autowired
    CardStatusService cardStatusService;
    @Autowired
    LogService logService;
    @Autowired
    ExcelFileService excelFileService;
    @Autowired
    ActionRequestJSONService actionRequestJSONService;
    @Autowired
    RequestsLogService requestsLogService;
    @Autowired
    ProductsService productsService;

    /**
     * @param request This Parameter Is For The Information To Be Saved
     *                <p>
     *                This Function Is For Saving The Action Request In Db
     */

    public ActionRequest saveRequest(ActionRequest request) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Date date = new Date();
            RequestsLog requestsLog = new RequestsLog();
            if (request.getActionType().equalsIgnoreCase(CHANGE_PRODUCT.getAction())) {
                setProductOldValue(request);
            }
            String jsonActionRequestDetails = objectMapper.writeValueAsString(request.getActionRequest());
            request.setActionDetails(jsonActionRequestDetails);
            request.setActionStatus(ActionStatus.PENDING.getType());
            request.setUserRequestStatus(UserRequestStatus.PENDING.getType());
            request.setApplicationUser(CustomUserDetails.getCurrentInstance().getApplicationUser());
            request.setRequestDate(date);
            request.setModifiedDate(date);
            saveLog(request);
            ActionRequest savedActionRequest = dao.save(request);
            requestsLog.setAction(RequestsActionsEnum.CREATE.getStringValue());
            requestsLog.setRequestId(savedActionRequest.getId().toString());
            requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());
            requestsLogService.merge(requestsLog);
            return savedActionRequest;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return null;
    }

    private void setProductOldValue(ActionRequest request) {
        String productOldName = request.getActionRequest().getOldValue();
        Long productOldNumber = productsService.getProductNumberByLabel(productOldName);
        String oldValue = "";
        if (productOldNumber != null)
            oldValue = productOldNumber.toString();
        oldValue += "- " + productOldName;
        request.getActionRequest().setOldValue(oldValue);
    }

    public void saveLog(ActionRequest request) {
        try {
            com.sss.fatora.domain.local.Log log = new com.sss.fatora.domain.local.Log();
            log.setRecordStatus(1);
            log.setType(LogType.Actions.getType());
            log.setAction(request.getActionType());
            log.setCreatorId(CustomUserDetails.getCurrentInstance().getApplicationUser().getCreatorId());
            log.setCreationDate(new Date());
            log.setRelatedEntity(new CardDataVW().getRelatedEntity());
            log.setEntityId(request.getCardNumber());
            log.setApplicationUser(CustomUserDetails.getCurrentInstance().getApplicationUser());
            log.setBank(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank());

            if (request.getActionType().equalsIgnoreCase(CHANGE_STATUS.getAction())) {
                log.setActionValue(request.getActionRequest().getStatus().toString());
                log.setOldValue(request.getActionRequest().getOldValue());
            } else if (request.getActionType().equalsIgnoreCase(CARD_RESET.getAction())) {
                log.setActionValue("0");
                log.setOldValue(request.getActionRequest().getOldValue());
            } else if (!request.getActionType().equalsIgnoreCase(MANAGE_ACCOUNTS.getAction())) {
                log.setActionValue(request.getActionRequest().getNewValue());
                log.setOldValue(request.getActionRequest().getOldValue());
            }

            logService.merge(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param actionRequest   This Parameter Is For The Value Of Filters Send From
     *                        The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To
     *                        Search In
     * @param filterOperator  There Is An Specific Operator For Every Filter
     *                        {@link Operator}
     * @param fullname        This Parameter Is Added To Where Clause
     *                        <p>
     *                        This Function Add Certain Conditions To Where Clause
     *                        That Can't Be Added To The Filter (ActionRequest)
     *                        Directly And Then Request The Dynamic Search Function
     *                        ( dynamicSearch() )
     */

    public Page<ActionRequest> search(ActionRequest actionRequest, Long fromDate, Long toDate, Long fromModifiedDate,
            Long toModifiedDate,
            Long fromApprovalDate, Long toApprovalDate, Pagination pagination, Map<String, Operator> filterOperator,
            Integer userId)
            throws NoSuchFieldException, IllegalAccessException, IntrospectionException, InvocationTargetException {
        Page<ActionRequest> actionRequests;
        String additionalConstraint = "";
        String conditionForJoin = "";
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();

        if (actionRequest == null)
            actionRequest = new ActionRequest();

        if (fromApprovalDate != null || toApprovalDate != null) {
            if (fromApprovalDate == null)
                fromApprovalDate = new Date(1612178075113L).getTime();
            if (toApprovalDate == null)
                toApprovalDate = new Date(7258118400L * 1000).getTime();
            additionalConstraint = "ActionRequest.approvalDate between '" + f.format(fromApprovalDate) + "' AND '"
                    + f.format(toApprovalDate) + "'";
        }
        if (fromModifiedDate != null || toModifiedDate != null) {
            if (fromModifiedDate == null)
                fromModifiedDate = new Date(1612178075113L).getTime();
            if (toModifiedDate == null)
                toModifiedDate = new Date(7258118400L * 1000).getTime();
            additionalConstraint = "ActionRequest.modifiedDate between '" + f.format(fromModifiedDate) + "' AND '"
                    + f.format(toModifiedDate) + "'";
        }
        if (fromModifiedDate == null && toModifiedDate == null && fromApprovalDate == null && toApprovalDate == null) {
            if (fromDate == null) {
                fromDate = new Date(1612178075113L).getTime();
            }
            if (toDate == null) {
                toDate = new Date(7258118400L * 1000).getTime();
            }
            additionalConstraint = "ActionRequest.requestDate between '" + f.format(fromDate) + "' AND '"
                    + f.format(toDate) + "'";
        } else if (fromDate != null || toDate != null) {
            if (fromDate == null) {
                fromDate = new Date(1612178075113L).getTime();
            }
            if (toDate == null) {
                toDate = new Date(7258118400L * 1000).getTime();
            }
            additionalConstraint += " And ActionRequest.requestDate between '" + f.format(fromDate) + "' AND '"
                    + f.format(toDate) + "'";
        }
        // if (CustomUserDetails.getCurrentInstance().getApplicationUser().getBank() !=
        // null) {
        // actionRequest.setProductNumber(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank().getCode()
        // + "*");
        // filterOperator.put("productNumber", Operator.CONTAINS_WITH_STAR);
        // }

        if (userId != null) {
            conditionForJoin = " AND ApplicationUser.id = '" + userId + "' ";
            additionalConstraint += conditionForJoin;
        }

        if (actionRequest.getUserRequestStatus() != null
                && actionRequest.getUserRequestStatus().equalsIgnoreCase("Processed")) {
            additionalConstraint += " AND (ActionRequest.userRequestStatus IN ('Approved','Rejected'))";
            actionRequest.setUserRequestStatus(null);
        }
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("ActionRequest.creationDate");
            pagination.setOrderType("DESC");
        }

        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            List<Integer> users = applicationUserService.getAllUsersByBankId(applicationUser.getBank().getId())
                    .stream()
                    .map(ApplicationUser::getId)
                    .collect(Collectors.toList());
            additionalConstraint += " AND ( ActionRequest.applicationUser IN "
                    + users.toString().replace("[", "(").replace("]", ")") +
                    " OR " + getRequestsByUserBankBins(applicationUser) + " ) ";
        }

        actionRequests = this.dynamicSearch(actionRequest, pagination, additionalConstraint, filterOperator, null,false);

        return actionRequests;

    }

    /**
     * This Function Is For Adding A Join Statement To The Query
     */
    public String addJoinToQuery() {
        String join = " LEFT JOIN ApplicationUser AS ApplicationUser ON ApplicationUser.id = ActionRequest.applicationUser ";
        return join;
    }
        public String addAcquiringJoinToQuery() {
        String join = " LEFT JOIN ApplicationUser AS ApplicationUser ON ApplicationUser.id = AcquiringActionRequest.applicationUser ";
        return join;
    }

    /**
     * @param actionRequest This Parameter Is For The Information To Be Sent To The
     *                      Respective Service
     *                      <p>
     *                      This Function Is For Approving The Action Sent And
     *                      Request A Specific Service By Action Type
     */
    public Object approveRequest(List<Long> requestIds) {
        Object resp = null;
        // List<Object> responseList = new ArrayList<>();
        for (Long actionRequestId : requestIds) {
            ObjectMapper objectMapper = new ObjectMapper();
            RequestsLog requestsLog = new RequestsLog();
            ActionRequest actionRequestItr = dao.getRequestById(actionRequestId);
            try {
                ActionRequestJSON j = objectMapper.readValue(actionRequestItr.getActionDetails(),
                        ActionRequestJSON.class);
                actionRequestItr.setActionRequest(j);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            String type = actionRequestItr.getActionType();
            if (type.equalsIgnoreCase(CHANGE_PRODUCT.getAction())) {
                ResponseEntity<ActionResponse> response = readCardService.changeProduct(actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getNewValue(),
                        Long.valueOf(actionRequestItr.getActionRequest().getProductID()));
                if (response != null)
                    resp = response.getBody();
            } else if (type.equalsIgnoreCase(CHANGE_MOBILE_NUMBER.getAction())) {
                ResponseEntity<ActionResponse> response = readCardService.changeMobileNumber(actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        actionRequestItr.getActionRequest().getNewValue(),
                        actionRequestItr.getActionRequest().getOldValue());
                if (response != null)
                    resp = response.getBody();
            } else if (type.equalsIgnoreCase(CHANGE_STATUS.getAction())) {
                ResponseEntity<ActionResponse> response = readAccountService.changeStatus(actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        actionRequestItr.getActionRequest().getStatus(),
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getStatus().toString());
                if (response != null)
                    resp = response.getBody();
            } else if (type.equalsIgnoreCase(VALIDATE_CARD.getAction())) {
                ResponseEntity<ActionResponse> response = readAccountService.validateCard(actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getNewValue());
                if (response != null)
                    resp = response.getBody();
            }

            else if (type.equalsIgnoreCase(CARD_RESET.getAction())) {
                String newValue = "0";
                ResponseEntity<ActionResponse> response = readAccountService.resetPINCount(actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        actionRequestItr.getActionRequest().getOldValue(), newValue);
                if (response != null)
                    resp = response.getBody();

            } else if (type.equalsIgnoreCase(CHANGE_LANGUAGE.getAction())) {
                ResponseEntity<ActionResponse> response = readCardService.changeLanguage(
                        actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getNewValue());
                if (response != null)
                    resp = response.getBody();
            } else if (type.equalsIgnoreCase(Close_Card.getAction())) {
                ResponseEntity<ActionResponse> response = readCardService.closeCard(actionRequestItr.getId(),
                        actionRequestItr
                                .getCardNumber(),
                        actionRequestItr.getCardNumber());
                if (response != null)
                    resp = response.getBody();

            } else if (type.equalsIgnoreCase(MANAGE_ACCOUNTS.getAction())) {
                ResponseEntity<ActionResponse> response = readAccountService.manageAccounts(actionRequestItr
                        .getActionRequest().getAccountDTO().getCard_Number(),
                        actionRequestItr.getActionRequest().getAccountDTO(),
                        actionRequestItr.getId()

                );
                if (response != null)
                    resp = response.getBody();

            } else if (type.equalsIgnoreCase(REISSUE_CARD.getAction())) {
                ActionRequestJSON details = actionRequestItr.getActionRequest();

                String expiryDate = details.getReissueCardDTO().getExpiry_Date();
                String deliveryBranch = details.getReissueCardDTO().getDelivery_Branch();
                int instantCard = details.getReissueCardDTO().getInstant_Card();
                int oldExpiry = details.getReissueCardDTO().getOld_Expiry();
                int oldCardNumber = details.getReissueCardDTO().getOld_Number();

                ResponseEntity<ActionResponse> response = readCardService.reissuecard(
                        actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        expiryDate,
                        deliveryBranch,
                        instantCard,
                        oldExpiry,
                        oldCardNumber,
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getNewValue()

                );

                if (response != null)
                    resp = response.getBody();
            } else if (type.equalsIgnoreCase(RENEW_CARD.getAction())) {
                ActionRequestJSON details = actionRequestItr.getActionRequest();

                String expiryDate = details.getReissueCardDTO().getExpiry_Date();
                String deliveryBranch = details.getReissueCardDTO().getDelivery_Branch();
                int instantCard = details.getReissueCardDTO().getInstant_Card();
                int oldExpiry = details.getReissueCardDTO().getOld_Expiry();
                int oldCardNumber = details.getReissueCardDTO().getOld_Number();

                ResponseEntity<ActionResponse> response = readCardService.reissuecard(
                        actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        expiryDate,
                        deliveryBranch,
                        instantCard,
                        oldExpiry,
                        oldCardNumber,
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getNewValue()

                );

                if (response != null)
                    resp = response.getBody();
            } else if (type.equalsIgnoreCase(INSTANT_REISSUE_CARD.getAction())) {
                ActionRequestJSON details = actionRequestItr.getActionRequest();

                String expiryDate = details.getReissueCardDTO().getExpiry_Date();
                String deliveryBranch = details.getReissueCardDTO().getDelivery_Branch();
                int oldExpiry = details.getReissueCardDTO().getOld_Expiry();
                int oldCardNumber = details.getReissueCardDTO().getOld_Number();

                int instantCard = 1;

                ResponseEntity<ActionResponse> response = readCardService.instantReissuecard(
                        actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        expiryDate,
                        deliveryBranch,
                        instantCard,
                        oldExpiry,
                        oldCardNumber,
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getNewValue());

                if (response != null)
                    resp = response.getBody();
            } else if (type.equalsIgnoreCase(INSTANT_RENEW_CARD.getAction())) {
                ActionRequestJSON details = actionRequestItr.getActionRequest();

                String expiryDate = null;
                String deliveryBranch = details.getReissueCardDTO().getDelivery_Branch();
                int oldExpiry = details.getReissueCardDTO().getOld_Expiry();
                int oldCardNumber = details.getReissueCardDTO().getOld_Number();

                int instantCard = 1;

                ResponseEntity<ActionResponse> response = readCardService.instantReissuecard(
                        actionRequestItr.getId(),
                        actionRequestItr.getCardNumber(),
                        expiryDate,
                        deliveryBranch,
                        instantCard,
                        oldExpiry,
                        oldCardNumber,
                        actionRequestItr.getActionRequest().getOldValue(),
                        actionRequestItr.getActionRequest().getNewValue());

                if (response != null)
                    resp = response.getBody();
            }

            actionRequestItr = getById(actionRequestItr.getId());
            if (resp != null && resp instanceof ActionResponse) {
                System.out.println("**resp instanceof ActionResponse**");
                if (((ActionResponse) resp).getSuccess() == 1)
                    actionRequestItr.setActionStatus(ActionStatus.SUCCESSES.getType());
                else
                    actionRequestItr.setActionStatus(ActionStatus.FAILED.getType());
            } else if ((resp != null && resp instanceof SoapResponse)) {
                System.out.println("**resp instanceof SoapResponse**");
                actionRequestItr.setActionStatus(ActionStatus.SUCCESSES.getType());
            } else {
                actionRequestItr.setActionStatus(ActionStatus.FAILED.getType());
            }
            if (actionRequestItr.getErrorMessage() != null
                    && actionRequestItr.getActionStatus().equalsIgnoreCase(ActionStatus.SUCCESSES.getType())) {
                System.out.println("**Success Request With Error Message**");
                System.out.println("Date : " + new Date());
                System.out.println("**************************************");
            }
            if (actionRequestItr.getUserRequestStatus().equalsIgnoreCase(UserRequestStatus.PENDING.getType()))
                requestsLog.setAction(RequestsActionsEnum.APPROVE.getStringValue());
            // else
            // requestsLog.setAction(RequestsActionsEnum.REPROCESS.getStringValue());
            actionRequestItr.setUserRequestStatus(UserRequestStatus.APPROVED.getType());
            actionRequestItr.setApprovalDate(new Date());

            ActionRequest savedActionRequest = dao.save(actionRequestItr);
            requestsLog.setRequestId(savedActionRequest.getId().toString());
            requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());

            try {
                requestsLogService.merge(requestsLog);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
            actionRequestItr.setApplicationUser(CustomUserDetails.getCurrentUser());
        }
        return resp;
    }

    /**
     * @param requestIds This Parameter Indicates To The Requests To Be Rejected
     *                   <p>
     *                   This Function Is For Reject The Action Sent
     */
    public Boolean rejectRequest(List<Long> requestIds) {
        String userRequestStatus = UserRequestStatus.DENIED.getType();
        String actionStatus = ActionStatus.USER_REQUEST_DENIED.getType();
        // actionRequest.setUserRequestStatus(UserRequestStatus.DENIED.getType());
        // actionRequest.setActionStatus(ActionStatus.USER_REQUEST_DENIED.getType());
        for (Long requestId : requestIds) {
            RequestsLog requestsLog = new RequestsLog();
            requestsLog.setAction(RequestsActionsEnum.REJECT.getStringValue());
            requestsLog.setRequestId(requestId.toString());
            requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());
            try {
                requestsLogService.merge(requestsLog);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
        dao.rejectRequest(requestIds, userRequestStatus, actionStatus, CustomUserDetails.getCurrentUser().getId(),
                new Date());
        return true;
    }

    public void saveErrorMessage(Long actionRequestID, String Error) {
        try {

            ActionRequest actionRequest = getById(actionRequestID.longValue());
            actionRequest.setErrorMessage(Error);
            merge(actionRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Boolean archive(List<Long> requestIds, Boolean status) {
        try {
            for (Long requestId : requestIds) {
                RequestsLog requestsLog = new RequestsLog();
                requestsLog.setRequestId(requestId.toString());
                requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());
                if (status)
                    requestsLog.setAction(RequestsActionsEnum.ARCHIVE.getStringValue());
                else
                    requestsLog.setAction(RequestsActionsEnum.MOVE_TO_PROCESS.getStringValue());
                requestsLogService.merge(requestsLog);
            }
            dao.archive(status, requestIds, new Date(), CustomUserDetails.getCurrentUser().getId());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public ActionRequest prepareRequestObject(ActionRequest request, Map<String, Integer> excelIndexes, Row row,
            String type) {
        try {
            getPropertyValueByExcel(request, excelIndexes, row);
            if (type.equalsIgnoreCase("change")) {
                ObjectMapper objectMapper = new ObjectMapper();
                ActionRequestJSON actionRequestJSON = new ActionRequestJSON();
                String jsonActionRequestDetails = objectMapper.writeValueAsString(
                        actionRequestJSONService.prepareRequestJsonObject(actionRequestJSON, excelIndexes, row));
                request.setActionDetails(jsonActionRequestDetails);
            }
            if (type.equalsIgnoreCase("reissue") || type.equalsIgnoreCase("instant-reissue")) {

                ObjectMapper objectMapper = new ObjectMapper();
                ActionRequestJSON actionRequestJSON = new ActionRequestJSON();
                String jsonActionRequestDetails = objectMapper.writeValueAsString(
                        actionRequestJSONService.prepareReissueRequestJsonObject(actionRequestJSON,
                                actionRequestJSON.getReissueCardDTO(), excelIndexes, row));
                request.setActionDetails(jsonActionRequestDetails);
            }
            if (type.equalsIgnoreCase("renew") || type.equalsIgnoreCase("instant-renew")) {

                ObjectMapper objectMapper = new ObjectMapper();
                ActionRequestJSON actionRequestJSON = new ActionRequestJSON();
                String jsonActionRequestDetails = objectMapper.writeValueAsString(
                        actionRequestJSONService.prepareRenewRequestJsonObject(actionRequestJSON,
                                actionRequestJSON.getReissueCardDTO(), excelIndexes, row));
                request.setActionDetails(jsonActionRequestDetails);
            }
            if (type.equalsIgnoreCase("change-language")) {
                ObjectMapper objectMapper = new ObjectMapper();
                ActionRequestJSON actionRequestJSON = new ActionRequestJSON();
                String jsonActionRequestDetails = objectMapper.writeValueAsString(
                        actionRequestJSONService.prepareRequestLanguagueJsonObject(actionRequestJSON, excelIndexes,
                                row));
                request.setActionDetails(jsonActionRequestDetails);
            }
            if (type.equalsIgnoreCase("change-product")) {
                ObjectMapper objectMapper = new ObjectMapper();
                ActionRequestJSON actionRequestJSON = new ActionRequestJSON();
                // String jsonActionRequestDetails = objectMapper.writeValueAsString(
                // actionRequestJSONService.prepareRequestProductJsonObject(actionRequestJSON,
                // excelIndexes, row));
                request.setActionDetails(objectMapper.writeValueAsString(actionRequestJSON));
            }
        } catch (IllegalAccessException | JsonProcessingException e) {
            e.printStackTrace();
        }
        return request;
    }

    public List<ActionRequest> saveRequestsExcelToDatabase(TempContentModel tempContentModel, String type)
            throws Exception {
        List<ActionRequest> actionRequestList = excelFileService.convertRequestsExcelToDatabase(tempContentModel, type);
        List<ActionRequest> actionRequestFinalList = new ArrayList<>();
        List<String> cardNumbers = new ArrayList<>();

        for (ActionRequest actionRequest : actionRequestList) {
            CardDataVW relatedCard = readCardService.getCardByNumber(actionRequest.getCardNumber());

            if (relatedCard == null) {
                throw new Exception("This Card Number " + actionRequest.getCardNumber()
                        + " does not exist please check and reupload file");
            } else {
                ActionRequestJSON actionRequestJSON = new ActionRequestJSON();

                if (type.equalsIgnoreCase(BulkTemplateType.Close_Card.getType())) {
                    actionRequestJSON.setNewValue(actionRequest.getCardNumber());
                    actionRequest.setActionType(Close_Card.getAction());
                } else if (type.equalsIgnoreCase(BulkTemplateType.Validate_Card.getType())) {
                    actionRequestJSON.setNewValue("0");
                    actionRequestJSON.setOldValue(relatedCard.getStatus().toString());
                    actionRequest.setActionType(VALIDATE_CARD.getAction());
                } else if (type.equalsIgnoreCase(BulkTemplateType.Reissue_Card.getType())) {
                    actionRequest.setActionType(REISSUE_CARD.getAction());

                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        actionRequestJSON = objectMapper.readValue(actionRequest.getActionDetails(),
                                ActionRequestJSON.class);

                        ReissueCardDTO reissueCardDTO = actionRequestJSON.getReissueCardDTO();
                        if (reissueCardDTO != null) {
                            reissueCardDTO.setCard_Number(actionRequest.getCardNumber());
                            String expiryDate = reissueCardDTO.getExpiry_Date();
                            String deliveryBranch = reissueCardDTO.getDelivery_Branch();
                            int instantCard = reissueCardDTO.getInstant_Card();
                            int oldExpiry = reissueCardDTO.getOld_Expiry();
                            int oldCardNumber = reissueCardDTO.getOld_Number();

                            ResponseEntity<ActionResponse> response = readCardService.reissuecard(
                                    actionRequest.getId(),
                                    reissueCardDTO.getCard_Number(),
                                    expiryDate,
                                    deliveryBranch,
                                    instantCard,
                                    oldExpiry,
                                    oldCardNumber,
                                    actionRequestJSON.getOldValue(),
                                    actionRequestJSON.getNewValue());
                        }
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                        throw new Exception("Error processing action details for Reissue Card", e);
                    }
                } else if (type.equalsIgnoreCase(BulkTemplateType.Instant_Reissue_Card.getType())) {
                    actionRequest.setActionType(INSTANT_REISSUE_CARD.getAction());

                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        actionRequestJSON = objectMapper.readValue(actionRequest.getActionDetails(),
                                ActionRequestJSON.class);

                        ReissueCardDTO reissueCardDTO = actionRequestJSON.getReissueCardDTO();
                        if (reissueCardDTO != null) {
                            reissueCardDTO.setCard_Number(actionRequest.getCardNumber());
                            String expiryDate = reissueCardDTO.getExpiry_Date();
                            String deliveryBranch = reissueCardDTO.getDelivery_Branch();
                            reissueCardDTO.setInstant_Card(1);
                            int oldExpiry = reissueCardDTO.getOld_Expiry();
                            int oldCardNumber = reissueCardDTO.getOld_Number();

                            ResponseEntity<ActionResponse> response = readCardService.reissuecard(
                                    actionRequest.getId(),
                                    reissueCardDTO.getCard_Number(),
                                    expiryDate,
                                    deliveryBranch,
                                    reissueCardDTO.getInstant_Card(),
                                    oldExpiry,
                                    oldCardNumber,
                                    actionRequestJSON.getOldValue(),
                                    actionRequestJSON.getNewValue());
                        }
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                        throw new Exception("Error processing action details for Instant Reissue Card", e);
                    }
                } else if (type.equalsIgnoreCase(BulkTemplateType.Renew_Card.getType())) {
                    actionRequest.setActionType(RENEW_CARD.getAction());

                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        actionRequestJSON = objectMapper.readValue(actionRequest.getActionDetails(),
                                ActionRequestJSON.class);

                        ReissueCardDTO reissueCardDTO = actionRequestJSON.getReissueCardDTO();
                        if (reissueCardDTO != null) {
                            reissueCardDTO.setCard_Number(actionRequest.getCardNumber());
                            String expiryDate = reissueCardDTO.getExpiry_Date();
                            String deliveryBranch = reissueCardDTO.getDelivery_Branch();
                            int instantCard = reissueCardDTO.getInstant_Card();
                            reissueCardDTO.setOld_Expiry(0);
                            reissueCardDTO.setOld_Number(0);

                            ResponseEntity<ActionResponse> response = readCardService.renewcard(
                                    actionRequest.getId(),
                                    reissueCardDTO.getCard_Number(),
                                    expiryDate,
                                    deliveryBranch,
                                    instantCard,
                                    reissueCardDTO.getOld_Expiry(),
                                    reissueCardDTO.getOld_Number(),
                                    actionRequestJSON.getOldValue(),
                                    actionRequestJSON.getNewValue());
                        }
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                        throw new Exception("Error processing action details for Renew Card", e);
                    }
                } else if (type.equalsIgnoreCase(BulkTemplateType.Instant_Renew_Card.getType())) {
                    actionRequest.setActionType(INSTANT_RENEW_CARD.getAction());

                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        actionRequestJSON = objectMapper.readValue(actionRequest.getActionDetails(),
                                ActionRequestJSON.class);

                        ReissueCardDTO reissueCardDTO = actionRequestJSON.getReissueCardDTO();
                        if (reissueCardDTO != null) {
                            reissueCardDTO.setCard_Number(actionRequest.getCardNumber());
                            String expiryDate = reissueCardDTO.getExpiry_Date();
                            String deliveryBranch = reissueCardDTO.getDelivery_Branch();
                            reissueCardDTO.setInstant_Card(1);
                            reissueCardDTO.setOld_Expiry(0);
                            reissueCardDTO.setOld_Number(0);

                            ResponseEntity<ActionResponse> response = readCardService.renewcard(
                                    actionRequest.getId(),
                                    reissueCardDTO.getCard_Number(),
                                    expiryDate,
                                    deliveryBranch,
                                    reissueCardDTO.getInstant_Card(),
                                    reissueCardDTO.getOld_Expiry(),
                                    reissueCardDTO.getOld_Number(),
                                    actionRequestJSON.getOldValue(),
                                    actionRequestJSON.getNewValue());
                        }
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                        throw new Exception("Error processing action details for Renew Card", e);
                    }
                } else if (type.equalsIgnoreCase(BulkTemplateType.Change_Language.getType())) {
                    // actionRequestJSON.setNewValue(actionRequest.);
                    ObjectMapper objectMapper = new ObjectMapper();

                    ActionRequestJSON parsedJson = objectMapper.readValue(actionRequest.getActionDetails(),
                            ActionRequestJSON.class);
                    // actionRequest.setActionRequest(parsedJson);

                    actionRequest.getId();
                    actionRequest.getCardNumber();
                    actionRequestJSON.setOldValue(relatedCard.getLangCode());
                    actionRequestJSON.setNewValue(parsedJson.getNewValue());

                    actionRequest.setActionType(CHANGE_LANGUAGE.getAction());

                } else if (type.equalsIgnoreCase(BulkTemplateType.Change_Product.getType())) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        ActionRequestJSON parsedJson = objectMapper.readValue(actionRequest.getActionDetails(),
                                ActionRequestJSON.class);
                        // actionRequest.setActionRequest(parsedJson);

                        actionRequest.getId();
                        actionRequest.getCardNumber();
                        actionRequestJSON.setOldValue(relatedCard.getProduct());
                        actionRequestJSON.setNewValue(parsedJson.getNewValue());
                        // actionRequestJSON.setProductID(parsedJson.getProductID()); ;

                        actionRequest.setActionType(CHANGE_PRODUCT.getAction());

                    } catch (Exception e) {
                        e.getMessage();
                    }
                }

                else {
                    try {
                        actionRequest.setActionType(CHANGE_STATUS.getAction());
                        JSONObject jsonObject = new JSONObject(actionRequest.getActionDetails());
                        Integer cdStat = cardStatusService.getStatusByDesc(jsonObject.get("newValue").toString());
                        if (cdStat == null)
                            throw new Exception("This Card Number " + actionRequest.getCardNumber()
                                    + " does not have status please check and reupload file");
                        actionRequestJSON.setOldValue(relatedCard.getStatus().toString());
                        actionRequestJSON.setExpDate(relatedCard.getExpDate());
                        actionRequestJSON.setNewValue(cdStat.toString());
                        actionRequestJSON.setStatus(cdStat);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }

                actionRequest.setActionRequest(actionRequestJSON);
                if (!cardNumbers.contains(actionRequest.getCardNumber())) {
                    cardNumbers.add(actionRequest.getCardNumber());
                    actionRequestFinalList.add(actionRequest);
                } else {
                    throw new Exception("This Card Number " + actionRequest.getCardNumber()
                            + " is duplicated please check and reupload file");
                }
            }
        }

        // to add the first one in excel file lastly so they would show in the same
        // order as the file
        Collections.reverse(actionRequestFinalList);

        for (ActionRequest actionRequest : actionRequestFinalList) {
            saveRequest(actionRequest);
        }

        return actionRequestFinalList;
    }

    public List<ActionRequest> saveChangeProductRequestsExcelToDatabase(TempContentModel tempContentModel, String type,
            Long productID)
            throws Exception {
        List<ActionRequest> actionRequestList = excelFileService.convertRequestsExcelToDatabase(tempContentModel, type);
        List<ActionRequest> actionRequestFinalList = new ArrayList<>();
        List<String> cardNumbers = new ArrayList<>();

        for (ActionRequest actionRequest : actionRequestList) {
            CardDataVW relatedCard = readCardService.getCardByNumber(actionRequest.getCardNumber());

            if (relatedCard == null) {
                throw new Exception("This Card Number " + actionRequest.getCardNumber()
                        + " does not exist please check and reupload file");
            } else {
                ActionRequestJSON actionRequestJSON = new ActionRequestJSON();

                if (type.equalsIgnoreCase(BulkTemplateType.Change_Product.getType())) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        ActionRequestJSON parsedJson = objectMapper.readValue(actionRequest.getActionDetails(),
                                ActionRequestJSON.class);
                        // actionRequest.setActionRequest(parsedJson);
                        Products product = productsService.getProductById(productID);
                        actionRequest.getId();
                        actionRequest.getCardNumber();
                        actionRequestJSON.setOldValue(relatedCard.getProduct());
                        actionRequestJSON.setNewValue(product.getLabel());
                        actionRequestJSON.setProductID(Integer.parseInt(productID.toString()));

                        actionRequest.setActionType(CHANGE_PRODUCT.getAction());

                    } catch (Exception e) {
                        e.getMessage();
                    }
                }

                actionRequest.setActionRequest(actionRequestJSON);
                if (!cardNumbers.contains(actionRequest.getCardNumber())) {
                    cardNumbers.add(actionRequest.getCardNumber());
                    actionRequestFinalList.add(actionRequest);
                } else {
                    throw new Exception("This Card Number " + actionRequest.getCardNumber()
                            + " is duplicated please check and reupload file");
                }
            }
        }

        // to add the first one in excel file lastly so they would show in the same
        // order as the file
        Collections.reverse(actionRequestFinalList);

        for (ActionRequest actionRequest : actionRequestFinalList) {
            saveRequest(actionRequest);
        }

        return actionRequestFinalList;
    }

    public List<ActionRequest> reprocessActionRequest(List<ActionRequest> actionRequest) {
        try {
            for (ActionRequest actionRequestItr : actionRequest) {
                actionRequestItr.setUserRequestStatus(UserRequestStatus.PENDING.getType());
                actionRequestItr.setActionStatus(ActionStatus.PENDING.getType());
                // actionRequestItr.setRequestDate(new Date());
                actionRequestItr.setApprovalDate(null);
                actionRequestItr.setErrorMessage(null);
                // actionRequestItr.setModifiedDate(new Date());
                actionRequestItr.setApplicationUser(CustomUserDetails.getCurrentUser());
                ActionRequest savedRequest = this.merge(actionRequestItr);
                RequestsLog requestsLog = new RequestsLog();
                requestsLog.setRequestId(savedRequest.getId().toString());
                requestsLog.setAction(RequestsActionsEnum.REPROCESS.getStringValue());
                requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());
                requestsLogService.merge(requestsLog);
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return actionRequest;
    }

    private String getRequestsByUserBankBins(ApplicationUser applicationUser) {
        String additionalConstraint = "";
        String BinString = applicationUser.getBank().getBin();
        String[] Bins = BinString.split(",");
        for (int BinCount = 0; BinCount < Bins.length; BinCount++) {
            String Bin = Bins[BinCount];
            if (BinCount < Bins.length - 1)
                additionalConstraint += "ActionRequest.cardNumber like  '" + Bin + "%' or ";
            else
                additionalConstraint += "ActionRequest.cardNumber like  '" + Bin + "%' ";
        }
        return additionalConstraint;
    }
}
