package com.sss.fatora.domain.middleware.terminals.dto;

import com.sss.fatora.domain.middleware.Address;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class TerminalDto {
    //First Stage
    protected Integer customerId;
    protected String  bank;
    protected String  customerNumber;
    protected String  customerName;
    protected String  branchName;
    protected String  branchNumber;
    //Second Stage
    protected Integer merchantId;
    protected String  merchantName;
    protected String  merchantNumber;
    protected String  mcc;
    protected String  merchantEmail;
    protected Address merchantAddress;
    @NotNull(message = "account number must not be null")
    @NotBlank(message = "account number must not be blank")
    protected String  accountNumber;
    @NotNull(message = "account type must not be null")
    @NotBlank(message = "account type must not be blank")
    protected String  accountType;

}
