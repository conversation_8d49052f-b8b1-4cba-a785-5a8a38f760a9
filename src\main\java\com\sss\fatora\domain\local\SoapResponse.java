package com.sss.fatora.domain.local;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.sss.fatora.domain.converter.GenericConverter;
import com.sss.fatora.service.converter.GenericConverterService;

@JacksonXmlRootElement(localName = "soap:Envelope")
public class SoapResponse extends GenericConverter {

    Body body;

    @JacksonXmlProperty(localName = "Body")
    @JacksonXmlElementWrapper(useWrapping = false)
    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }
}
