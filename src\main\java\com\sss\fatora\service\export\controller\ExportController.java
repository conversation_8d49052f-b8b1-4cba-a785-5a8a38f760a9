package com.sss.fatora.service.export.controller;

import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.domain.local.Log;
import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.domain.local.application.IssueApplication;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.TransactionDataVW;
import com.sss.fatora.service.export.service.*;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Optional;


@RestController
public class ExportController {

    @Autowired
    ExportCardsService exportCardsService;
    @Autowired
    ExportTransactionsService exportTransactionsService;
    @Autowired
    ExportLogService exportLogService;
    @Autowired
    ExportApplicationsService exportApplicationsService;
    @Autowired
    ExportSettlementTransactionService exportSettlementTransactionService;
    @Autowired
    ExportIssueApplicationService exportIssueApplicationService;
    @Autowired
    ExportCustomerService exportCustomerService;

    @PostMapping(value = "cards/export")
    public void exportCards(@ParameterName(value = "filter", required = false) CardDataVW card,
                            @ParameterName(value = "fromIssueDate", required = false) Long fromIssueDate,
                            @ParameterName(value = "toIssueDate", required = false) Long toIssueDate,
                            @ParameterName(value = "details") Boolean details,
                            @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                            @ParameterName(value = "pagination", required = false) Pagination pagination,
                            HttpServletResponse response) throws Exception {


        if (card == null)
            card = new CardDataVW();

        byte[] cards = exportCardsService.export(card, fromIssueDate, toIssueDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(), details);
        response.setContentLength(cards.length);
        response.getOutputStream().write(cards);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

    }


    @PostMapping(value = "transactions/export")
    public void exportTransaction(@ParameterName(value = "filter", required = false) TransactionDataVW transaction,
                                  @ParameterName(value = "fromUDate", required = false) Long fromUDate,
                                  @ParameterName(value = "toUDate", required = false) Long toUDate,
                                  @ParameterName(value = "details", required = false) Boolean details,
                                  @ParameterName(value = "pagination", required = false) Pagination pagination,
                                  @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                  @ParameterName(value = "withBins", required = false) Boolean withBins,
                                  HttpServletResponse response) throws Exception {
        if (transaction == null)
            transaction = new TransactionDataVW();
        byte[] transactions = exportTransactionsService.export(transaction, fromUDate, toUDate, pagination, Optional
                .ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(), details,withBins);
        response.setContentLength(transactions.length);
        response.getOutputStream().write(transactions);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    @PostMapping(value = "settlement-transactions/export")
    public void exportSettlementTransaction(@ParameterName(value = "filter", required = false) SettlementTransaction transaction,
                                            @ParameterName(value = "fromDate", required = false) Long fromDate,
                                            @ParameterName(value = "toDate", required = false) Long toDate,
//                                            @ParameterName(value = "showUnreconciled", required = false) Boolean showUnreconciled,
                                            @ParameterName(value = "showReconciled", required = false) Boolean
                                                        showReconciled,
                                            @ParameterName(value = "showSettled", required = false) Boolean showSettled,
                                            @ParameterName(value = "details", required = false) Boolean details,
                                            @ParameterName(value = "pagination", required = false) Pagination pagination,
                                            @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                            @ParameterName(value = "withBins", required = false) Boolean withBins,
                                            HttpServletResponse response) throws Exception {
            if (transaction == null)
                transaction = new SettlementTransaction();
            byte[] transactions = exportSettlementTransactionService.export(transaction, fromDate, toDate,
                    showReconciled,showSettled, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(), details,withBins);
            response.setContentLength(transactions.length);
            response.getOutputStream().write(transactions);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        @RequestMapping(value = "logs/export", method = RequestMethod.POST)
        public void search (@ParameterName(value = "filter", required = false) Log log,
                @ParameterName(value = "fromDate", required = false) Long fromDate,
                @ParameterName(value = "toDate", required = false) Long toDate,
                @ParameterName(value = "filterOperator", required = false) MapWrapper < Operator > filterOperator,
                @ParameterName(value = "pagination", required = false) Pagination pagination,
                HttpServletResponse response) throws Exception {
            byte[] logs = exportLogService.search(log, fromDate, toDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
            response.setContentLength(logs.length);
            response.getOutputStream().write(logs);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        }


        @RequestMapping(value = "application/export", method = RequestMethod.POST)
        public void exportApplications (@ParameterName(value = "filter", required = false) LocalApplication
        localApplication,
                @ParameterName(value = "fromDate", required = false) Long fromDate,
                @ParameterName(value = "toDate", required = false) Long toDate,
                @ParameterName(value = "filterOperator", required = false) MapWrapper < Operator > filterOperator,
                @ParameterName(value = "pagination", required = false) Pagination pagination,
                HttpServletResponse response) throws Exception {
            if (localApplication == null)
                localApplication = new LocalApplication();
            byte[] localApplications = exportApplicationsService.export(localApplication, fromDate, toDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
            response.setContentLength(localApplications.length);
            response.getOutputStream().write(localApplications);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        }

    @RequestMapping(value = "issue-application/export", method = RequestMethod.POST)
    public void exportIssueApplication (@ParameterName(value = "filter", required = false) IssueApplication
                                                    issueApplication,
                                    @ParameterName(value = "fromDate", required = false) Long fromDate,
                                    @ParameterName(value = "toDate", required = false) Long toDate,
                                    @ParameterName(value = "filterOperator", required = false) MapWrapper < Operator > filterOperator,
                                    @ParameterName(value = "pagination", required = false) Pagination pagination,
                                    HttpServletResponse response) throws Exception {
        if (issueApplication == null)
            issueApplication = new IssueApplication();
        byte[] issueApplications = exportIssueApplicationService.export(issueApplication, fromDate, toDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
        response.setContentLength(issueApplications.length);
        response.getOutputStream().write(issueApplications);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

    }

    @PostMapping(value = "customer/export")
    public void exportCustomer(@ParameterName(value = "filter", required = false) Customer customer,
                            @ParameterName(value = "details") Boolean details,
                            @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                            @ParameterName(value = "pagination", required = false) Pagination pagination,
                            HttpServletResponse response) throws Exception {


        if (customer == null)
            customer = new Customer();
        byte[] customers = exportCustomerService.export(customer, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(), details);
        response.setContentLength(customers.length);
        response.getOutputStream().write(customers);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

    }
}
