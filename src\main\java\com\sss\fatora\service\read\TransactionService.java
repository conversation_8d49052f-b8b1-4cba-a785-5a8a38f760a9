package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.TransactionDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.read.TransactionDataVW;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import com.sss.fatora.service.local.ApplicationUserPrivilegeService;
import com.sss.fatora.service.local.BankService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.ObjectConverter;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional("readingDBTransactionManager")
public class TransactionService extends GenericReadService<TransactionDao, TransactionDataVW, Long> {
    final TransactionDao transactionDao;
    final BankService bankService;
    final ApplicationUserPrivilegeService applicationUserPrivilegeService;
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;

    public TransactionService(TransactionDao transactionDao, BankService bankService,
            ApplicationUserPrivilegeService applicationUserPrivilegeService, PaginationService paginationService,
            PrivilegeService privilegeService, ConfigService configService) {
        this.transactionDao = transactionDao;
        this.bankService = bankService;
        this.applicationUserPrivilegeService = applicationUserPrivilegeService;
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
    }

    /**
     * @param transaction       This Parameter Is For The Value Of Filters Send From
     *                          The Front-End
     * @param fromUDate,toUDate This Two Parameters Represent The Period We Want To
     *                          Search In
     * @param filterOperator    There Is An Specific Operator For Every Filter
     *                          {@link com.sss.fatora.utils.constants.Operator}
     * @param projection        This Is A List Of Columns For Select Statement In
     *                          Search Query
     * @param withBins          This Parameter Is For Adding The Bank Bins To Where
     *                          Clause
     *                          <p>
     *                          This Function Add Certain Conditions To Where Clause
     *                          That Can't Be Added To The Filter
     *                          (TransactionDataVW)
     *                          Directly And Then Request The Dynamic Search
     *                          Function ( dynamicSearch() )
     */

    public Page<TransactionDataVW> search(TransactionDataVW transaction, Long fromUDate, Long toUDate,
            Pagination pagination, Map<String, Operator> filterOperator, List<String> projection, Boolean withBins,
            Boolean isCount)
            throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page transactions;
        String additionalConstraint = "";
        long date1 = 99;
        long date2 = 99;
        long time1 = 99;
        long time2 = 99;
        long dateLimitLong = 99;
        String searchLimitConstraint = "";
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        DateFormat timeFormat = new SimpleDateFormat("HHmmss");

        if (transaction == null)
            transaction = new TransactionDataVW();

        if (fromUDate == null && transaction.getId() == null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.MONTH, -1);
            Date lastMonthDate = cal.getTime();
            fromUDate = lastMonthDate.getTime();
        }
        if (toUDate == null && transaction.getId() == null)
            toUDate = new Date().getTime();

        if (applicationUser.getMonthlySearchLimit() != null) {
            Date dateLimit = DateUtils.getDateAfterMonthsAdded(new Date(),
                    -1 * applicationUser.getMonthlySearchLimit());
            dateLimitLong = Long.parseLong(dateFormat.format(new Date(dateLimit.getTime())));
            searchLimitConstraint = " And TransactionDataVW.udate >=" + dateLimitLong;

        }
        if (fromUDate != null) {
            date1 = Long.parseLong(dateFormat.format(new Date(fromUDate)));
            time1 = Long.parseLong(timeFormat.format(new Date(fromUDate)));
            if (date1 < dateLimitLong && dateLimitLong != 99) {
                date1 = dateLimitLong;
            }

        }
        if (toUDate != null) {
            date2 = Long.parseLong(dateFormat.format(new Date(toUDate)));
            time2 = Long.parseLong(timeFormat.format(new Date(toUDate)));
        }
        // Case Is Like If And Else In Sql
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            if (privilegeService.privilegeFoundByUserIdAndPrivilegeName(applicationUser.getId(), "hpan")) {
                projection.add("Case When TransactionDataVW.issuerBank =" + bankCode +
                        " Then TransactionDataVW.hpan When TransactionDataVW.issuerBank <>" + bankCode +
                        " Then TransactionDataVW.maskedHpan End As hpan ");
                projection.add("Case When TransactionDataVW.issuerBank =" + bankCode +
                        " Then TransactionDataVW.acct1 When TransactionDataVW.issuerBank <>" + bankCode +
                        " Then 'Unavailable' End As acct1 ");
                projection.removeIf(s -> s.equals("TransactionDataVW.hpan AS hpan"));
                projection.removeIf(s -> s.equals("TransactionDataVW.acct1 AS acct1"));
            }
            // if (withBins){
            // bins = applicationUser.getBank().getBinCodes().stream().map(s -> "'" + s +
            // "'").collect(Collectors.joining(","));
            // additionalConstraint += "(substring(TransactionDataVW.hpan,0,6) IN (" + bins
            // + ")" +
            // " AND TransactionDataVW.udate BETWEEN " + date1 + " AND " + date2 +
            // searchLimitConstraint;
            // }
            if (withBins != null && withBins) {
                additionalConstraint += "( " + getTransactionsByUserBankBins(applicationUser) + ") AND ";
                if (date1 == date2) {
                    additionalConstraint += "(TransactionDataVW.udate = " + date1 +
                            " AND TransactionDataVW.onlineTime BETWEEN " + time1 + " AND " + time2 + ")";
                } else {
                    additionalConstraint += "(" +
                            "(TransactionDataVW.udate = " + date1 + " AND TransactionDataVW.onlineTime >= " + time1
                            + ") OR " +
                            "(TransactionDataVW.udate > " + date1 + " AND TransactionDataVW.udate < " + date2 + ") OR "
                            +
                            "(TransactionDataVW.udate = " + date2 + " AND TransactionDataVW.onlineTime <= " + time2
                            + ")" +
                            ")";
                }
            } else {
                additionalConstraint += "(TransactionDataVW.issuerBank = " + bankCode + " OR " +
                        " TransactionDataVW.acquirerBank = " + bankCode + ") AND ";
                if (date1 == date2) {
                    additionalConstraint += "(TransactionDataVW.udate = " + date1 +
                            " AND TransactionDataVW.onlineTime BETWEEN " + time1 + " AND " + time2 + ")";
                } else {
                    additionalConstraint += "(" +
                            "(TransactionDataVW.udate = " + date1 + " AND TransactionDataVW.onlineTime >= " + time1
                            + ") OR " +
                            "(TransactionDataVW.udate > " + date1 + " AND TransactionDataVW.udate < " + date2 + ") OR "
                            +
                            "(TransactionDataVW.udate = " + date2 + " AND TransactionDataVW.onlineTime <= " + time2
                            + ")" +
                            ")";
                }

            }
                        if(transaction.getId()!=null){
                additionalConstraint = "";
            }
        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()) && transaction.getId() == null) {
            if (date1 == date2) {
                additionalConstraint += "(TransactionDataVW.udate = " + date1 +
                        " AND TransactionDataVW.onlineTime BETWEEN " + time1 + " AND " + time2 + ")";
            } else {
                additionalConstraint += "(" +
                        "(TransactionDataVW.udate = " + date1 + " AND TransactionDataVW.onlineTime >= " + time1
                        + ") OR " +
                        "(TransactionDataVW.udate > " + date1 + " AND TransactionDataVW.udate < " + date2 + ") OR " +
                        "(TransactionDataVW.udate = " + date2 + " AND TransactionDataVW.onlineTime <= " + time2 + ")" +
                        ")";
            }
        }

        // " AND TransactionDataVW.onlineTime BETWEEN " + time1 + " AND " + time2
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("TransactionDataVW.utrnno");
            pagination.setOrderType("DESC");
        }

        transactions = this.dynamicSearch(transaction, pagination, additionalConstraint, filterOperator, projection,
                isCount, this::reverseAmount);
        return transactions;

    }

    /**
     * This Function Check If The Search Is For Reversal Transaction Then Reverse
     * ActAmt ( Actual Amount )
     * And ReqAmt ( Requested Amount ) If Founded
     */

    public Object reverseAmount(Object transaction) {
        transaction = ObjectConverter.convertToObject(transaction, TransactionDataVW.class);
        if (((TransactionDataVW) transaction).getReversal() == 1) {
            ((TransactionDataVW) transaction).setReqAmt(((TransactionDataVW) transaction).getReqAmt() != 0
                    ? ((TransactionDataVW) transaction).getReqAmt() * -1
                    : 0);
            ((TransactionDataVW) transaction).setActAmt(((TransactionDataVW) transaction).getActAmt() != 0
                    ? ((TransactionDataVW) transaction).getActAmt() * -1
                    : 0);
        }
        return transaction;
    }

    /**
     * @param transaction       This Parameter Is For The Value Of Filters Send From
     *                          The Front-End
     * @param fromUDate,toUDate This Two Parameters Represent The Period We Want To
     *                          Search In
     * @param filterOperator    There Is An Specific Operator For Every Filter
     *                          {@link com.sss.fatora.utils.constants.Operator}
     * @param withBins          This Parameter Is For Adding The Bank Bins To Where
     *                          Clause
     *                          <p>
     *                          This Function Get UserPrivileges And Domain Columns
     *                          And Then Request The Intersection Function
     *                          ( filterProjectionList() ) On Them, Set The Desired
     *                          Filter Values To The Filter Object When
     *                          Requesting ( getRouting() ) And Then Request Search
     *                          Function ( search() )
     */

    @Log(actionType = ActionType.TRANSACTION_SEARCH)
    public Page<TransactionDataVW> mainSearch(TransactionDataVW transaction, Long fromUDate, Long toUDate,
            Pagination pagination, Map<String, Operator> filterOperator, Boolean withBins, Boolean isCount)
            throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
        projection.add("TransactionDataVW.id as id");
        // getRouting(transaction);

        Page<TransactionDataVW> transactions = search(transaction, fromUDate, toUDate, pagination, filterOperator,
                projection, withBins, isCount);
        return transactions;
    }

    /**
     * @param filter This Parameter Is For The Value Of Filters Send From The
     *               Front-End
     *               This Function Check If The User Is A Bank User, It Adds A New
     *               Value
     *               In The Filter
     */

    public void getRouting(TransactionDataVW filter) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (filter.getIssuerBank() != null && applicationUser.getBank() != null) {
            String bankCode = applicationUser.getBank().getCode();
            filter.setIssuerBank(bankCode);
        }
    }

    /**
     * @param filter   This Parameter Is For The Value Of Filters Send From The
     *                 Front-End
     * @param recordId This Parameter Is Set In The Filter Param For Where Clause Of
     *                 The Query
     *                 <p>
     *                 This Function Get UserPrivileges And Domain Columns And Then
     *                 Request The Intersection Function
     *                 ( filterProjectionList() ) On Them , Set The Desired Filter
     *                 Values To The Filter Object And Then
     * @param withBins
     */

    @Log(actionType = ActionType.TRANSACTION_DETAILS)
    public TransactionDataVW getWithDetails(TransactionDataVW filter, Long recordId, Boolean withBins, Boolean isCount)
            throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        projection.addAll(configService.getTransactionDetailsColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
        projection.removeIf(s -> s.equals("TransactionDataVW.netbal AS netbal"));
        filter.setId(recordId);
        filter.setUtrnno(Long.parseLong(recordId.toString().substring(0, recordId.toString().length() - 1)));
        String reversal = recordId.toString().substring(recordId.toString().length() - 1);
        filter.setReversal(Long.parseLong(reversal));
        Page<TransactionDataVW> transactions = search(filter, null, null, null, new HashMap<>(), projection, withBins,
                isCount);
        if (transactions != null) {
            return !transactions.isEmpty()
                    ? ObjectConverter.convertToObject(transactions.getContent().get(0), TransactionDataVW.class)
                    : null;
        }
        return null;
    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List
     *                   Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two
     *                   List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As
     *                   Column) To Be Used In The
     *                   Select Section Of The Query
     */

    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> "TransactionDataVW." + s + " AS " + s)
                .collect(Collectors.toList());
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This
     * Specific Domain (Online Transaction)
     */

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService
                .getPrivilegesById(PrivilegeType.TRANSACTION_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    // This Function Is Deprecated
    @Log(actionType = ActionType.TRANSACTION_EXPORT)
    public Page<TransactionDataVW> export(TransactionDataVW transaction, Long fromUDate, Long toUDate,
            Pagination pagination, Map<String, Operator> filterOperator, Boolean details, Boolean withBins,
            Boolean isCount)
            throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        if (details)
            projection.addAll(configService.getTransactionDetailsColumns());
        projection = filterProjectionList(projection, privileges);

        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(configService.getMaxExportSize());
        }

        Page<TransactionDataVW> transactions = search(transaction, fromUDate, toUDate, pagination, filterOperator,
                projection, withBins, isCount);
        return transactions;
    }

    private String getTransactionsByUserBankBins(ApplicationUser applicationUser) {
        String additionalConstraint = "";
        String BinString = applicationUser.getBank().getBin();
        String[] Bins = BinString.split(",");
        for (int BinCount = 0; BinCount < Bins.length; BinCount++) {
            String Bin = Bins[BinCount];
            if (BinCount < Bins.length - 1)
                additionalConstraint += "TransactionDataVW.hpan like  '" + Bin + "%' or ";
            else
                additionalConstraint += "TransactionDataVW.hpan like  '" + Bin + "%' ";
        }
        return additionalConstraint;
    }
}
