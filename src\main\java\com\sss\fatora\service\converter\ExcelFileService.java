package com.sss.fatora.service.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sss.fatora.domain.converter.Application;
import com.sss.fatora.domain.converter.Applications;
import com.sss.fatora.domain.converter.ExcelFile;
import com.sss.fatora.domain.local.ActionRequest;
import com.sss.fatora.service.local.ActionRequestService;
import com.sss.fatora.utils.constants.BulkTemplateType;
import com.sss.fatora.utils.model.TempContentModel;
import io.micrometer.core.instrument.util.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.PropertyException;
import java.io.*;
import java.util.*;

@Service
public class ExcelFileService {

    @Autowired
    ApplicationService applicationService;

    @Autowired
    ActionRequestService actionRequestService;

    private ExcelFile prepareExcelIndexes(TempContentModel tempContentModel) throws IOException {
        ExcelFile excelFile = new ExcelFile();
        if (tempContentModel.getContentExtension().equalsIgnoreCase("xls")) {
            excelFile.setHssfWorkbook(
                    new HSSFWorkbook(new FileInputStream(new File(tempContentModel.getRelativePath()))));
            Integer index = 0;
            if (excelFile.getHssfWorkbook().getNumberOfSheets() > 2)
                index = 2;

            excelFile.setSheet(excelFile.getHssfWorkbook().getSheetAt(index));
        } else {
            excelFile.setWorkbook(new XSSFWorkbook(new FileInputStream(new File(tempContentModel.getRelativePath()))));
            Integer index = 0;
            if (excelFile.getWorkbook().getNumberOfSheets() > 2)
                index = 2;

            excelFile.setSheet(excelFile.getWorkbook().getSheetAt(index));
        }
        Row row = excelFile.getSheet().getRow(0);
        if (row == null)
            return null;
        for (Integer i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            excelFile.getExcelIndexes().put(cell.getStringCellValue(), cell.getColumnIndex());
        }

        return excelFile;
    }

    public Object getCellValue(Cell targetCell) {
        Object targetCellValue = null;
        if (CellType.FORMULA.equals(targetCell.getCellType())) {
            if (targetCell.getCachedFormulaResultType().equals(CellType.STRING))
                if (targetCell.getStringCellValue().equals("")) {
                    targetCellValue = null;
                } else
                    targetCellValue = targetCell.getStringCellValue();
            else {

                if (DateUtil.isCellDateFormatted(targetCell)) {
                    targetCellValue = targetCell.getDateCellValue();
                } else {
                    String test = String.valueOf((int) targetCell.getNumericCellValue());
                    targetCellValue = test;
                }

            }
        } else if (CellType.NUMERIC.equals(targetCell.getCellType())) {
            // targetCellValue = (int) targetCell.getNumericCellValue();
            targetCellValue = targetCell.getNumericCellValue();
            targetCellValue = ((Double) targetCellValue).longValue();
        } else if (CellType.STRING.equals(targetCell.getCellType())) {
            targetCellValue = targetCell.getStringCellValue();

        } else if (CellType.BOOLEAN.equals(targetCell.getCellType())) {
            targetCellValue = targetCell.getBooleanCellValue();

        }
        return targetCellValue;
    }

    public Applications prepareApplicationObjects(ExcelFile excelFile) throws IllegalAccessException {
        Applications applications = new Applications();
        applications.setApplications(new ArrayList<>());

        for (Row row : excelFile.getSheet()) {
            if (row.getRowNum() == 0) {
                continue;
            }
            Boolean isEmpty = true;
            Iterator<Cell> cellIterator = row.cellIterator();
            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                if (cell != null && cell.getCellType() != CellType.BLANK && StringUtils.isNotBlank(cell.toString())) {
                    isEmpty = false;
                    continue;
                }
            }
            if (isEmpty) {
                continue;
            }
            Application application = new Application();
            Application preparedApplication = applicationService.prepareApplicationObject(application,
                    excelFile.getExcelIndexes(), row);
            applications.getApplications().add(preparedApplication);
        }
        return applications;
    }

    public String convertExcelToXml(TempContentModel tempContentModel) {
        try {

            // create an instance of `JAXBContext`
            JAXBContext context = JAXBContext.newInstance(Applications.class);
            //
            // // create an instance of `Marshaller`
            Marshaller marshaller = context.createMarshaller();
            //
            // // enable pretty-print XML output
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

            //
            // // write XML to `StringWriter`
            StringWriter sw = new StringWriter();

            ExcelFile excelFile = prepareExcelIndexes(tempContentModel);
            marshaller.marshal(prepareApplicationObjects(excelFile), sw);

            // print the XML
            System.out.println(sw.toString());
            return sw.toString();

        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (PropertyException e) {
            e.printStackTrace();
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return null;
    }

    public Applications convertExcelToDatabase(TempContentModel tempContentModel) {
        try {

            // create an instance of `JAXBContext`
            // JAXBContext context = JAXBContext.newInstance(Application.class);
            //
            // // create an instance of `Marshaller`
            // Marshaller marshaller = context.createMarshaller();
            //
            // // enable pretty-print XML output
            // marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

            //
            // // write XML to `StringWriter`
            // StringWriter sw = new StringWriter();

            ExcelFile excelFile = prepareExcelIndexes(tempContentModel);

            Applications preparedApplications = prepareApplicationObjects(excelFile);

            // print the XML
            // System.out.println(sw.toString());
            return preparedApplications;

        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<ActionRequest> convertRequestsExcelToDatabase(TempContentModel tempContentModel, String type)
            throws Exception {
        try {
            ExcelFile excelFile = prepareExcelIndexes(tempContentModel);
            if (excelFile == null)
                throw new Exception("Empty Template");
            else {
                if ((type.equals(BulkTemplateType.Close_Card.getType())
                        || type.equals(BulkTemplateType.Validate_Card.getType())) &&
                        !excelFile.getExcelIndexes().toString().equals("{CARD NUMBER=0}"))
                    throw new Exception("Wrong Template");
                if (type.equals(BulkTemplateType.Change_Status.getType()) &&
                        !excelFile.getExcelIndexes().toString().equals("{STATUS=1, CARD NUMBER=0}"))
                    throw new Exception("Wrong Template");
                if ((type.equals(BulkTemplateType.Reissue_Card.getType())
                        || type.equals(BulkTemplateType.Instant_Reissue_Card.getType()))
                        && !excelFile.getExcelIndexes().toString()
                                .equals("{OLD EXPIRY=3, DELIVERY BRANCH=1, CARD NUMBER=0, OLD NUMBER=2}"))
                    throw new Exception("Wrong Template");
                if ((type.equals(BulkTemplateType.Renew_Card.getType())
                        || type.equals(BulkTemplateType.Instant_Renew_Card.getType())) &&
                        !excelFile.getExcelIndexes().toString()
                                .equals("{DELIVERY BRANCH=1, CARD NUMBER=0}"))
                    throw new Exception("Wrong Template");

                if (type.equals(BulkTemplateType.Change_Language.getType()) &&
                        !excelFile.getExcelIndexes().toString().equals("{LANGUAGE=1, CARD NUMBER=0}"))
                    throw new Exception("Wrong Template");
                if (type.equals(BulkTemplateType.Change_Product.getType()) &&
                        !excelFile.getExcelIndexes().toString().equals("{CARD NUMBER=0}"))
                    throw new Exception("Wrong Template");
            }
            return prepareRequestObjects(excelFile, type);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ActionRequest> prepareRequestObjects(ExcelFile excelFile, String type) {
        List<ActionRequest> actionRequestList = new ArrayList<>();

        for (Row row : excelFile.getSheet()) {
            if (row.getRowNum() == 0) {
                continue;
            }
            Boolean isEmpty = true;
            Iterator<Cell> cellIterator = row.cellIterator();
            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                if (cell != null && cell.getCellType() != CellType.BLANK && StringUtils.isNotBlank(cell.toString())) {
                    isEmpty = false;
                    continue;
                }
            }
            if (isEmpty) {
                continue;
            }
            ActionRequest actionRequest = new ActionRequest();
            ActionRequest preparedRequest = actionRequestService.prepareRequestObject(actionRequest,
                    excelFile.getExcelIndexes(), row, type);
            actionRequestList.add(preparedRequest);
        }
        return actionRequestList;
    }

}
