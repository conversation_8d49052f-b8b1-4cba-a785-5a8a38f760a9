package com.sss.fatora.domain.local;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.sss.fatora.domain.converter.Application;

public class Body {
    @JacksonXmlProperty(localName = "Fault")
    @JacksonXmlElementWrapper(useWrapping = false)
    Fault fault;

    @JacksonXmlProperty(localName = "application")
    @JacksonXmlElementWrapper(useWrapping = false)
    Application application;

    public Fault getFault() {
        return fault;
    }

    public void setFault(Fault fault) {
        this.fault = fault;
    }

    public Application getApplication() {
        return application;
    }

    public void setApplication(Application application) {
        this.application = application;
    }
}
