package com.sss.fatora.service.local;


import com.sss.fatora.dao.local.LogDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.domain.local.Log;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.utils.constants.LogType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Transactional("localDBTransactionManager")
public class LogService extends GenericService<LogDao, Log, Long> {

    final PaginationService paginationService;
    final PrivilegeService privilegeService;

    public LogService(PaginationService paginationService, PrivilegeService privilegeService) {
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
    }

    /**
     * This Function Get All Logs By User Type {@link com.sss.fatora.utils.constants.UserType}
     */
    @Override
    public Page getAll(Pagination pagination, int... recordStatus) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page<Log> page = null;
        if (UserType.INTERNAL.getType().equals(applicationUser.getUserType()))
            page = dao.findAll(paginationService.getPagination(pagination));
        else if (UserType.EXTERNAL.getType().equals(applicationUser.getUserType()))
            page = dao.getExternalAudit(applicationUser.getBank().getId(), paginationService.getPagination(pagination));
        return page;
    }

    /**
     * @param log             This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator  There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *                        <p>
     *                        This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (Log)
     *                        Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     */

    public Page<Log> search(Log log, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException, ParseException {
        Page<Log> logs;
        String additionalConstraint = "";
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();

        if (log == null)
            log = new Log();

        if (fromDate == null) {
            fromDate = new Date(1612178075113L).getTime();
        }
        if (toDate == null) {
            toDate = new Date(7258118400L * 1000).getTime();
        }
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");

        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            List<Privilege> privileges = privilegeService.getPrivilegesById(PrivilegeType.LOG_PRIVILEGE.getType(), applicationUser.getId());
            if ((!log.getType().equalsIgnoreCase(LogType.Actions.getType()) && !log.getType().equalsIgnoreCase(LogType.Requests.getType()))
                    && !privileges.stream().anyMatch(privilege -> privilege.getName().equalsIgnoreCase("View All Logs"))) {
                log.setBank(new Bank(applicationUser.getBank().getId()));
            }

            additionalConstraint = "Log.creationDate BETWEEN '" + f.format(DateUtils.getStartDayOfDate(fromDate)) +
                    "' AND '" + f.format(DateUtils.getEndDayOfDate(toDate)) + "' ";
            if (log.getType().equalsIgnoreCase(LogType.Actions.getType()) || log.getType().equalsIgnoreCase(LogType.Requests.getType()))
                additionalConstraint += " AND ( Log.bank = " + applicationUser.getBank().getId()
                        + " or " + getLogsByUserBankBins(applicationUser) + " ) ";
            else
                additionalConstraint += " AND Log.bank = " + applicationUser.getBank().getId();

        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()))
            additionalConstraint = "Log.creationDate between '" + f.format(DateUtils.getStartDayOfDate(fromDate)) + "' AND '" + f.format(DateUtils.getEndDayOfDate(toDate)) + "'";

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("Log.creationDate");
            pagination.setOrderType("DESC");
        }

        if (log.getBank() == null)
            log.setBank(new Bank());
        if (log.getApplicationUser() == null)
            log.setApplicationUser(new ApplicationUser());
        log.getApplicationUser().setForcePasswordUpdated(null);
        logs = this.dynamicSearch(log
                , pagination
                , additionalConstraint
                , filterOperator
                , null,false);

        return logs;
    }

    /**
     * @param userId This Parameter Is For User Id
     *               <p>
     *               This Function Get All Logs This User
     */
    public List<Log> getLogsByUserId(Integer userId) {
        return dao.getLogsByUserId(userId);
    }

    private String getLogsByUserBankBins(ApplicationUser applicationUser) {
        String additionalConstraint = "";
        String BinString = applicationUser.getBank().getBin();
        String[] Bins = BinString.split(",");
        for (int BinCount = 0; BinCount < Bins.length; BinCount++) {
            String Bin = Bins[BinCount];
            if (BinCount < Bins.length - 1)
                additionalConstraint += "Log.entityId like  '" + Bin + "%' or ";
            else
                additionalConstraint += "Log.entityId like  '" + Bin + "%' ";
        }
        return additionalConstraint;
    }
}
