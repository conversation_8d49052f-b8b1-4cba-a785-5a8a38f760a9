package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.AccountTypeDao;
import com.sss.fatora.dao.read.AgentsDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.read.AccountType;
import com.sss.fatora.domain.read.Agents;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("readingDBTransactionManager")
public class AccountTypeService extends GenericReadService<AccountTypeDao, AccountType, Long> {

}
