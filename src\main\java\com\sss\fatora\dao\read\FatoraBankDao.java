package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.FatoraBank;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FatoraBankDao extends GenericReadDao<FatoraBank , Long> {

    @Query("SELECT FB.shortName FROM FatoraBank FB " +
            "WHERE " +
            "FB.code =:code"
    )
    String getFatoraBankByCode(@Param("code") String code);
}
