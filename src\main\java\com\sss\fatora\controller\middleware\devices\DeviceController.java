package com.sss.fatora.controller.middleware.devices;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.domain.middleware.device.*;
import com.sss.fatora.domain.middleware.device.Sims.AssignSimRequest;
import com.sss.fatora.domain.middleware.response.SearchMiddlewareResponse;
import com.sss.fatora.service.middleware.devices.DeviceService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import oracle.ucp.proxy.annotation.Post;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.xml.ws.Response;
import java.util.List;

@RestController
@RequestMapping("/devices")
public class DeviceController {
    private final DeviceService deviceService;
    private final ObjectMapper objectMapper;

    public DeviceController(DeviceService deviceService, ObjectMapper objectMapper) {
        this.deviceService = deviceService;
        this.objectMapper = objectMapper;
    }

    @RequestMapping("/search")
    public ResponseObject search(@ParameterName("device") DeviceRequest deviceRequest,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination) {
        DeviceResponse devices = deviceService.search(deviceRequest, pagination);
        return devices != null ?
                ResponseObject.FETCHED_SUCCESS(devices, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/details")
    public ResponseObject getWithDetails(@ParameterName("device") DeviceRequest deviceRequest) {
        DeviceResponse devices = deviceService.getWithDetails(deviceRequest);
        return devices != null ?
                ResponseObject.FETCHED_SUCCESS(devices, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/get-hosts")
    public ResponseObject getHosts(@ParameterName("device") DeviceRequest deviceRequest) {
        DeviceResponse hosts = deviceService.getHosts(deviceRequest);
        return hosts != null ?
                ResponseObject.FETCHED_SUCCESS(hosts, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/get-sim-details")
    public ResponseObject getSIMDetails(@ParameterName("device") DeviceRequest deviceRequest) {
        SIMDetailsResponse simDetails = deviceService.getSIMDetails(deviceRequest);
        return simDetails != null ?
                ResponseObject.FETCHED_SUCCESS(simDetails, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/export")
    public ResponseObject export(@ParameterName("device") DeviceRequest deviceRequest,
                                 @ParameterName("withDetails") Boolean withDetails,
                                 HttpServletResponse response) {
        DeviceResponse devices = deviceService.export(deviceRequest, new Pagination(), withDetails, response);
        return ResponseObject.FETCHED_SUCCESS(devices, null);
    }

    // NEW APIS
    @RequestMapping("/get-banks")
    public ResponseObject getBanks() {

        List<Bank> banks = deviceService.getBanks();
        return !banks.isEmpty() ?
                ResponseObject.FETCHED_SUCCESS(banks, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/get-owners")
    public ResponseObject getOwners() {

        List<Owner> owners = deviceService.getOwners();
        return !owners.isEmpty() ?
                ResponseObject.FETCHED_SUCCESS(owners, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @PostMapping("/update")
    public ResponseObject update(@RequestBody UpdateDeviceRequest request) {
        try {
            this.deviceService.update(request.getDeviceSerialNumber(), request.getNewId(), request.getType());
        } catch (Exception e) {
            return ResponseObject.UPDATING_FAILED(null, null);
        }
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    @RequestMapping("/get-custodians")
    public ResponseObject getCustodians() {

        List<Custodian> custodians = deviceService.getCustodians();
        return !custodians.isEmpty() ?
                ResponseObject.FETCHED_SUCCESS(custodians, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/get-all-status")
    public ResponseObject getAllStatus() {

        List<Status> status = deviceService.getStatus();
        return !status.isEmpty() ?
                ResponseObject.FETCHED_SUCCESS(status, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


    @PostMapping("/assign-sim")
    public ResponseObject assignSim(@RequestBody AssignSimRequest request) throws Exception {
        deviceService.assignSim(request);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }
}
