package com.sss.fatora.service.middleware.acquiring.customers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.domain.middleware.acquiring.AcqCustomers.AcqCustomer;
import com.sss.fatora.domain.middleware.acquiring.AcqCustomers.GetCustomerByNumberRequest;
import com.sss.fatora.domain.middleware.acquiring.AcqCustomers.GetCustomersWithFilterRequest;
import com.sss.fatora.domain.middleware.response.ObjectMiddlewareResponse;
import com.sss.fatora.domain.middleware.response.SearchMiddlewareResponse;
import com.sss.fatora.service.middleware.terminals.TerminalsService;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.logging.Logger;

@Service
public class AcqCustomerService {
    private final RestTemplate restTemplate;

    private final Environment environment;

    private static final Logger LOGGER = Logger.getLogger(TerminalsService.class.getName());


    public AcqCustomerService(RestTemplate restTemplate, Environment environment) {
        this.restTemplate = restTemplate;
        this.environment = environment;
    }

    public AcqCustomer getCustomerByNumber(GetCustomerByNumberRequest acqGetCustomerRequest) throws Exception {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetCustomerByCustomerNumber");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(acqGetCustomerRequest, headers);
        //ResponseEntity<ObjectMiddlewareResponse> response = restTemplate.postForEntity(url, request, ObjectMiddlewareResponse.class);
        ParameterizedTypeReference<ObjectMiddlewareResponse<AcqCustomer>> responseType = new ParameterizedTypeReference<ObjectMiddlewareResponse<AcqCustomer>>() {};
        ResponseEntity<ObjectMiddlewareResponse<AcqCustomer>> response = restTemplate.exchange(url, HttpMethod.POST, request, responseType);

        if (response.getBody() != null && !response.getBody().getSuccess()) {
            throw new Exception("error when fetching customer info");
        }
        return  response.getBody().getResult();
    }

    public List<AcqCustomer> getCustomersWithFilter(GetCustomersWithFilterRequest getCustomersWithFilterRequest){

        String url = environment.getProperty("Base_Url") + environment.getProperty("GetCustomers");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Object> request = new HttpEntity<>(getCustomersWithFilterRequest, headers);
        LOGGER.info(" url :" + url);
        LOGGER.info(" \nRequest :" + new ObjectMapper().valueToTree(request).toString());

        ResponseEntity<SearchMiddlewareResponse> response = restTemplate.postForEntity(url, request, SearchMiddlewareResponse.class);

        LOGGER.info(" \nResponse :" + new ObjectMapper().valueToTree(response).toString());
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }

        return response.getBody().getResult().getItems();
    }
}
