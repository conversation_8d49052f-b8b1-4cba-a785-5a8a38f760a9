package com.sss.fatora.service.local;

import com.sss.fatora.dao.local.BankDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.data.domain.Page;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.IntrospectionException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.List;

@Service
@Transactional("localDBTransactionManager")
public class BankService extends GenericService<BankDao, Bank, Integer> {

    final PaginationService paginationService;
    final ContentUtilService contentUtilService;

    public BankService(PaginationService paginationService, ContentUtilService contentUtilService) {
        this.paginationService = paginationService;
        this.contentUtilService = contentUtilService;
    }


    /**
     * @param bank_id This Parameter Is For Bank Id
     *
     * This Function Get All The Users In This Specific Bank
     * */
    public Page<ApplicationUser> getUsers(Pagination pagination, Integer bank_id) {
        if (bank_id != null) {
            return dao.getUsers(bank_id, paginationService.getPagination(pagination));
        } else return null;
    }

    /**
     * @param bank This Parameter Is For Bank Object
     *
     * This Function Save A Bank Object
     * */
    public Bank saveBankWithContent(Bank bank, TempContentModel tempContentModel) throws IOException {
        if (CustomUserDetails.getCurrentUser().getUserType().equals(UserType.EXTERNAL.getType())){
            throw  new AccessDeniedException("Access Denied For Bank User To Add Bank");
        }
        if (tempContentModel != null) {
            String bankImage = contentUtilService.getContentFromTempContentModel(tempContentModel);
            bank.setImage(bankImage);
            bank.setImageContentType(tempContentModel.getContentType());
        }
        return dao.save(bank);
    }

    @Override
    public Page getAll(Pagination pagination, int... recordStatus) throws AccessDeniedException {
        if (CustomUserDetails.getCurrentUser().getUserType().equals(UserType.EXTERNAL.getType())){
            throw  new AccessDeniedException("Access Denied For Bank User To View Banks");
        }
        return super.getAll(pagination, recordStatus);
    }

    /**
     * @param bank  This Parameter Is For The Value Of Filters Send From The Front-End
     *
     * This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (bank)
     * Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     *
     * */

    public Page<Bank> search(Pagination pagination, Bank bank) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException {
        Page<Bank> banks;
        String additionalConstraint = "";
        ApplicationUser currentUser = CustomUserDetails.getCurrentInstance().getApplicationUser();

        if (bank == null)
            bank = new Bank();

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("ApplicationUser.creationDate");
            pagination.setOrderType("DESC");
        }

        banks = this.dynamicSearch(bank
                , pagination
                , additionalConstraint
                , new MapWrapper<Operator>().getMap()
                , null,false);

        return banks;
    }

    // Deprecated
    public List<Bank> getUserAllowableBanks() {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            if (UserType.INTERNAL.getType().equals(applicationUser.getUserType())) {
                return dao.findAll();
            } else {
                return Collections.singletonList(applicationUser.getBank());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
