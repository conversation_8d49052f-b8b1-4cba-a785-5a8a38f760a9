package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_TERMINAL_MERCHANT")
public class TerminalVW {
    @Id
    @Column(name = "TERMINAL_ID")
    private Long terminalId;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "MERCHANT_ID")
    private String merchantId;

    @Column(name = "MERCHANT")
    private String merchant;

    @Column(name = "MAIN_TERMINAL")
    private String mainTerminal;

    @Column(name = "BANK_CODE")
    private String bankCode;

    @Column(name = "BANK")
    private String bank;
}
