package com.sss.fatora.domain.issuing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_COUNTRY_CODES")
public class CountryCodes {
    @Id
    @Column(name = "CODE")
    String code;

    @Column(name = "NAME")
    String name;

    @Column(name = "COUNTRY_NAME",length = 4000)
    String countryName;

}
