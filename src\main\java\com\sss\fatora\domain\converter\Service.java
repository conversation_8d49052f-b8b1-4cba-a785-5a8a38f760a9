package com.sss.fatora.domain.converter;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = {"value", "service_object"})
public class Service extends GenericConverter {
    private Integer value;
    private ServiceObject service_object;

    public Service() {
    }

    public Service(Integer value, ServiceObject service_object) {
        this.value = value;
        this.service_object = service_object;
    }

    public Integer getValue() {
        return value;
    }

    @XmlAttribute(name = "value")
    public void setValue(Integer value) {
        this.value = value;
    }
}
