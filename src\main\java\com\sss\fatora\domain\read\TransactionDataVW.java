package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_TRANSACTIONS")
//@Table(name = "CARD_TRXN_DATA")
//we must put the name in fatora server like this  (SVISTA.CARD_TRXN_DATA)
public class TransactionDataVW implements Loggable {

    @Id
    @Column(name = "ID")
    Long id;

    @Column(name = "HPAN")
    String hpan;
    @Column(name = "MASKED_HPAN")
    String maskedHpan;
    @Column(name = "UDATE")
    Long udate;
    @Column(name = "TIME")
    Long onlineTime;
    @Column(name = "ACCT1")
    String acct1;
    @Column(name = "ATMID")
    String atmId;
    @Column(name = "MAIN_TERMINAL")
    String mainTerminal;
    @Column(name = "TRANS_TYPE_DESC")
    String transTypeDesc;
    @Column(name = "REQAMT")
    Double reqAmt;
    @Column(name = "ACTAMT")
    Double actAmt;
    @Column(name = "RESP_DESC")
    String respDesc;
    @Column(name = "DEVICE_RESP_DESC")
    String deviceRespDesc;
    @Column(name = "REVERSAL")
    Long reversal;
    @Column(name = "SYS_CURRENCY")
    Long sysCurrency;
    @Column(name = "MERCHANT")
    String merchant;
    @Column(name = "Issuer_Bank")
    String issuerBank;
    @Column(name = "Acquirer_Bank")
    String acquirerBank;
    @Column(name = "IS_FINANCIAL")
    Integer financial;
    @Column(name = "TERMINAL_TYPE")
    String terminalType;



    @Column(name = "ORGDEV")
    Long orgdev;
    @Column(name = "HPAN_NUM")
    Long hpanNum;
    @Column(name = "UTRNNO")//Trans#
    Long utrnno;
    @Column(name = "RRN")
    String onlineRrn;
    @Column(name = "RESP") //Trans Response Code
    Long resp;
    @Column(name = "DEVICE_RESP")
    String deviceResp;
    @Column(name = "CONAMT")
    Long conAmt;
    @Column(name = "TRANS_TYPE")
    Long transType;
    @Column(name = "BILLS1")
    Long bills1;
    @Column(name = "BILLS2")
    Long bills2;
    @Column(name = "BILLS3")
    Long bills3;
    @Column(name = "BILLS4")
    Long bills4;
    @Column(name = "NETBAL")
    Long netbal;
    @Column(name = "ADDRESS_NAME")
    String addressName;
    @Column(name = "ADDRESS_STREET")
    String addressStreet;
    @Column(name = "ADDRESS_CITY")
    String addressCity;
    @Column(name = "ADDRESS_STATE")
    String addressState;
    @Column(name = "ADDRESS_COUNTRY")
    String addressCountry;
    @Column(name = "ADDRESS_POSTAL_CODE")
    String addressPostalCode;
    @Column(name = "SV_TRACE")
    Long svTrace;

    @Column(name = "CARD_INPUT_MODE")
    String cardInputMode;
    @Column(name = "CARDHOLDER_AUTH")
    String cardHolderAuth;
    @Column(name = "POS_DATA_CODE")
    String posDataCode;

    @Transient
    @JsonIgnore
    String query;

    @Override
    @Transient
    public String fetchId() {
        if (this.getOnlineRrn() != null)
            return this.getOnlineRrn();
        return null;
    }

    @Override
    @Transient
    public String getRelatedEntity() {

//        return this.getClass().getSimpleName();
        return "Transactions";
    }

    @Override
    public void setQuery(String query) {
        this.query = query;
    }

    @Override
    public String getQuery() {
        return this.query;
    }
}
