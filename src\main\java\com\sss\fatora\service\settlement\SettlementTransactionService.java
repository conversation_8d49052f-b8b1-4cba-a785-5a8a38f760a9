package com.sss.fatora.service.settlement;

import com.sss.fatora.dao.settlement.SettlementTransactionDao;
import com.sss.fatora.dao.read.TransactionDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import com.sss.fatora.service.local.ApplicationUserPrivilegeService;
import com.sss.fatora.service.local.BankService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.ObjectConverter;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
//@Transactional("ariaDBTransactionManager")
public class SettlementTransactionService extends GenericReadService<SettlementTransactionDao, SettlementTransaction, String> {
    final TransactionDao transactionDao;
    final BankService bankService;
    final ApplicationUserPrivilegeService applicationUserPrivilegeService;
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;

    public SettlementTransactionService(TransactionDao transactionDao, BankService bankService, ApplicationUserPrivilegeService applicationUserPrivilegeService, PaginationService paginationService, PrivilegeService privilegeService, ConfigService configService) {
        this.transactionDao = transactionDao;
        this.bankService = bankService;
        this.applicationUserPrivilegeService = applicationUserPrivilegeService;
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
    }

    /**
     * @param transaction      This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate  This Two Parameters Represent The Period We Want To Search In
     * @param showReconciled This Parameter Is For Adding A Specific Condition To The Search Query ( This
     *                         condition Is Fatora logic )
     * @param filterOperator   There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection       This Is A List Of Columns For Select Statement In Search Query
     * @param withBins         This Parameter Is For Adding The Bank Bins To Where Clause
     *                         <p>
     *                         This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (SettlementTransaction)
     *                         Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     */

    public Page<SettlementTransaction> search(SettlementTransaction transaction, Long fromDate, Long toDate, Boolean
            showReconciled, Boolean showSettled, Pagination pagination, Map<String, Operator> filterOperator,
                                              List<String> projection, Boolean withBins) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page transactions;
        String additionalConstraint = "";
        String oracleFormat = "yyyy-MM-dd HH24:MI:ss";
        String javaFormat = "yyyy-MM-dd HH:mm:ss";
        DateFormat f = new SimpleDateFormat(javaFormat);
        String bins = "";
        String date1 = "";
        String date2 = "";
        String reconciliationDate = "";
        String settlementDate = "";
        String searchLimitConstraint = "";


        if (transaction == null)
            transaction = new SettlementTransaction();
        if (fromDate == null)
            fromDate = new Date(*********).getTime();
        if (toDate == null)
            toDate = new Date(System.currentTimeMillis() * 100).getTime();
        date1 = getDate(fromDate);
        date2 = getDate(toDate);

        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            additionalConstraint =
                    " SettlementTransaction.date BETWEEN " + date1 + " AND " + date2 + searchLimitConstraint;
            if (withBins != null && withBins) {

//                bins = applicationUser.getBank().getBinCodes().stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
//                additionalConstraint += " AND (substring(TransactionDataVW.hpan,0,6) IN (" + bins + ")";

                additionalConstraint += " AND ( " + getTransactionsByUserBankBins(applicationUser) + " ) ";

            }
        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType())) {
            additionalConstraint = "SettlementTransaction.date BETWEEN " + date1 + " AND " + date2 + searchLimitConstraint;
        }
        if (showReconciled != null) {
            if (showReconciled.equals(true))
                additionalConstraint += " AND SettlementTransaction.reconciliationDate is not null  ";
            if (showReconciled.equals(false))
                additionalConstraint += " AND SettlementTransaction.reconciliationDate is null  ";
        }
        if (showSettled != null) {
            if (showSettled.equals(true))
                additionalConstraint += " AND SettlementTransaction.settlmentDate is not null ";
            if (showSettled.equals(false))
                additionalConstraint += " AND SettlementTransaction.settlmentDate is null ";
        }
        if (transaction.getReconciliationDate() != null) {
            reconciliationDate = f.format(DateUtils.getStartDayOfDate(transaction.getReconciliationDate().getTime()));
            additionalConstraint += " AND SettlementTransaction.reconciliationDate = '" + reconciliationDate + "'";
            transaction.setReconciliationDate(null);
//            filterOperator.remove("reconciliationDate");
        }
        if (transaction.getSettlmentDate() != null) {
            settlementDate = f.format(DateUtils.getStartDayOfDate(transaction.getSettlmentDate().getTime()));
            additionalConstraint += " AND SettlementTransaction.settlmentDate = '" + settlementDate + "'";
            transaction.setSettlmentDate(null);
//            filterOperator.remove("settlmentDate");
        }

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("SettlementTransaction.date");
            pagination.setOrderType("DESC");
        }

        transactions = this.dynamicSearch(transaction
                , pagination
                , additionalConstraint
                , filterOperator
                , projection
                ,true);

        String stars = "";

        // This Code Change Numbers With Asterisk For cardNumber Entity
        if (transactions != null) {
            for (int j = 0; j < transactions.getContent().size(); j++) {
                if (((Map<Object, Object>) transactions.getContent().get(j)).get("cardNumber") != null) {
                    for (int i = 6; i < ((Map<Object, Object>) transactions.getContent().get(j)).get("cardNumber").toString().length() - 4; i++) {
                        stars += "*";
                    }

                    String codedCardNo = ((Map<Object, Object>) transactions.getContent().get(j)).
                            get("cardNumber").toString().substring(0, 6) +
                            stars + ((Map<Object, Object>) transactions.getContent().get(j)).
                            get("cardNumber").toString().substring(((Map<Object, Object>) transactions.getContent().get(j)).
                            get("cardNumber").toString().length() - 4);
                    stars = "";
                    ((Map<Object, Object>) transactions.getContent().get(j)).put("cardNumber", codedCardNo);

                }
            }
        }

        return transactions;

    }

    /**
     * @param transaction      This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate  This Two Parameters Represent The Period We Want To Search In
     * @param showReconciled This Parameter Is For Adding A Specific Condition To The Search Query (This
     *                         condition Is Fatora logic)
     * @param filterOperator   There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param withBins         This Parameter Is For Adding The Bank Bins To Where Clause
     *                         <p>
     *                         This Function Get UserPrivileges And Domain Columns And Then Request The Intersection Function
     *                         ( filterProjectionList() ) On Them, Set The Desired Filter Values To The Filter Object When
     *                         Requesting ( getRouting() ) And Then Request Search Function ( search() )
     */

    @Log(actionType = ActionType.TRANSACTION_SEARCH)
    public Page<SettlementTransaction> mainSearch(SettlementTransaction transaction, Long fromDate, Long
            toDate, Boolean showReconciled, Boolean showSettled, Pagination pagination, Map<String, Operator>
                                                          filterOperator, Boolean withBins) throws
            IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getSettlementTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
        getRouting(transaction);

        Page<SettlementTransaction> transactions = search(transaction, fromDate, toDate, showReconciled,
                showSettled, pagination, filterOperator, projection, withBins);
        return transactions;
    }

    /**
     * @param filter This Parameter Is For The Value Of Filters Send From The Front-End
     *               This Function Check If The User Is A Bank User, It Adds A New Value
     *               In The Filter
     */

    public void getRouting(SettlementTransaction filter) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getBank() != null) {
            String bankCode = applicationUser.getBank().getCode();
            filter.setBankOfRecord(bankCode);
        }
    }

    /**
     * @param expDate This Parameter Is A TimeStamp Date
     *                <p>
     *                This Function Change The Date From TimeStamp To Another Irregular Format
     *                That Is Like (********)
     */
    private String getDate(Long expDate) {
        String stringDate;
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        calendar.setTimeInMillis(expDate);
        Integer dateYear = calendar.get(Calendar.YEAR);
        Integer dateMonth = calendar.get(Calendar.MONTH) + 1;
        Integer dateDay = calendar.get(Calendar.DAY_OF_MONTH);
        /*
          This 'If' Is For EXAMPLE.
          year = 2021, month = 11, day = 12 So It Become -> ******** Do Not Need To Add Any Zero
         */
        if (dateMonth >= 10 && dateDay >= 10) {
            stringDate = dateYear.toString() + dateMonth.toString() + dateDay.toString();
        }
        /*
         * This 'If' Is For EXAMPLE.
         * year = 2021, month = 1, day = 12 So It Become -> 20210112 We Add A Zero After Year Before Month
         */
        else if (dateMonth < 10 && dateDay >= 10) {
            stringDate = dateYear.toString() + "0" + dateMonth.toString() + dateDay.toString();
        }
        /*
         * This 'If' Is For EXAMPLE.
         * year = 2021, month = 11, day = 2 So It Become -> 20211102 We Add A Zero After Month Before Day
         */
        else if (dateMonth >= 10 && dateDay < 10) {
            stringDate = dateYear.toString() + dateMonth.toString() + "0" + dateDay.toString();
        }
        /*
         * This 'If' Is For EXAMPLE.
         * year = 2021, month = 1, day = 2 So It Become -> 20210102 We Add Two Zeros One Before Month And The Other
         * Before Day
         */
        else {
            stringDate = dateYear.toString() + "0" + dateMonth.toString() + "0" + dateDay.toString();
        }
        return stringDate;
    }

    /**
     * @param filter   This Parameter Is For The Value Of Filters Send From The Front-End
     * @param recordId This Parameter Is Set In The Filter Param For Where Clause Of The Query
     *                 <p>
     *                 This Function Get UserPrivileges And Domain Columns And Then Request The Intersection Function
     *                 ( filterProjectionList() ) On Them , Set The Desired Filter Values To The Filter Object And Then
     * @param withBins
     */

    @Log(actionType = ActionType.TRANSACTION_DETAILS)
    public SettlementTransaction getWithDetails(SettlementTransaction filter, Integer recordId, Boolean withBins) throws
            IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getSettlementTransactionDetailsColumns());
//        projection.addAll(configService.getSettlementTransactionDetailsColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
        filter.setRowId(recordId);
        Page<SettlementTransaction> transactions = search(filter, null, null, null, null, null, new HashMap<>(), projection, withBins);
        if (transactions != null) {
            return !transactions.isEmpty() ? ObjectConverter.convertToObject(transactions.getContent().get(0), SettlementTransaction.class) : null;
        }
        return null;
    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As Column) To Be Used In The
     *                   Select Section Of The Query
     */

    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> "SettlementTransaction." + s + " AS " + s)
                .collect(Collectors.toList());
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This Specific Domain (SettlementTransaction)
     */

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.SETTLEMENT_TRANSACTION_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    private String getTransactionsByUserBankBins(ApplicationUser applicationUser) {
        String additionalConstraint = "";
        String BinString = applicationUser.getBank().getBin();
        String[] Bins = BinString.split(",");
        for (int BinCount = 0; BinCount < Bins.length; BinCount++) {
            String Bin = Bins[BinCount];
            if (BinCount < Bins.length - 1)
                additionalConstraint += "SettlementTransaction.cardNumber like  '" + Bin + "%' or ";
            else
                additionalConstraint += "SettlementTransaction.cardNumber like  '" + Bin + "%' ";
        }
        return additionalConstraint;
    }
}
