package com.sss.fatora.service.converter;


import com.sss.fatora.domain.converter.ContactData;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ContactDataService extends GenericConverterService<ContactData> {

    public ContactData prepareContactDataObject(ContactData contactData, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(contactData, excelIndexes, row);
        return contactData;

    }


    public ContactData prepareContactDataObjectFromPanel(ContactData contactData, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(contactData, fixedValues);
        return contactData;

    }

    public ContactData prepareContactDataObjectForChangeMobileNumber(ContactData contactData, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(contactData, fixedValues);
        return contactData;

    }
}
