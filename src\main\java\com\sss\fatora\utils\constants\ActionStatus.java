package com.sss.fatora.utils.constants;

public enum ActionStatus {
    SUCCESSES("Success"),
    FAILED("Failed"),
    PENDING("Waiting for approval"),
    CONNECTION_ERROR("Connection Error"),
    USER_REQUEST_DENIED("User request denied");
    private String type;

    ActionStatus(String type){
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
