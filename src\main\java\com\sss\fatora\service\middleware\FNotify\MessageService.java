package com.sss.fatora.service.middleware.FNotify;

import com.sss.fatora.domain.middleware.FNotify.*;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Service
//@Transactional("smppDBTransactionManager")
public class MessageService {

    private final PrivilegeService privilegeService;
    private final ConfigService configService;
    private final Environment environment;
    final RestTemplate restTemplate;

    public MessageService(PrivilegeService privilegeService, ConfigService configService, Environment env, RestTemplate restTemplate) {
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.environment = env;
        this.restTemplate = restTemplate;
    }

    public MessagesAPIResponse filterSearchResultByBankCode(MessagesAPIResponse messages, ApplicationUser applicationUser) {
        String bins = "";
        String bankCode = "";
        if (applicationUser.getBank() != null) {
            bins = applicationUser.getBank().getBinCodes().stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            bankCode = applicationUser.getBank().getCode();
        }
        List<MessageResponse> removedMessageResponseList = new ArrayList<>();
        for (MessageResponse message : messages.getMessages()) {
            if (message.getIdentifier().length() == 16) {
                if (!bins.contains(message.getIdentifier().substring(0, 6))) {
                    removedMessageResponseList.add(message);
                }
            } else if (message.getIdentifier().length() == 8) {
                if (!bankCode.equals(message.getIdentifier().substring(0, 2))) {
                    removedMessageResponseList.add(message);
                }
            }
        }
        messages.getMessages().removeAll(removedMessageResponseList);
        return messages;
    }


    public MessagesAPIResponse projectionSearch(MessagesAPIResponse messages) throws IllegalAccessException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getFNotifyColumns());
        if (privileges == null || privileges.isEmpty()) {
            return new MessagesAPIResponse();
        }
        projection = filterProjectionList(projection, privileges);
        for (MessageResponse message : messages.getMessages())
            for (Field field : message.getClass().getDeclaredFields())
                if (!projection.contains(field.getName())) {
                    field.setAccessible(true);
                    field.set(message, null);
                }
        return messages;
    }

    public MessagesAPIResponse search(MessageRequest message, Pagination pagination) {
        String url = environment.getProperty("FatoraFNotifyUrl") + environment.getProperty("allMessages") + "?page=" + pagination.getStart() + "&size=" + pagination.getSize();
        String username = environment.getProperty("BasicAuthUsernameFNotify");
        String password = environment.getProperty("BasicAuthPasswordFNotify");
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(username, password);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(message, headers);

        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> allbins = new ArrayList<>();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = "";
            if (applicationUser.getBank() != null) {
                List<String> bins =applicationUser.getBank().getBinCodes();
                allbins.addAll(bins);
                bankCode = applicationUser.getBank().getCode();
                allbins.add(bankCode);
            }

        }
        message.setPrefixes(allbins);
        ResponseEntity<MessagesAPIResponse> response = restTemplate.postForEntity(url, request, MessagesAPIResponse.class);
        if (response != null) {
            try {
                return projectionSearch(response.getBody());

            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else
            return null;
    }

    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        List<String> projections = projection.stream()
                .distinct()
                .filter(privileges::contains)
//                .map(s -> {
////                    if (s.equalsIgnoreCase("messageTypeId"))
////                        s = " MessageTypes.name " + " AS " + " MessageTypeName ";
////                    else
////                    s = " Messages." + s + " AS " + s;
//                    return s;
//                })
                .collect(Collectors.toList());
        return projections;
    }

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.FNotifyColumns.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    public MessagesTypeResponse getMessageTypes() {
        String url = environment.getProperty("FatoraFNotifyUrl") + environment.getProperty("messageType");
        String username = environment.getProperty("BasicAuthUsernameFNotify");
        String password = environment.getProperty("BasicAuthPasswordFNotify");
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(username, password);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<String>(headers);
        ResponseEntity<MessagesTypeResponse> response = restTemplate.exchange(url, HttpMethod.GET, request, MessagesTypeResponse.class);
        return response != null ? response.getBody() : null;
    }

}
