package com.sss.fatora.domain.converter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@XmlType(propOrder = {"command", "account_number", "currency", "account_type","account_object"})

public class Account extends GenericConverter {
    // @ExcelProperty(name = "id8")
    private String id;
    //  @ExcelProperty(name = "ns1:command9")
    private String command;
    // @ExcelProperty(name = "ns1:account_number")
    private String account_number;
    // @ExcelProperty(name = "ns1:currency")
    private String currency;
    // @ExcelProperty(name = "ns1:account_type")
    private String account_type;
    private AccountObject account_object;


    public String getId() {
        return id;
    }
    @XmlAttribute(name = "id")
    public void setId(String id) {
        this.id = id;
    }


}
