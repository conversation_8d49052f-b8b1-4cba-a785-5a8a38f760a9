package com.sss.fatora.utils.service;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.GenericDomain;
import com.sss.fatora.domain.generic.LocalDomain;
import com.sss.fatora.utils.annotation.PersistRelatedObject;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class RefService {
    @Autowired
    @Qualifier("localDBEntityManagerFactory")
    private EntityManager em;

    public void linkChildrenWithParents(Object domain) {
        try {
            for (PropertyDescriptor pd : Introspector.getBeanInfo(domain.getClass()).getPropertyDescriptors()) {
                Class<?> classType = pd.getPropertyType();
                Object propertyValue = pd.getReadMethod().invoke(domain);

                // if the property is list or set
                if (classType.equals(Set.class) || classType.equals(List.class)) {
                    // if the property is null initialize the Set
                    if (propertyValue == null) {
                        pd.getWriteMethod().invoke(domain, classType.equals(Set.class) ? new HashSet() : new ArrayList());
                        continue;
                    }
                    // if the Set is empty,
                    // so there is no children to link with it's parent
                    if (((Set) propertyValue).size() == 0) continue;
                    // if the type of Set elements is not extends generic domain,
                    // so there is no need to link
                    if (!(((Set) propertyValue).toArray()[0] instanceof LocalDomain)) continue;

                    Class<?> childClassType = ((Set) propertyValue).toArray()[0].getClass();
                    Method setParentMethod = getParentSetterMethod(domain.getClass(), childClassType);

                    // link the parent with all it's children by invoke setParentMethod
                    for (Object child : (Set) propertyValue) {
                        linkChildrenWithParents(child);
                        setParentMethod.invoke(child, domain);
                    }

                } else {
                    Session session = em.unwrap(Session.class);
                    // link parent with it's children also for other single type property
                    if (propertyValue instanceof LocalDomain && !session.contains(propertyValue))
                        linkChildrenWithParents(propertyValue);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Method getParentSetterMethod(Class<?> domain, Class<?> childClassType) throws Exception {
        for (PropertyDescriptor pd : Introspector.getBeanInfo(childClassType).getPropertyDescriptors()) {
            Class<?> classType = pd.getPropertyType();
            if (classType.equals(domain)) {
                return pd.getWriteMethod();
            }
        }

        return null;
    }

    public Object persistMethod(GenericDomain domain) throws Exception {
//        if(domain == null) {
//            return null;
//        }
        for (PropertyDescriptor pd : Introspector.getBeanInfo(domain.getClass()).getPropertyDescriptors()) {
            Method getter = pd.getReadMethod();

            if (getter == null)
                continue;
            if (getter.isAnnotationPresent(PersistRelatedObject.class)) {

                GenericDomain currentEntity = (GenericDomain) getter.invoke(domain);
                if (currentEntity != null) {
                    GenericDao entityDao = currentEntity.evaluateEntityDao();
                    entityDao.save(currentEntity);
                }

            }
        }

        return domain;
    }
}
