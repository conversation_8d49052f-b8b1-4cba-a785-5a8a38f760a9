package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "command","delivery_agent_number","card_number","card_type","cardholder"})
public class Card extends GenericConverter{
    @ExcelProperty(name = "id3")
    private String id = "card_1";
    @ExcelProperty(name = "ns1:command4")
    private String command;
    @ExcelProperty(name = "ns1:delivery_agent_number")
    private String delivery_agent_number;
    @ExcelProperty(name = "ns1:card_type")
    private String card_type;
    private String card_number;
    private CardHolder cardholder;


    public String getId() {
        return id;
    }
    @XmlAttribute(name = "id")
    public void setId(String id) {
        this.id = "card_1";
    }
}
