package com.sss.fatora.domain.read;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "FAT_FP_CARD_LIMIT")
public class CardLimit {

    @Id
    private Long id;

    @Column(name = "CARD_NO")
    private String cardNumber;

    @Column(name = "LMT_ID")
    private Long limitId;

    @Column(name = "DSC")
    private String dsc;

    @Column(name = "LMT")
    private Long limit;

    @Column(name = "TDY")
    private Long tdy;

}
