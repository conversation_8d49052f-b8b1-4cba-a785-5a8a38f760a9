package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalCardholderDTO {

    @JsonProperty(value = "Cardholder_Number")
    String cardholderNumber;
    @JsonProperty(value = "Cardholder_Name")
    String cardholderName;
    @JsonProperty(value = "Cardholder_Mobile_Number")
    String mobile;
    @JsonProperty(value = "Person")
    LocalPersonDTO person;
}
