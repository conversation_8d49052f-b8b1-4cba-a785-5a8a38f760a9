package com.sss.fatora.security.jwt;

import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.security.service.ApplicationUserDetailsService;
import com.sss.fatora.security.service.TokenBuildService;
import com.sss.fatora.service.local.ApplicationUserService;
import com.sss.fatora.utils.constants.UserType;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.DefaultClaims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Component
public class TokenHandler {

    ResourceBundle tokenProperties = ResourceBundle.getBundle("generalProps/tokenHandler", Locale.ENGLISH);

    //  houres * minutes * seconds * millies
    final long EXPIRATIONTIME = 2 * 60 * 60 * 1000;

    //to parse token
    @Value("${SECRET}")
    String SECRET;

    //to build the token based on user's bank
    String SIIBSECRET = tokenProperties.getString("SIIBSECRET");

    String FatoraSECRET = tokenProperties.getString("FatoraSECRET");

    final public String TOKEN_PREFIX = tokenProperties.getString("TOKEN_PREFIX");            // the prefix of the token in the http header
    final public String HEADER_STRING = tokenProperties.getString("HEADER_STRING");    // the http header containing the prefix + the token

    @Autowired
    private ApplicationUserDetailsService userDetailsService;

  /*  @Autowired
    private ApplicationUserService applicationUserService;
*/
    @Autowired
    private TokenBuildService tokenBuildService;

    /**
     * Generate a token from the username.
     *
     * @param customUserDetails The subject from which generate the token.
     * @return The generated token.
     */
    public String build(String systemName, CustomUserDetails customUserDetails) {

        String JWT = "";
        String SECRET = getSecret(customUserDetails);
        Map<String, Object> userDetails = new HashMap<String, Object>();
        //ApplicationUser user = applicationUserService.findByDomainName(username);
        userDetails = tokenBuildService.buildUserDetails(customUserDetails);

        JWT = Jwts.builder()
                .setId(UUID.randomUUID().toString())
                .setSubject(customUserDetails.getApplicationUser().getDomainName())
                .setClaims(userDetails)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATIONTIME))
                //.compressWith(CompressionCodecs.DEFLATE) // uncomment to enable token compression
                .signWith(SignatureAlgorithm.HS512, SECRET)
                .compact();

        return JWT;

    }

    public String getSecret(CustomUserDetails customUserDetails) {
        if (customUserDetails.getApplicationUser().getUserType().equals(UserType.EXTERNAL.getType())
                && customUserDetails.getApplicationUser().getBank().getPrefix().equals("SIIB"))
            return SIIBSECRET;
        else
            return FatoraSECRET;
    }

    /**
     * Parse a token and extract the subject (username).
     *
     * @param token A token to parse.
     * @return The subject (username) of the token.
     */
    public CustomUserDetails parse(HttpServletRequest request, String token) {
      // String SECRET = getSecret(customUserDetails);

        DefaultClaims tokenBody = (DefaultClaims) Jwts.parser()
                .setSigningKey(SECRET)
                .parseClaimsJws(token.replace(TOKEN_PREFIX, ""))
                .getBody();

        String email = (String) tokenBody.get("email");
        Boolean mobile = Boolean.valueOf(tokenBody.get("mobile").toString());
        //request.setAttribute("domainName", tokenBody.get("domainName"));
        request.setAttribute("userId", tokenBody.get("userId"));
        request.setAttribute("systemModuleId", tokenBody.get("systemModuleId"));
//        request.setAttribute("bank", tokenBody.get("bank"));

        CustomUserDetails userDetails = (CustomUserDetails) userDetailsService.loadUserByUsername(email);
        //to get userDetails from ((CustomUserDetails) authentication.getPrincipal())
        // we must put userDetails inside principal
        userDetails.setSystemModuleId((Integer) tokenBody.get("systemModuleId"));
        userDetails.setMobile(mobile);
        return userDetails;

    }


}
