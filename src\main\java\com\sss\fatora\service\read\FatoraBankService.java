package com.sss.fatora.service.read;


import com.sss.fatora.dao.read.FatoraBankDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.domain.read.FatoraBank;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import com.sss.fatora.utils.constants.UserType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@Transactional("readingDBTransactionManager")
public class FatoraBankService extends GenericReadService<FatoraBankDao,FatoraBank,Long> {

    /**
     * This Function Get All Banks And Send Them To The Front-End
     * */
    public List<FatoraBank> getUserAllowableBanks() {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            return dao.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getBankNameByCode(String code){
        return dao.getFatoraBankByCode(code);
    }
}
