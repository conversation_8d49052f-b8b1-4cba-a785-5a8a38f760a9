package com.sss.fatora.controller.local;

import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.LogDao;
import com.sss.fatora.domain.local.Log;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.service.local.LogService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Optional;

@RestController
@RequestMapping("/audit")
public class LogController extends GenericController<LogService, LogDao, Log, Long> {

    @Override
    @PreAuthorize("hasAnyAuthority('Admin','Audit')")
    public ResponseObject getAll(@ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @RequestParam(required = false) int... status) {
        return super.getAll(pagination, status);
    }


    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResponseObject search(@ParameterName(value = "filter", required = false) Log log,
                                 @ParameterName(value = "fromDate", required = false) Long fromDate,
                                 @ParameterName(value = "toDate", required = false) Long toDate,
                                 @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, ParseException {
        Page<Log> logs = service.search(log, fromDate, toDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", logs.getTotalElements());
        return logs != null ?
                ResponseObject.FETCHED_SUCCESS(logs.getContent(), extra) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
