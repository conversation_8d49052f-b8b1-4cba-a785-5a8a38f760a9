package com.sss.fatora.domain.middleware.acquiring.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class HandleAcquiringRequestDto {
    @NotNull
    @NotBlank
    private Long id;
    @NotNull
    @NotBlank
    private Boolean approved;
    @Null
    private String description;
}
