package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.domain.local.application.IssueApplication;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IssueApplicationDao extends GenericDao<IssueApplication,Long> {

    @Query(value="SELECT DISTINCT A FROM IssueApplication A " +
            "WHERE A.id in :idList")
    List<IssueApplication> getApplicationByIdList(@Param("idList") List<Long> idList);
//    @Query(value="SELECT DISTINCT L.account.id FROM IssueApplication A " +
//            "LEFT JOIN LocalApplicationAccount L ON L.issueApplication.id = A.id " +
//            "WHERE A.id =:idList")
//    List<Long> getAccountIdByIssueApplicationIdList(@Param("idList") Long idList);
}
