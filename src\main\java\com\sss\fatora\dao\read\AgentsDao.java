package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.Agents;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AgentsDao extends GenericReadDao<Agents, Long> {
    @Query("SELECT A FROM Agents A " +
            "WHERE A.bankCode=:bankCode")
    List<Agents> getAllAgentsByBankCode(@Param("bankCode") String bankCode);

    @Query("SELECT A FROM Agents A " +
            "WHERE A.bankCode is not null")
    List<Agents> findAllAgents();
}
