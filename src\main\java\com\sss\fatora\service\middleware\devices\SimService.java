package com.sss.fatora.service.middleware.devices;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.domain.middleware.device.Sims.GetSimsRequest;
import com.sss.fatora.domain.middleware.device.Sims.Sim;
import com.sss.fatora.domain.middleware.response.SearchMiddlewareResponse;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.middleware.terminals.TerminalsService;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.logging.Logger;

@Service
public class SimService {

    private final Environment environment;
    private final ConfigService configService;
    final RestTemplate restTemplate;
    private static final Logger LOGGER = Logger.getLogger(TerminalsService.class.getName());

    public SimService(Environment environment, ConfigService configService, RestTemplate restTemplate) {
        this.environment = environment;
        this.configService = configService;
        this.restTemplate = restTemplate;
    }

    public SearchMiddlewareResponse<Sim> search(GetSimsRequest request) {

        String url = environment.getProperty("Base_Url") + environment.getProperty("GetSims");
        return setSimsFilterRequest(request,url);
    }

    private SearchMiddlewareResponse<Sim> setSimsFilterRequest(GetSimsRequest simRequest,String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(simRequest, headers);
        LOGGER.info(" url :" + url);
        LOGGER.info(" \nRequest :" + new ObjectMapper().valueToTree(request).toString());
        ResponseEntity<SearchMiddlewareResponse> response = restTemplate.postForEntity(url, request, SearchMiddlewareResponse.class);
        LOGGER.info(" \nResponse :" + new ObjectMapper().valueToTree(response).toString());
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }
}
