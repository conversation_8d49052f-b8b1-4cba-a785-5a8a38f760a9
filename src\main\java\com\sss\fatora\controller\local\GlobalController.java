package com.sss.fatora.controller.local;

import com.sss.fatora.domain.read.CardStatusVW;
import com.sss.fatora.service.read.GlobalService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/global")
public class GlobalController {
    GlobalService globalService;

    @Autowired
    public void setGlobalService(GlobalService globalService) {
        this.globalService = globalService;
    }


    @GetMapping
    public ResponseObject getGlobalData() {

        Map cardData = this.globalService.getGlobalData();
        return cardData != null ?
                ResponseObject.FETCHED_SUCCESS(cardData, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

}
