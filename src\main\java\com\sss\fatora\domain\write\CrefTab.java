package com.sss.fatora.domain.write;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import com.sss.fatora.utils.log.Loggable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "CREF_TAB")
public class CrefTab extends LocalDomain implements Loggable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "WRITING_CARD_SEQ")
    @SequenceGenerator(sequenceName = "WRITING_CARD_SEQ",initialValue = 1, allocationSize = 1, name = "WRITING_CARD_SEQ")
    private Integer id;
    @Column(name = "CREF_NO")
    String crefNo;
    @Column(name = "ISSUE_DT")
    Date issueDate;
    @Column(name = "EXP_DT")
    Long expDate;
    @Column(name = "CD_CAPT")
    Integer cdCapt;
    @Column(name = "PIN_CNT")
    Integer pinCnt;
    @Column(name = "Language")
    String language;
    @Transient
    @JsonIgnore
    String query;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }

    @Override
    public String fetchId() {
        return this.getCrefNo();
    }

    @Override
    public String getRelatedEntity() {
        return "CARD";
    }

    @Override
    public void setQuery(String query) {
        this.query=query;
    }

    @Override
    public String getQuery() {
        return query;
    }
}
