package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "region","city","street"})
public class AddressName extends GenericConverter{
    @ExcelProperty(name = "language19")
    private String language;
    @ExcelProperty(name = "ns1:region")
    private String region;
    @ExcelProperty(name = "ns1:city")
    private String city;
    @ExcelProperty(name = "ns1:street")
    private String street;



    public String getLanguage() {
        return language;
    }
    @XmlAttribute(name = "language")
    public void setLanguage(String language) {
        this.language = language;
    }
}
