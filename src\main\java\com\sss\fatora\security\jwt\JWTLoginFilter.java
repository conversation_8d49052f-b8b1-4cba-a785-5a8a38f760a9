package com.sss.fatora.security.jwt;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.security.model.LoginRequest;
import com.sss.fatora.security.service.AuthService;
import com.sss.fatora.security.service.TokenAuthenticationService;
import com.sss.fatora.utils.model.ResponseObject;
import org.apache.catalina.filters.RemoteAddrFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;

@Component("jwtLoginFilter")
public class JWTLoginFilter extends AbstractAuthenticationProcessingFilter {

    private final static String LOGIN_URL = "/login";

    @Autowired
    private TokenAuthenticationService tokenAuthenticationService;

    @Autowired
    private AuthService authService;


    public JWTLoginFilter() {
        super(new AntPathRequestMatcher(LOGIN_URL));
        // set empty authentication manager
        setAuthenticationManager(new AuthenticationManager() {
            public Authentication authenticate(Authentication authentication) throws AuthenticationException {
                return null;
            }
        });
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse res) throws AuthenticationException, IOException, ServletException {

        // Retrieve username and password from the http request and save them in an Account object.

        String username = "";
        String password = "";
        try {
            LoginRequest loginRequest = new ObjectMapper().readValue(req.getReader(), LoginRequest.class);
            username = loginRequest.getEmail();
            password = loginRequest.getPassword();
            if (loginRequest.getMobile() == null) {
                loginRequest.setMobile(false);
            }

            if (loginRequest.getEmail() == null || loginRequest.getEmail().isEmpty()
                    || loginRequest.getPassword() == null || loginRequest.getPassword().isEmpty()
                    || loginRequest.getSystemName() == null || loginRequest.getSystemName().isEmpty()) {
                res.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                authService.buildLoginFailedResponse(req, res, ResponseObject.Text.BADCREDENTIALS.getText());
                return null;
            }
            req.setAttribute("systemName", loginRequest.getSystemName());
            req.setAttribute("password", password);
            req.setAttribute("mobile", loginRequest.getMobile());
            req.setAttribute("bank", loginRequest.getBank());
        } catch (Exception e) {
            e.printStackTrace();
            authService.buildLoginFailedResponse(req, res, ResponseObject.Text.BADCREDENTIALS.getText());
            return null;
        }


        ServletRequestAttributes attributes = new ServletRequestAttributes(req, res);
        initContextHolders(req, attributes);
        // Verify if the correctness of login details.
        // If correct, the successfulAuthentication() method is executed.
        return getAuthenticationManager().authenticate(
                new UsernamePasswordAuthenticationToken(
                        username,
                        password,
                        new ArrayList<GrantedAuthority>()
                )
        );
    }

    @Override
    public void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain,
                                         Authentication auth) throws IOException, ServletException {

        // Pass authenticated user data to the tokenAuthenticationService in order to add a JWT to the http response.
        tokenAuthenticationService.addAuthentication(req, res, auth);
        authService.buildLoginSuccessResponse(req, res, (CustomUserDetails) auth.getPrincipal());
    }


    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response, AuthenticationException failed) throws IOException, ServletException {
        SecurityContextHolder.clearContext();
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        authService.buildLoginFailedResponse(request, response, failed.getMessage());
    }

    private void initContextHolders(HttpServletRequest request, ServletRequestAttributes requestAttributes) {
        LocaleContextHolder.setLocale(request.getLocale());
        RequestContextHolder.setRequestAttributes(requestAttributes);
        if (logger.isTraceEnabled()) {
            logger.trace("Bound request context to thread: " + request);
        }
    }
}
