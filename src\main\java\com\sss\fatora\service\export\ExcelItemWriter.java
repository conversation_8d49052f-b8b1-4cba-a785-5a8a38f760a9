package com.sss.fatora.service.export;

import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.ApplicationUserPrivilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.export.service.ExportCardsService;
import com.sss.fatora.service.local.ApplicationUserService;
import com.sss.fatora.service.read.CardStatusService;
import com.sss.fatora.utils.service.ObjectConverter;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.batch.item.ItemWriter;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.sss.fatora.utils.constants.ActionType.VALIDATE_CARD;

public class ExcelItemWriter implements ItemWriter<HashMap<String, Object>> {
    /*
     * private static final String[] HEADERS = {"CREF", "NAME ON CARD", "STATUS",
     * "PN", "BRANCH", "ISSUE DATE", "EXPIRY DATE", "FIRST NAME", "FAMILY NAME",
     * "MOB", "ADDRESS1", "ADDRESS2", "CITY"};
     */
    private final List<String> headers;
    private final List<String> columns;
    private final SXSSFWorkbook workbook;
    private final Boolean withPrivileges;
    private SXSSFSheet sheet;
    private Integer currentRowNumber = 1;
    private final CellStyle style;
    private final Font font;
    private final Boolean encodeCardNo;

    ExportCardsService exportCardsService;

    private final CardStatusService cardStatusService;
    private final ApplicationUserService applicationUserService;

    ExcelItemWriter(SXSSFWorkbook workbook, SXSSFSheet sheet, List<String> headers, List<String> columns,
            Boolean withPrivileges, Boolean encodeCardNo, CardStatusService cardStatusService,
            ApplicationUserService applicationUserService) {
        this.workbook = workbook;
        this.sheet = sheet;
        this.headers = headers;
        this.columns = columns;
        this.withPrivileges = withPrivileges;
        style = workbook.createCellStyle();
        font = workbook.createFont();
        this.encodeCardNo = encodeCardNo;
        this.cardStatusService = cardStatusService;
        this.applicationUserService = applicationUserService;
    }

    /**
     * @param list This Parameter Represents All The Chunk Of Data To Be Written In
     *             The Excel File
     *             <p>
     *             This Function Write Every Row Of Data In The Excel File Row By
     *             Row
     */
    @Override
    public void write(List<? extends HashMap<String, Object>> list) {

        if (currentRowNumber == 1) {
            if (withPrivileges) {
                deleteNonAuthorizedHeaders();
            }
            addHeaders();
        }
        for (int i = 0; i < list.size(); i++) {
            HashMap<String, Object> map;
            if (!(list.get(0) instanceof Map)) {
                map = ObjectConverter.convertObjectToMap(list.get(i));
            } else {
                map = list.get(i);
            }
            writeRow(map);
            setColumnsWidth();
        }
    }

    /**
     * This Function Intersect Between User Privileges And The Columns And Headers
     * Of This Type Of Excel
     * File ( SettlementTransaction, Cards...)
     */
    private void deleteNonAuthorizedHeaders() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = new ArrayList<>();
        for (ApplicationUserPrivilege row : applicationUser.getApplicationUserPrivileges()) {
            privileges.add(row.getPrivilege().getName());
        }
        Integer index = 0;
        Boolean founded = false;
        List<String> headersToRemove = new LinkedList<String>();
        List<String> columnsToRemove = new LinkedList<String>();
        for (String col : columns) {
            for (String priv : privileges) {
                if (col.equalsIgnoreCase(priv)) {
                    founded = true;
                    break;
                }
            }
            if (founded == false) {
                headersToRemove.add(headers.get(index));
                columnsToRemove.add(columns.get(index));
            }
            founded = false;
            index++;
        }
        headers.removeAll(headersToRemove);
        columns.removeAll(columnsToRemove);

    }

    /**
     * @param objectToWrite This Parameter Have The Value Of Every Column In This
     *                      Specific Row
     *                      <p>
     *                      This Function Get All The Objects To Be Written In This
     *                      Row And Write Every One Of
     *                      Them In It's Specific Cell
     */
    private void writeRow(HashMap<String, Object> objectToWrite) {
        this.sheet = workbook.getSheetAt(0);
        String colored = endOfDayValue(objectToWrite);
        List<Object> columns = prepareColumns(objectToWrite);
        Row row = this.sheet.createRow(currentRowNumber++);
        for (int i = 0; i < columns.size(); i++) {
            writeCell(row, i, columns.get(i), colored);
        }
    }

    /**
     * @param currentColumnNumber This Parameter Indicates On Which Column We Are
     * @param value               This Parameter Is For The Value Of This Cell
     * @param color               This Parameter Is For The Color Of This Cell
     *                            <p>
     *                            This Function Set The Value Of Every Cell And Set
     *                            It's Style
     */
    private void writeCell(Row row, int currentColumnNumber, Object value, String color) {
        Cell cell = row.createCell(currentColumnNumber);
        setValueOfCell(value, cell, currentColumnNumber);
        // cell.setCellValue(value);
        cell.setCellStyle(setCellStyle(currentColumnNumber, color));

    }

    /**
     * @param value               This Parameter Represent The Value Of The Cell To
     *                            Be Set
     * @param cell                This Parameter Is The Where The Value Need To Be
     *                            Set
     *                            <p>
     * @param currentColumnNumber
     */
    private void setValueOfCell(Object value, Cell cell, int currentColumnNumber) {
        if (headers.indexOf("Requested Amount") == currentColumnNumber
                || headers.indexOf("Actual Amount") == currentColumnNumber
                || headers.indexOf("Amount") == currentColumnNumber)
            cell.setCellValue(Double.parseDouble(value.toString()));
        else if (value.getClass().getName().equalsIgnoreCase("java.lang.String")) {
            cell.setCellValue((String) value);
        } else if (value.getClass().getName().equalsIgnoreCase("java.lang.Long")) {
            cell.setCellValue((Long) value);
        } else if (value.getClass().getName().equalsIgnoreCase("java.sql.Timestamp")) {
            Integer index = value.toString().indexOf('.');
            if (index.equals(null)) {
                value = value.toString().substring(0, value.toString().length() - 1);
            }
            value = value.toString().substring(0, index);
            cell.setCellValue(value.toString());
        } else {
            cell.setCellValue(value.toString());
        }
        // setCellTypeForSpecificColumns(value,cell);
    }

    // private void setCellTypeForSpecificColumns(Object value, Cell cell) {
    // if (headers.indexOf("reqAmt") || cell.getRow().)
    // cell.setCellValue(Double.parseDouble(value.toString()));
    // }

    /**
     * @param currentColumnNumber This Parameter Is For The Number Of The Current
     *                            Column
     * @param color               This Parameter Is The Color To Be Set On Excel
     *                            File
     *                            <p>
     *                            This Function Set The Style Of The Every Cell
     */
    private CellStyle setCellStyle(Integer currentColumnNumber, String color) {
        font.setColor(rowColor(color));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        if (headers.indexOf("Query") == currentColumnNumber)
            style.setBorderRight(BorderStyle.NONE);
        else
            style.setBorderRight(BorderStyle.THICK);

        style.setFont(font);
        sheet.setColumnWidth(currentColumnNumber, 6000);
        return style;
    }

    /**
     * @param color This Parameter Is The Color To Be Set On Excel File
     *              <p>
     *              This Function Set The Color Of The Row To Be Written In Excel
     *              File
     */
    private short rowColor(String color) {
        if (color != null && color.equalsIgnoreCase("green")) {
            return IndexedColors.GREEN.getIndex();
        } else {
            return IndexedColors.BLACK.getIndex();
        }

    }

    /**
     * @param objectToWrite This Parameter Have The Values Of One Row Of The Excel
     *                      Files
     *                      <p>
     *                      This Function Return The Color Of The Row To Be Written
     *                      It Depends On Certain Conditions
     */
    private String endOfDayValue(HashMap<String, Object> objectToWrite) {

        // for(String column: columns){
        // if(column.equalsIgnoreCase("closed")){
        if (objectToWrite.get("closed") != null && objectToWrite.get("closed").equals("1")) {
            return "green";
        } else if (objectToWrite.get("closed") != null && objectToWrite.get("closed").equals("0")) {
            return "black";
        }
        // }
        return null;
    }

    /**
     * This Function Add The Headers Value For The First Time To The Excel File
     */
    private void addHeaders() {

        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setBold(true);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);
        style.setBorderBottom(BorderStyle.THICK);
        style.setBorderTop(BorderStyle.THICK);
        Row row = sheet.getRow(0);
        if (row == null) {
            row = sheet.createRow(0);
            int col = 0;

            for (String header : headers) {
                Cell cell = row.createCell(col);
                cell.setCellValue(header);
                cell.setCellStyle(style);
                col++;
            }
        }

    }

    private void setColumnsWidth() {
        if (headers.contains("Export Options"))
            sheet.setColumnWidth(headers.indexOf("Export Options"), 800 * 25);
        if (headers.contains("Bank"))
            sheet.setColumnWidth(headers.indexOf("Bank"), 256 * 31);
        if (headers.contains("Old Value"))
            sheet.setColumnWidth(headers.indexOf("Old Value"), 256 * 34);
        if (headers.contains("Action Value"))
            sheet.setColumnWidth(headers.indexOf("Action Value"), 256 * 34);
    }

    /**
     * @param objectToWrite This Parameter Have The Values Of One Row Of The Excel
     *                      Files
     *                      <p>
     *                      This Function Return A List Of Objects Extracted From
     *                      The HasMap
     */
    private List<Object> prepareColumns(HashMap<String, Object> objectToWrite) {
        List<Object> row = new ArrayList<>();
        refactoringDateAndTime(objectToWrite);
        changeStatusDecoder(objectToWrite);
        retrieveCreatorName(objectToWrite);
        for (String column : columns) {
            if (objectToWrite.get(column) != null) {
                row.add(objectToWrite.get(column));
                reverseAmount(objectToWrite);
                if (encodeCardNo)
                    cardEncoder(objectToWrite);
            } else
                row.add("");
        }
        return row;
    }

    private void changeStatusDecoder(HashMap<String, Object> objectToWrite) {
        if (objectToWrite.get("action") != null && ((objectToWrite.get("action").equals("Change_Status"))
                || objectToWrite.get("action").equals(VALIDATE_CARD.getAction()))) {
            try {
                if (objectToWrite.get("actionValue") != null) {
                    String actionValue = cardStatusService
                            .getByCdStat(Long.valueOf(objectToWrite.get("actionValue").toString()));
                    objectToWrite.put("actionValue", actionValue);
                }
                if (objectToWrite.get("oldValue") != null) {
                    String oldValue = cardStatusService
                            .getByCdStat(Long.valueOf(objectToWrite.get("oldValue").toString()));
                    objectToWrite.put("oldValue", oldValue);
                }
            } catch (NumberFormatException e) {
                System.out.println("NumberFormatException : " + e.getMessage());
            }
        }
    }

    private void retrieveCreatorName(HashMap<String, Object> objectToWrite) {
        if (objectToWrite.get("creatorId") != null) {
            try {
                String username = "";
                ApplicationUser applicationUser = applicationUserService
                        .getById((Integer) objectToWrite.get("creatorId"));
                if (applicationUser != null)
                    username = applicationUser.getFullName();
                objectToWrite.put("creatorId", username);
            } catch (Exception e) {
                System.out.println(" Exception: " + e.getMessage());
            }
        }
    }

    /**
     * @param objectToWrite This Parameter Have The Values Of One Row Of The Excel
     *                      Files
     *                      <p>
     *                      This Function Is Writing The Date And Time In A Specific
     *                      Format
     */
    public void refactoringDateAndTime(HashMap<String, Object> objectToWrite) {
        if (objectToWrite.get("udate") != null) {
            try {
                Long udateLong = (long) objectToWrite.get("udate");
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                String udateString = udateLong.toString();
                Date date = formatter.parse(udateString);
                SimpleDateFormat outputFormatter = new SimpleDateFormat("dd/MM/yyyy");
                String formattedUdate = outputFormatter.format(date);
                objectToWrite.put("udate", formattedUdate); // Replace long with formatted date string
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        if (objectToWrite.get("creationDate") != null) {
            Timestamp timestamp = (Timestamp) objectToWrite.get("creationDate");
            Date date = new Date(timestamp.getTime());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String strDate = formatter.format(date);
            objectToWrite.put("creationDate", strDate);
        }
        if (objectToWrite.get("issueDate") != null) {
            Timestamp timestamp = (Timestamp) objectToWrite.get("issueDate");
            Date date = new Date(timestamp.getTime());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String strDate = formatter.format(date);
            objectToWrite.put("issueDate", strDate);
        }
        if (objectToWrite.get("date") != null && objectToWrite.get("date") instanceof Long) {
            String dateString = objectToWrite.get("date").toString();
            StringBuilder g = new StringBuilder();
            g.append(dateString);
            g.insert(4, '-');
            g.insert(7, '-');
            dateString = g.toString();
            objectToWrite.put("date", dateString);
        }
        if (objectToWrite.get("expDate") != null && objectToWrite.get("expDate") instanceof Long) {
            String dateString = objectToWrite.get("expDate").toString();
            StringBuilder g = new StringBuilder();
            g.append(dateString);
            g.insert(4, '-');
            // g.insert(7,'-');
            dateString = g.toString();
            objectToWrite.put("expDate", dateString);
        }
        /*
         * Ex. The Input Is : 358 -> The Output Is 00:03:58
         * The Input Is : 1248 -> The Output Is 00:12:48
         * The Input Is : 23458 -> The Output Is 02:34:58
         */
        if (objectToWrite.get("time") != null && objectToWrite.get("time") instanceof Long
                || objectToWrite.get("time") != null && objectToWrite.get("time") instanceof String) {
            String timeString = objectToWrite.get("time").toString();
            objectToWrite.put("time", FormatTime(timeString));
        }
        if (objectToWrite.get("onlineTime") != null && objectToWrite.get("onlineTime") instanceof Long ||
                objectToWrite.get("onlineTime") != null && objectToWrite.get("onlineTime") instanceof String) {
            String timeString = objectToWrite.get("onlineTime").toString();
            objectToWrite.put("onlineTime", FormatTime(timeString));
        }
    }

    public String FormatTime(String timeString) {
        StringBuilder g = new StringBuilder();
        g.append(timeString).reverse();
        timeString = g.toString();
        timeString = addZerosIfNeeded(timeString);
        g.delete(0, timeString.length());
        String[] s = new String[4];
        Integer num = 0;
        String timeFormated = "";
        for (int i = 0; i <= 4; i = i + 2) {
            if (i >= 4) {
                s[num] = timeString.substring(i);
            } else {
                s[num] = timeString.substring(i, i + 2);
            }
            if (num == 2) {
                timeFormated = timeFormated + s[num];
            } else {
                timeFormated = timeFormated + s[num] + ":";
            }
            num++;
        }
        timeString = timeFormated;
        if (timeString.length() == 7) {
            g.append(timeString).append(0).reverse();
        } else {
            g.append(timeString).reverse();
        }
        timeString = g.toString();
        g.delete(0, timeString.length());
        return timeString;
    }

    /**
     * @param time THis Parameter Is Time In Format Like 230422 Means 23:04:22
     *             <p>
     *             This Function Add Zeros To The Param
     */
    public String addZerosIfNeeded(String time) {
        if (time.length() < 2) {
            time = time + "00000";
        } else if (time.length() < 3) {
            time = time + "0000";
        } else if (time.length() < 4) {
            time = time + "000";
        } else if (time.length() < 5) {
            time = time + "00";
        } else if (time.length() < 6) {
            time = time + "0";
        } else {
            time = time;
        }
        return time;
    }

    /**
     * @param transaction This Parameter Have The Values Of One Row Of The Excel
     *                    Files
     *                    <p>
     *                    This Function Reverse The Amount Of Some Values For Any
     *                    Row
     */
    public void reverseAmount(HashMap<String, Object> transaction) {
        if (transaction.get("reversal") != null && !transaction.get("reversal").equals(1L))
            return;
        if (transaction.get("reqAmt") != null) {
            transaction.put("reqAmt",
                    transaction.get("reqAmt").equals(0D) ? Double.parseDouble(transaction.get("reqAmt").toString()) * -1
                            : 0);
        }
        if (transaction.get("actAmt") != null) {
            transaction.put("actAmt",
                    transaction.get("actAmt").equals(0D) ? Double.parseDouble(transaction.get("actAmt").toString()) * -1
                            : 0);
        }
    }

    public void cardEncoder(HashMap<String, Object> transaction) {
        if (transaction.get("cardNumber") != null) {
            String stars = "";
            for (int i = 6; i < transaction.get("cardNumber").toString().length() - 4; i++)
                stars += "*";
            String codedCardNo = transaction.get("cardNumber").toString().substring(0, 6) + stars + transaction
                    .get("cardNumber").toString().substring(transaction.get("cardNumber").toString().length() - 4);
            transaction.put("cardNumber", codedCardNo);
        }
    }
}
