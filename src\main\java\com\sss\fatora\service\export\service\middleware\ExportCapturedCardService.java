package com.sss.fatora.service.export.service.middleware;

import com.sss.fatora.domain.middleware.CapturedCards.CapturedCard;
import com.sss.fatora.domain.middleware.Merchants.Merchant;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.read.FatoraBankService;
import com.sss.fatora.utils.service.ContentUtilService;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ExportCapturedCardService {
    final ContentUtilService contentUtilService;
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private final FatoraBankService fatoraBankService;
    List<String> headers;

    public ExportCapturedCardService(ContentUtilService contentUtilService, FatoraBankService fatoraBankService) {
        this.contentUtilService = contentUtilService;
        this.fatoraBankService = fatoraBankService;
    }

    void initWorkbook(List<String> projectionColumns) {
        headers = projectionColumns;
        workbook = new SXSSFWorkbook();
        this.sheet = workbook.createSheet("CapturedCards");

    }

    private void writeHeaderLine(List<String> projectionHeaders) {
//        sheet = workbook.createSheet("CapturedCards");

        Row row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setBold(true);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);
        style.setBorderBottom(BorderStyle.THICK);
        style.setBorderTop(BorderStyle.THICK);
        int i = 0;
        List<String> projection = new ArrayList<>(projectionHeaders);
        for (String iterator : projection) {
            createCell(row, i++, iterator, style);
        }
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        sheet.setColumnWidth(columnCount, 6000);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue((String) value);
        }
        if (headers.contains("Capture Reason"))
            sheet.setColumnWidth(headers.indexOf("Capture Reason"), 256 * 28);
        if (headers.contains("Card Holder"))
            sheet.setColumnWidth(headers.indexOf("Card Holder"), 256 * 36);
        cell.setCellStyle(style);
    }

    private void writeDataLines(List<CapturedCard> capturedCardList, List<String> projectionColumns) {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);
        style.setFont(font);
        for (CapturedCard card : capturedCardList) {
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            String bankName = fatoraBankService.getBankNameByCode(card.getBank()) != null ?
                    fatoraBankService.getBankNameByCode(card.getBank()) : card.getBank();
            card.setBank(bankName);
            setupRowByType(style, row, columnCount, card, projectionColumns);
        }
    }

    private void setupRowByType(CellStyle style, Row row, Integer columnCount, CapturedCard capturedCard,
                                List<String> projectionColumns) {
        List<String> fields;
        fields = projectionColumns;
        for (String field : fields) {
            Object value = null;
            try {
                Field declaredField = capturedCard.getClass().getDeclaredField(field);
                declaredField.setAccessible(true);
                value = declaredField.get(capturedCard);
                if (value != null)
                    value = processDate(value);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
            createCell(row, columnCount++, value, style);
        }


    }

    private Object processDate(Object value) {
        Boolean isDate = false;
        Date dateObject = null;
        try {
            dateObject = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(value.toString());
            isDate = true;
        } catch (ParseException e) {
        }
        if (isDate)
            value = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateObject);
        return value;
    }

    //    @Log(actionType = ActionType.CAPTURED_CARD_EXPORT)
    public void export(List<CapturedCard> capturedCardList, HttpServletResponse response, List<String> projectionHeaders, List<String> projectionColumns) throws IOException {
        initWorkbook(projectionHeaders);
        writeHeaderLine(projectionHeaders);
        writeDataLines(capturedCardList, projectionColumns);
        File file = contentUtilService.makeExcelFile();
        FileOutputStream fileOutputStream = new FileOutputStream(file.getPath());
        workbook.write(fileOutputStream);
        workbook.dispose();
        workbook = null;
        fileOutputStream.flush();
        fileOutputStream.close();
        byte[] cards = FileUtils.readFileToByteArray(file);
        response.setContentLength(cards.length);
        response.getOutputStream().write(cards);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }
}
