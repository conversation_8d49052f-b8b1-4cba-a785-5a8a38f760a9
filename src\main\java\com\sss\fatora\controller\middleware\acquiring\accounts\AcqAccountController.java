package com.sss.fatora.controller.middleware.acquiring.accounts;

import com.sss.fatora.domain.middleware.acquiring.AcqAccounts.AcqAccount;
import com.sss.fatora.domain.middleware.acquiring.AcqAccounts.AcqAccountRequest;
import com.sss.fatora.service.middleware.acquiring.accounts.AcqAccountService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/accounts")
public class AcqAccountController {

    private final AcqAccountService acqAccountService;

    public AcqAccountController(AcqAccountService acqAccountService) {
        this.acqAccountService = acqAccountService;
    }

    @PostMapping("/search")
    public ResponseObject getCustomerAccounts(@RequestBody AcqAccountRequest acqAccountRequest) throws Exception {
        List<AcqAccount> accounts = acqAccountService.getCustomerAccounts(acqAccountRequest);
        return ResponseObject.FETCHED_SUCCESS(accounts,null);
    }
}
