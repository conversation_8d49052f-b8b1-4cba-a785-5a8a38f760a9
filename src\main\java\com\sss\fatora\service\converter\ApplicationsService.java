package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Application;
import com.sss.fatora.domain.converter.Applications;
import com.sss.fatora.domain.converter.Customer;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.util.Map;
@Service
public class ApplicationsService extends GenericConverterService<Applications> {
    public Applications prepareApplicationsObject(Applications applications, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
//        application.setApplication_type("APTPISSA");
//        application.setAgent_number(1);
//        application.setApplication_flow_id(1001);
//        application.setApplication_status("APST0006");
//        application.setOperator_id("A0393");
//        application.setInstitution_id(1001);
//        application.setCustomer_type("ENTTPERS");

       // preparedApplications.setCustomer(customerService.prepareCustomerObject(new Customer(),excelIndexes,row));
        return applications;
    }
}
