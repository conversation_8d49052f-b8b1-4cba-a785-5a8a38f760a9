package com.sss.fatora.domain.local;
import java.util.List;
import java.util.Map;
import com.sss.fatora.domain.converter.GenericConverter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class AcquiringActionRequestJSON extends GenericConverter {

    private Long id;
    private String oldValue;
    private String newValue;
    private Integer status;
    private String serialNumber;
    private String terminalId;
    private String latitude;
    private String longitude;
    private String oldLongitude;
    private String oldLatitude;
    private String oldAccountNumber;
    private String newAccountNumber;
    private List<Map<String, String>> pairs;
}
