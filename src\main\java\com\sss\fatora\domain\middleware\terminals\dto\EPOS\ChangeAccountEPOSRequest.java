package com.sss.fatora.domain.middleware.terminals.dto.EPOS;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class ChangeAccountEPOSRequest {

    private String terminalId;
    @NotNull
    private String oldAccountNumber;
    @NotNull
    private String oldAccountType;

    @NotNull
    private String newAccountNumber;
    @NotNull
    private String newAccountType;

}
