package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.Application;
import com.sss.fatora.domain.converter.Contact;
import com.sss.fatora.domain.converter.Customer;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ApplicationService extends GenericConverterService<Application> {

    @Autowired
    CustomerServiceConverter customerServiceConverter;

    public Application prepareApplicationObject(Application application, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        Application preparedApplication = getPropertyValueByExcel(application, excelIndexes, row);
        if (preparedApplication != null) {
            preparedApplication.setCustomer(customerServiceConverter.prepareCustomerObject(new Customer(), excelIndexes, row));
            return application;
        } else return null;
    }

    public Application prepareApplicationObjectFromPanel(Application application, Map<String, String> fixedValues) throws IllegalAccessException {
        Application preparedApplication = getPropertyValueByObject(application, fixedValues);
        if (preparedApplication != null) {
            preparedApplication.setCustomer(customerServiceConverter.prepareCustomerObjectFromPanel(application.getCustomer(), fixedValues));
            return application;
        } else
            return null;
    }

    public Application prepareApplicationObjectForChangePrimaryAccount(Application application, Map<String, String> fixedValues) throws IllegalAccessException {
        Application preparedApplication = getPropertyValueByObject(application, fixedValues);
        if (preparedApplication != null) {
            preparedApplication.setCustomer(customerServiceConverter.prepareCustomerObjectForChangePrimaryAccount(new Customer(), fixedValues));
            return application;
        } else
            return null;
    }




    public Application prepareApplicationForAccount(Application application, Map<String, String> fixedValues, List<Account> accountList) throws IllegalAccessException {
        Application preparedApplication = getPropertyValueByObject(application, fixedValues);
        if (preparedApplication != null) {
            preparedApplication.setCustomer(customerServiceConverter.prepareCustomerObjectForAccount(new Customer(), fixedValues, accountList));
            return application;
        } else
            return null;
    }


    public Application prepareApplicationForChangeMobileNumber(Application application, Map<String, String> fixedValues, List<Contact> contactList) throws IllegalAccessException {
        Application preparedApplication = getPropertyValueByObject(application, fixedValues);
        if (preparedApplication != null) {
            preparedApplication.setCustomer(customerServiceConverter.prepareCustomerObjectForChangeMobileNumber(new Customer(), fixedValues, contactList));
            return application;
        } else
            return null;
    }

    public Application prepareApplicationForChangeProduct(Application application, Map<String, String> fixedValues) throws IllegalAccessException {
        Application preparedApplication = getPropertyValueByObject(application, fixedValues);
        if (preparedApplication != null) {
            preparedApplication.setCustomer(customerServiceConverter.prepareCustomerObjectForChangeProduct(new Customer(), fixedValues));
            return application;
        } else
            return null;
    }
}
