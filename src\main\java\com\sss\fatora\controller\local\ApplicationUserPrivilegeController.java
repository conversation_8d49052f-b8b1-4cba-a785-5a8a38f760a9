package com.sss.fatora.controller.local;

import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.ApplicationUserPrivilegeDao;
import com.sss.fatora.dao.local.PrivilegeDao;
import com.sss.fatora.domain.local.ApplicationUserPrivilege;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.service.local.ApplicationUserPrivilegeService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/user-privilege")
public class ApplicationUserPrivilegeController extends GenericController<ApplicationUserPrivilegeService, ApplicationUserPrivilegeDao, ApplicationUserPrivilege, Integer> {
    @RequestMapping(method = RequestMethod.GET, path = "/by-user")
    public ResponseObject getByUserId(@RequestParam("userId") Integer userId) {
        try {
            Set<ApplicationUserPrivilege> applicationUserPrivilegeList = service.getAllPrivilegeForUser(userId);
            return applicationUserPrivilegeList != null ?
                    new ResponseObject(ResponseObject.Text.FETCHEDSUCCESSFULY, ResponseObject.ReturnCode.SUCCESS, null, applicationUserPrivilegeList) :
                    new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);

        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        }
    }

    @RequestMapping(method = RequestMethod.POST, path = "/assign-privileges")
    public ResponseObject AssignUserPrivileges(@RequestParam("userId") Integer userId,
                                               @ParameterName(value = "addedPrivileges", required = false) List<Integer> addedPrivileges,
                                               @ParameterName(value = "deletedPrivileges", required = false) List<Integer> deletedPrivileges) {
        try {
            Boolean managePrivileges = service.managePrivileges(userId, addedPrivileges, deletedPrivileges);
            return managePrivileges != null ?
                    new ResponseObject(ResponseObject.Text.ADDEDSUCCESSFULY,
                            ResponseObject.ReturnCode.SUCCESS, null, managePrivileges) :
                    new ResponseObject(ResponseObject.Text.ADDINGFAILED,
                            ResponseObject.ReturnCode.FAILED, null, null);

        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseObject(ResponseObject.Text.ADDINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        }
    }


    @RequestMapping(method = RequestMethod.GET, path = "/available-privileges")
    public ResponseObject getAvailablePrivilegesForUser(@RequestParam("userId") Integer userId) {
        try {
            List<Privilege> availablePrivileges = service.getAvailablePrivilegesForUser(userId);
            return availablePrivileges != null ?
                    new ResponseObject(ResponseObject.Text.FETCHEDSUCCESSFULY, ResponseObject.ReturnCode.SUCCESS, null, availablePrivileges) :
                    new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);

        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        }
    }
}
