package com.sss.fatora.domain.middleware.acquiring.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class GetAcquiringRequestFilterDto {
    private String requestStatus;
    private String actionType;
    private String entityName;
    private String entityId;
    private Date    approvalDate;
    private Boolean archived;
}
