package com.sss.fatora.domain.local.application;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "Issue_Application")
public class IssueApplication extends LocalDomain {

    public IssueApplication(Long id){this.id = id;}

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id")
    private Long id;

    // Set After The Card Is Request For Creation
    @Column(name = "Card_Status")
    private String cardStatus;

    // Not Required For Card Creation
    @Column(name = "Status")
    private String status;

    // Set After The Card Is Submitted
    @Column(name = "Submit_Date")
    private Date submitDate;

    // Set After Archiving
    @Column(name = "Archive_Date")
    private Date archiveDate;

    // Set After Card Creation
    @Column(name = "Card_Number")
    private String cardNumber;

    // Required For Card Creation
    @Column(name = "Application_Number")
    private String applicationNumber;

    // Required For Card Creation
    @Column(name = "Instant_Card")
    private Integer instant;

    /* +++++++++++Customer Section+++++++++++ */
    // Required For Card Creation
    @Column(name = "Customer_Number")
    private String customerNumber;

    // Required For Card Creation
    @Column(name = "Customer_Branch")
    private String customerBranch;

    // Required For Card Creation
    @Column(name = "Customer_Nationality")
    private String customerNationality;

    // Required For Card Creation
    @Column(name = "Customer_Mobile_Number")
    private String customerMobileNumber;

    // Required For Card Creation
    @Column(name = "Customer_Email_Address")
    private String customerEmailAddress;

    // Required For Card Creation
    @Column(name = "Customer_Country")
    private String customerCountry;

    // Required For Card Creation
    @Column(name = "Customer_Region")
    private String customerRegion;

    // Required For Card Creation
    @Column(name = "Customer_City")
    private String customerCity;

    // Required For Card Creation
    @Column(name = "Customer_Street")
    private String customerStreet;

    // Required For Card Creation
    @Column(name = "Customer_House")
    private String customerHouse;

    // Required For Card Creation
    @Column(name = "Customer_Apartment")
    private String customerApartment;

    /* +++++++++++Customer Person Section+++++++++++ */
    // Required For Card Creation
    @Column(name = "Customer_Person_First_Name")
    private String customerPersonFirstName;

    // Required For Card Creation
    @Column(name = "Customer_Person_Last_Name")
    private String customerPersonLastName;

    // Required For Card Creation
    @Column(name = "Customer_Person_Father_Name")
    private String customerPersonFatherName;

    // Required For Card Creation
    @Column(name = "Customer_Person_Mother_Name")
    private String customerPersonMotherName;

    // Required For Card Creation
    @Column(name = "Customer_Person_Gender")
    private String customerPersonGender;

    // Required For Card Creation
    @Column(name = "Customer_Person_Date_Of_Birth")
    private Date customerPersonDateOfBirth;

    // Required For Card Creation
    @Column(name = "Customer_Person_Place_Of_Birth")
    private String customerPersonPlaceOfBirth;

    // Required For Card Creation
    @Column(name = "Customer_Person_IdType")
    private String customerPersonIdType;

    // Required For Card Creation
    @Column(name = "Customer_Person_IdNumber")
    private String customerPersonIdNumber;

    /* +++++++++++Card Section+++++++++++ */
    // Required For Card Creation
    @Column(name = "Card_Product")
    private String cardProduct;

    // Required For Card Creation
    @Column(name = "Card_Contract_Type")
    private String cardContractType;

    // Required For Card Creation
    @Column(name = "Card_Delivery_Branch")
    private String cardDeliveryBranch;

    // Required For Card Creation
    @Column(name = "Card_Type")
    private String cardType;

    /* +++++++++++CardHolder Section+++++++++++ */
    // Required For Card Creation
    @Column(name = "Cardholder_Number")
    private String cardholderNumber;

    // Required For Card Creation
    @Column(name = "Cardholder_Name")
    private String cardholderName;

    // Required For Card Creation
    @Column(name = "Cardholder_Mobile_Number")
    private String cardholderMobileNumber;

    /* +++++++++++CardHolder Person Section+++++++++++ */
    // Required For Card Creation
    @Column(name = "Cardholder_Person_First_Name")
    private String cardholderPersonFirstName;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_Last_Name")
    private String cardholderPersonLastName;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_Father_Name")
    private String cardholderPersonFatherName;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_Mother_Name")
    private String cardholderPersonMotherName;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_Gender")
    private String cardholderPersonGender;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_Date_Of_Birth")
    private Date cardholderPersonDateOfBirth;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_Place_Of_Birth")
    private String cardholderPersonPlaceOfBirth;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_IdType")
    private String cardholderPersonIdType;

    // Required For Card Creation
    @Column(name = "Cardholder_Person_IdNumber")
    private String cardholderPersonIdNumber;

    @Column(name = "Customer",length = 1023)
    private String stringCustomer;

    @Column(name = "Card",length = 1023)
    private String stringCard;

    @Column(name = "Account",length = 2047)
    private String stringAccounts;

    @Column(name = "Bank_Code")
    private String bankCode;

    @Column(name = "Number_Of_Accounts")
    private Integer numberOfAccounts;

    @Column(name = "Card_Response_Code")
    private String cardResponseCode;

    @Column(name = "Card_Response_Error",length = 1023)
    private String cardResponseError;

    @Transient
    LocalCard transCard;
    @Transient
    LocalCustomer transCustomer;
    @Transient
    List<LocalAccount> transAccountList;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }


}
