package com.sss.fatora.domain.smpp;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.GenericDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
/**
 * Not Used Any More
 *
 * */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "Message_Type")
public class MessageLogType implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", unique = true, nullable = false)
    private Long id;

    @Column(name = "Type")
    private String type;
    @Column(name = "Type_Name")
    private String typeName;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

//    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}
