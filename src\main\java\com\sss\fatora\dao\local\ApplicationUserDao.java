package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.ApplicationUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


public interface ApplicationUserDao extends GenericDao<ApplicationUser, Integer> {

    @Modifying
    @Query("UPDATE ApplicationUser AU SET AU.status = false WHERE AU.id = :id")
    void disableUser(@Param("id") Integer id);

    @Modifying
    @Query("UPDATE ApplicationUser AU SET AU.status = true WHERE AU.id = :id")
    void enableUser(@Param("id") Integer id);

    @Query("SELECT DISTINCT AU FROM ApplicationUser AU " +
            "LEFT JOIN FETCH AU.applicationUserPrivileges AUP " +
            "LEFT JOIN FETCH AUP.privilege P " +
            "WHERE AU.email=:email "
//            "AND AU.recordStatus <>-1 " +
            )
    ApplicationUser findByEmail(@Param("email") String email);

    @Modifying
    @Query("UPDATE ApplicationUser AU SET AU.password=:password , AU.forcePasswordUpdated=true  " +
            "WHERE AU.id=:id ")
    void updatePassword(@Param("password") String encryptedNewPass,
                        @Param("id") Integer id);


    @Query(value = "SELECT DISTINCT AU FROM ApplicationUser AU " +
            "WHERE " +
            "(:firstName is null or AU.firstName LIKE  %:firstName% )" +

            " and (:lastName is null or AU.lastName LIKE  %:lastName% )" +

            " and (:domainName is null or AU.domainName LIKE  %:domainName% ) " +

            " and (:mobile is null or AU.email LIKE  %:mobile% )" +

            " and (:userType is null or AU.userType LIKE  %:userType% )" +

            " and (:email is null or AU.email LIKE  %:email% )" +

            " and (:status is null or AU.mobile = :status )",
            countQuery = "SELECT count(DISTINCT AU)  FROM ApplicationUser AU "
    )
    Page<ApplicationUser> search(
            Pageable pageRequest,
            @Param("firstName") String firstName,
            @Param("lastName") String lastName,
            @Param("domainName") String domainName,
            @Param("email") String email,
            @Param("mobile") String mobile,
            @Param("userType") String userType,
            @Param("status") Boolean status
    );

    @Modifying
    @Query("UPDATE ApplicationUser AU SET AU.password=:password " +
            "WHERE AU.id=:applicationUserId")
    void resetPassword(@Param("applicationUserId") Integer applicationUserId,
                       @Param("password") String password);

    @Query("SELECT AU FROM ApplicationUser AU " +
            "LEFT JOIN FETCH AU.bank B " +
            "WHERE B.id=:bankId " +
            "AND AU.recordStatus<>-1")
    List<ApplicationUser> findByBankId(@Param("bankId") Integer id);

    @Query("SELECT AU.id AS id, AU.fullName AS fullName FROM ApplicationUser AU " +
            "WHERE AU.recordStatus<>-1")
    List<Object[]> findAllByStatus();

@Query("SELECT AU.id AS id, AU.fullName AS fullName FROM ApplicationUser AU " +
       "LEFT JOIN AU.bank B " +
       "WHERE (B.id = :bankId OR AU.userType = 'internal') " +
       "AND AU.recordStatus <> -1")
List<Object[]> getExternalAndInternalUsers(@Param("bankId") Integer id);

}
