package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

@Setter
@Getter
public class ServiceObject extends GenericConverter{
    private String ref_id;
  //  @ExcelProperty(name = "ns1:start_date7")
   // private String start_date;

    public ServiceObject() {
    }

    public ServiceObject(String ref_id) {
        this.ref_id = ref_id;
    }

    public String getRef_id() {
        return ref_id;
    }
    @XmlAttribute(name = "ref_id")
    public void setRef_id(String ref_id) {
        this.ref_id = ref_id;
    }
}
