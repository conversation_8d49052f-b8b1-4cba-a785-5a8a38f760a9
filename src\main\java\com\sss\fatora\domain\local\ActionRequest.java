package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;
import java.util.Date;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "Action_Request")
public class ActionRequest extends LocalDomain {

    public ActionRequest(Long id) {
        this.Id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long Id;

    @Column(name = "Action_Type")
    String actionType;

    @ExcelProperty(name = "CARD NUMBER")
    @Column(name = "Card_Number")
    String cardNumber;

    @Column(name = "Action_Value")
    String actionValue;

    @Column(name = "Request_Date")
    Date requestDate;

    @Column(name = "Action_Details")
    String actionDetails;

    @Column(name = "User_Request_Status")
    String userRequestStatus;

    @Column(name = "Action_Status")
    String actionStatus;

    @Column(name = "Error_Message")
    String errorMessage;
    @Column(name = "Approval_Date")
    Date approvalDate;

    @Column(name = "Archived")
    Boolean archived = false;

//    @Column(name = "Archived_Date")
//    Date archivedDate;

    @ManyToOne
    @JoinColumn(name = "User_Id", referencedColumnName = "id")
    @JsonIgnoreProperties("actionRequests")
    ApplicationUser applicationUser;

    @Transient
    ActionRequestJSON actionRequest;


    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }

}
