package com.sss.fatora.service.export.service;

import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.local.application.IssueApplication;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class ExportIssueApplicationService {
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;
    final ExportService exportService;
    final ContentUtilService contentUtilService;

    public ExportIssueApplicationService(PaginationService paginationService, PrivilegeService privilegeService, ConfigService configService, ExportService exportService, ContentUtilService contentUtilService) {
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.exportService = exportService;
        this.contentUtilService = contentUtilService;
    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Check What Type Of Search Is Requested For Export
     *
     * */
    public byte[] export(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        if ("draft".equals(issueApplication.getStatus())) {
            return exportDrafts(issueApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("archive".equals(issueApplication.getStatus())) {
            return exportArchive(issueApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("submit".equals(issueApplication.getStatus())) {
            return exportSubmitted(issueApplication, fromDate, toDate, pagination, filterOperator);
        }
        return null;
    }


    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromUDate,toUDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection This Is A List Of Columns For Select Statement In Search Query
     * @param headers This Is A List Of Headers For Excel File
     * @param columns This Is A List Of Columns For Excel File
     *
     * This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (IssueApplication)
     * Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     *
     * */
    public byte[] search(IssueApplication issueApplication, Long fromUDate, Long toUDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection, List<String> headers, List<String> columns) throws Exception {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page<IssueApplication> localApplications;
        String additionalConstraint = "";
        String javaFormat = "yyyy-MM-dd HH:mm:ss";
        DateFormat f = new SimpleDateFormat(javaFormat);
        String searchLimitConstraint = "";
        getRouting(issueApplication);

        if (fromUDate == null) {
            fromUDate = new Date(1612178075113L).getTime();
        }
        if (toUDate == null) {
            toUDate = new Date(7258118400L * 1000).getTime();
        }
        //put product number in filter to get only user bank's cards
//        if(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank()!=null){
//            localApplication.setProductNumber(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank().getCode()+"*");
//            filterOperator.put("productNumber",Operator.CONTAINS_WITH_STAR);
//        }
        additionalConstraint = "IssueApplication.creationDate BETWEEN '" + f.format(fromUDate) + "' AND '" + f.format(toUDate) + "'" + searchLimitConstraint;
        if (pagination == null) {
            pagination = new Pagination();
        }
//        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
////            pagination.setOrderBy("IssueApplication.creationDate");
//            pagination.setOrderType("DESC");
//        }
        File file = contentUtilService.makeExcelFile();
        TempContentModel tempContentModel = contentUtilService.makeTempModel(file);
        localApplications = exportService.dynamicSearch(issueApplication
                , pagination
                , additionalConstraint
                , filterOperator
                , projection
                , file.getPath()
                , headers
                , columns);

        return contentUtilService.fetchAsBytes(tempContentModel);

    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Get Domain Columns And Then Request Search Function ( search() )
     * ( Draft Type )
     *
     * */
    public byte[] exportDrafts(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String>temp= Stream.of("creationDate","applicationNumber","cardholderName","cardholderNumber","customerNumber","creatorId","instant")
                .collect(Collectors.toList());
        List<String> projection = new ArrayList<>(temp);
        if (privileges == null || privileges.isEmpty()) {
            return null;
        }
        projection = filterProjectionList(projection, privileges);
        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("IssueApplication.creationDate");
            pagination.setOrderType("DESC");
        }
        return search(issueApplication, fromDate, toDate,  pagination, filterOperator, projection, configService.getIssueApplicationDraftHeaders(), configService.getIssueApplicationDraftColumns());
    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Get Domain Columns And Then Request Search Function ( search() )
     * ( Submit Type )
     *
     * */
    public byte[] exportSubmitted(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String>temp= Stream.of("creationDate","submitDate","applicationNumber","customerNumber","cardholderName","cardholderNumber","cardNumber","cardStatus","creatorId","instant")
                .collect(Collectors.toList());
        List<String> projection = new ArrayList<>(temp);
        if (privileges == null || privileges.isEmpty()) {
            return null;
        }
        projection = filterProjectionList(projection, privileges);
        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("IssueApplication.submitDate");
            pagination.setOrderType("DESC");
        }
        return search(issueApplication, fromDate, toDate, pagination, filterOperator, projection, configService.getIssueApplicationSubmitHeaders(), configService.getIssueApplicationSubmitColumns());
    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Get Domain Columns And Then Request Search Function ( search() )
     * ( Archive Type )
     *
     * */
    public byte[] exportArchive(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String>temp= Stream.of("creationDate","applicationNumber","cardholderName","cardholderNumber","customerNumber","numberOfAccounts","cardNumber","cardStatus","creatorId","instant")
                .collect(Collectors.toList());
        List<String> projection = new ArrayList<>(temp);
        if (privileges == null || privileges.isEmpty()) {
            return null;
        }
        projection = filterProjectionList(projection, privileges);
        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("IssueApplication.creationDate");
            pagination.setOrderType("DESC");
        }
        return search(issueApplication, fromDate, toDate, pagination, filterOperator, projection, configService.getIssueApplicationArchivedHeaders(), configService.getIssueApplicationArchivedColumns());
    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As Column) To Be Used In The
     *                   Select Section Of The Query
     * */
    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
                .map(s -> "IssueApplication." + s + " AS " + s)
                .collect(Collectors.toList());
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This Specific Domain (Customer)
     * */
    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.TRANSACTION_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    /**
     * @param filter  This Parameter Is For The Value Of Filters Send From The Front-End
     *                This Function Check If The User Is A Bank User, It Adds A New Value
     *                In The Filter
     * */
    public void getRouting(IssueApplication filter) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getBank() != null) {
            String bankCode = applicationUser.getBank().getCode();
            filter.setBankCode(bankCode);
        }
    }
}
