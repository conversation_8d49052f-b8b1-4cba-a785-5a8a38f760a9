package com.sss.fatora.service.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.sss.fatora.domain.converter.GenericConverter;
import com.sss.fatora.domain.local.SoapResponse;
import com.sss.fatora.domain.read.DTO.ReissueCardDTO;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.utils.annotation.ExcelProperty;
import com.sss.fatora.utils.service.HttpService;
import com.sss.fatora.utils.service.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ResourceUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

@PropertySource("classpath:generalProps/config.properties")
public class GenericConverterService<T extends GenericConverter> {

    @Autowired
    ExcelFileService excelFileService;

    @Autowired
    MessageSource messageSource;

    @Autowired
    Environment environment;

    @Autowired
    HttpService httpService;

    public ReissueCardDTO getReissuePropertyValueByExcel(T genericConverter, ReissueCardDTO dto,
            Map<String, Integer> excelIndexes, Row row)
            throws IllegalAccessException {
        boolean isEmpty = true;
        Field reissueField = null;
        for (Field field : genericConverter.getClass().getDeclaredFields()) {
            if (!field.getType().equals(ReissueCardDTO.class)) {
                continue;
            } else {
                reissueField = field;
                break;
            }
        }
        if (reissueField == null) {
            return null;
        }

        reissueField.setAccessible(true);
        ReissueCardDTO reissueCardDTO = dto;

        if (reissueCardDTO == null) {
            reissueCardDTO = new ReissueCardDTO();
            reissueField.set(genericConverter, reissueCardDTO); // Set it back into the genericConverter
        }

        for (Field nestedField : reissueCardDTO.getClass().getDeclaredFields()) {
            nestedField.setAccessible(true);
            ExcelProperty excelProperty = nestedField.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                Cell targetCell = row.getCell(excelIndexes.get(excelProperty.name()));
                if (targetCell != null) {
                    Object targetCellValue = excelFileService.getCellValue(targetCell);
                    if (targetCellValue != null) {
                        if (nestedField.getType().equals(String.class)) {
                            nestedField.set(reissueCardDTO, targetCellValue.toString());
                        }
                        if (nestedField.getType().equals(Integer.class)) {
                            nestedField.set(reissueCardDTO, Integer.parseInt(targetCellValue.toString()));
                        }
                        isEmpty = false;
                    } else {
                        nestedField.set(reissueCardDTO, null);
                    }
                }
            }
        }

        if (isEmpty) {
            return null;
        } else {
            return reissueCardDTO;
        }
    }

    public ReissueCardDTO getRenewPropertyValueByExcel(T genericConverter, ReissueCardDTO dto,
            Map<String, Integer> excelIndexes, Row row)
            throws IllegalAccessException {
        boolean isEmpty = true;
        Field reissueField = null;
        for (Field field : genericConverter.getClass().getDeclaredFields()) {
            if (!field.getType().equals(ReissueCardDTO.class)) {
                continue;
            } else {
                reissueField = field;
                break;
            }
        }
        if (reissueField == null) {
            return null; // No ReissueCardDTO field found
        }

        reissueField.setAccessible(true);
        ReissueCardDTO reissueCardDTO = dto;

        if (reissueCardDTO == null) {
            reissueCardDTO = new ReissueCardDTO();
            reissueField.set(genericConverter, reissueCardDTO); // Set it back into the genericConverter
        }

        for (Field nestedField : reissueCardDTO.getClass().getDeclaredFields()) {
            nestedField.setAccessible(true);
            ExcelProperty excelProperty = nestedField.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                if (!excelProperty.name().equalsIgnoreCase("OLD NUMBER")
                        && !excelProperty.name().equalsIgnoreCase("OLD EXPIRY")) {
                    Cell targetCell = row.getCell(excelIndexes.get(excelProperty.name()));
                    if (targetCell != null) {
                        Object targetCellValue = excelFileService.getCellValue(targetCell);
                        if (targetCellValue != null) {
                            if (nestedField.getType().equals(String.class)) {
                                nestedField.set(reissueCardDTO, targetCellValue.toString());
                            }
                            if (nestedField.getType().equals(Integer.class)) {
                                nestedField.set(reissueCardDTO, Integer.parseInt(targetCellValue.toString()));
                            }
                            isEmpty = false;
                        } else {
                            nestedField.set(reissueCardDTO, null);
                        }
                    }
                }
            }
        }

        if (isEmpty) {
            return null;
        } else {
            return reissueCardDTO;
        }
    }

    public T getPropertyValueByExcel(T genericConverter, Map<String, Integer> excelIndexes, Row row)
            throws IllegalAccessException {
        Boolean isEmpty = true;
        for (Field field : genericConverter.getClass().getDeclaredFields()) {
            // if the field is a list
            if (List.class.getSimpleName().equals(field.getType().getSimpleName())) {
                continue;
            }
            if (GenericConverter.class.getSimpleName().equals(field.getType().getSuperclass().getSimpleName())) {
                continue;
            } else {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (excelProperty != null) {
                    field.setAccessible(true);
                    // Cell targetCell =
                    // row.getCell(excelIndexes.get(excelProperty.name()),Row.RETURN_BLANK_AS_NULL);
                    Cell targetCell = row.getCell(excelIndexes.get(excelProperty.name()));

                    if (targetCell != null) {
                        Object targetCellValue = excelFileService.getCellValue(targetCell);
                        if (targetCellValue != null) {
                            field.set(genericConverter, targetCellValue);
                            isEmpty = false;
                        } else {
                            field.set(genericConverter, null);
                        }
                    } else
                        continue;
                } else
                    continue;
            }
        }
        if (isEmpty) {
            return null;
        } else
            return genericConverter;
    }

    public T getPropertyLanguageValueByExcel(T genericConverter, Map<String, Integer> excelIndexes, Row row)
            throws IllegalAccessException {
        Boolean isEmpty = true;
        for (Field field : genericConverter.getClass().getDeclaredFields()) {
            // if the field is a list
            if (List.class.getSimpleName().equals(field.getType().getSimpleName())) {
                continue;
            }
            if (GenericConverter.class.getSimpleName().equals(field.getType().getSuperclass().getSimpleName())) {
                continue;
            } else {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (excelProperty != null) {
                    field.setAccessible(true);
                    // Cell targetCell =
                    // row.getCell(excelIndexes.get(excelProperty.name()),Row.RETURN_BLANK_AS_NULL);
                    String name = excelProperty.name();
                    Cell targetCell = row
                            .getCell(excelIndexes.get("LANGUAGE"));

                    if (targetCell != null) {
                        Object targetCellValue = excelFileService.getCellValue(targetCell);
                        if (targetCellValue != null) {
                            field.set(genericConverter, targetCellValue);
                            isEmpty = false;
                        } else {
                            field.set(genericConverter, null);
                        }
                    } else
                        continue;
                } else
                    continue;
            }
        }
        if (isEmpty) {
            return null;
        } else
            return genericConverter;
    }

    public T getPropertyProductValueByExcel(T genericConverter, Map<String, Integer> excelIndexes, Row row)
            throws IllegalAccessException {
        Boolean isEmpty = true;
        for (Field field : genericConverter.getClass().getDeclaredFields()) {
            // if the field is a list
            if (List.class.getSimpleName().equals(field.getType().getSimpleName())) {
                continue;
            }
            if (GenericConverter.class.getSimpleName().equals(field.getType().getSuperclass().getSimpleName())) {
                continue;
             }
            //  else {
            //     ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            //     if (excelProperty != null) {
            //         field.setAccessible(true);
            //         // Cell targetCell =
            //         // row.getCell(excelIndexes.get(excelProperty.name()),Row.RETURN_BLANK_AS_NULL);
            //         Cell targetCell = row
            //                 .getCell(excelIndexes.get("PRODUCT"));

            //         if (targetCell != null) {
            //             Object targetCellValue = excelFileService.getCellValue(targetCell);
            //             if (targetCellValue != null) {
            //                 field.set(genericConverter, targetCellValue);
            //                 isEmpty = false;
            //             } else {
            //                 field.set(genericConverter, null);
            //             }
            //         } else
            //             continue;
            //     } else
            //         continue;
            // }
        }
        if (isEmpty) {
            return null;
        } else
            return genericConverter;
    }

    public T getPropertyValueByObject(T genericConverter, Map<String, String> objectIndexes)
            throws IllegalAccessException {
        Boolean isEmpty = true;
        for (Field field : genericConverter.getClass().getDeclaredFields()) {
            // if (List.class.getSimpleName().equals(field.getType().getSimpleName())) {
            // continue;
            // }
            // if
            // (GenericConverter.class.getSimpleName().equals(field.getType().getSuperclass().getSimpleName()))
            // {
            // continue;
            // }
            if (objectIndexes.containsKey(field.getName())) {
                field.setAccessible(true);
                field.set(genericConverter, objectIndexes.get(field.getName()));
                isEmpty = false;
            }
        }
        if (isEmpty) {
            return null;
        } else
            return genericConverter;
    }

    public String serializeObjectToXML(T inputObject, String outputType) {
        String response = null;
        try {
            // create an instance of `JAXBContext`
            JAXBContext context = JAXBContext.newInstance(inputObject.getClass());
            //
            // // create an instance of `Marshaller`
            Marshaller marshaller = context.createMarshaller();
            //
            // // enable pretty-print XML output
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

            // // write XML to `StringWriter`
            StringWriter xmlString = new StringWriter();

            marshaller.marshal(inputObject, xmlString);
            String applicationString = xmlString.toString().substring(55);
            String replacedApplication = applicationString.replace("<application>",
                    "<application xmlns=\"http://sv.bpc.in/SVAP\">");
            // print the XML
            // System.out.println(replacedApplication);
            if (outputType.equals("soap"))
                response = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:int=\"http://bpc.ru/sv/SVAP\">\n"
                        +
                        "\t<soapenv:Header/>\n" +
                        "\t<soapenv:Body>\n" +
                        replacedApplication +
                        // xmlString.toString() +
                        "\t</soapenv:Body>\n" +
                        "</soapenv:Envelope>";

            else
                response = xmlString.toString();
            System.out.println(response);
            return response;
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return null;
    }

    public SoapResponse deserializeXmlToObject(String xml, SoapResponse soapResponse) throws JsonProcessingException {
        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SoapResponse mappedObject = xmlMapper.readValue(xml, soapResponse.getClass());
        return mappedObject;
    }

    public SoapResponse connectToService(String body) throws JsonProcessingException {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        String url = environment.getProperty("svboWebService");
        HttpEntity<String> request = new HttpEntity<String>(body, headers);

        SoapResponse soapResponse = new SoapResponse();
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            System.out.println("****************Soap Response From Server*******************");
            System.out.println(response.toString());
            soapResponse = deserializeXmlToObject(response.getBody(), new SoapResponse());
            // soapResponse = deserializeXmlToObject(readFile(), new SoapResponse());
            return soapResponse;
        } catch (HttpStatusCodeException e) {
            soapResponse = deserializeXmlToObject(e.getResponseBodyAsString(), new SoapResponse());
            return soapResponse;
        } catch (ResourceAccessException resourceAccessException) {
            CustomUserDetails.getCurrentInstance().getErrorsList()
                    .add(messageSource.getMessage("wsdl_service_off", null, null));

            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

    }

    public String readFile() throws IOException {

        File file = ResourceUtils.getFile("classpath:success.txt");

        // Read File Content
        String content = new String(Files.readAllBytes(file.toPath()));
        System.out.println(content);
        return content;

    }

}
