package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_COUNTRY_CODES")
public class CountryCodes {
    @Id
    @Column(name = "SHORT_NAME")
    String shortName;
    @Column(name = "CODE")
    Integer code;
    @Transient
    String stringCode;
    @Column(name = "DESCRIPTION")
    String description;
    @Column(name = "PULLDOWN")
    String pullDown;

    public String getStringCode() {
        String stringCode = this.getCode().toString();
        if (stringCode.length() == 2) {
            stringCode="0" + stringCode;
        }
        if (stringCode.length() == 1) {
            stringCode="00" + stringCode;
        }
        return stringCode;
    }

    public void setStringCode(String stringCode) {
        this.stringCode = stringCode;
    }
}
