package com.sss.fatora.utils.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TempContentModel {

    private String name;
    private String physicalPath;

    private String contentExtension;

    @JsonIgnore
    private Date creationDate;

    private String relativePath;
    private String url;

    public TempContentModel(String name, String physicalPath, String relativePath, String url, String contentExtension) {
        this.name = name;
        this.physicalPath = physicalPath;
        this.relativePath = relativePath;
        this.url = url;
        this.contentExtension = contentExtension;
        //  this.creationDate = new Date();
    }

    private String contentType;

    public String getContentExtension() {
        return contentExtension;
    }

    private String contentURL;
    private byte[] content;

    public TempContentModel() {
        this.creationDate = new Date();
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public void setContentExtension(String contentExtension) {
        this.contentExtension = contentExtension;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhysicalPath() {
        return physicalPath;
    }

    public void setPhysicalPath(String physicalPath) {
        this.physicalPath = physicalPath;
    }

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getContentURL() {
        return contentURL;
    }

    public void setContentURL(String contentURL) {
        this.contentURL = contentURL;
    }

    public byte[] fetchContentBytes() throws IOException {
        File file = new File(this.getPhysicalPath());
        if (file.exists())
            return FileUtils.readFileToByteArray(file);
        BufferedInputStream in = new BufferedInputStream(new URL(getContentURL()).openStream());
        return IOUtils.toByteArray(in);
    }
}
