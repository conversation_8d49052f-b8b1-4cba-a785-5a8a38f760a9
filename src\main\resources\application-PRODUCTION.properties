#Reading Oracle DB Configuration
spring.datasource-reading-db.url=******************************************
spring.datasource-reading-db.username=READONLY
spring.datasource-reading-db.password=Read123Only
spring.datasource-reading-db.driverClassName=oracle.jdbc.OracleDriver
spring.jpa.datasource-reading-db.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
spring.datasource-reading-db.hibernate.schema=SVISTA

#Card Issuing Oracle DB Configuration
spring.datasource-card-issuing-db.url=******************************************
spring.datasource-card-issuing-db.username=FP_BO_READONLY
spring.datasource-card-issuing-db.password=FP_BO#321
spring.datasource-card-issuing-db.driverClassName=oracle.jdbc.OracleDriver
spring.jpa.datasource-card-issuing-db.dialect=org.hibernate.dialect.Oracle12cDialect
spring.datasource-card-issuing-db.hibernate.schema=MAIN


# Settlement sql server DB Configuration (Old Aria)
spring.datasource-settlement-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource-settlement-db.url=************************************************************
spring.datasource-settlement-db.username=Readonly
spring.datasource-settlement-db.password=Onlyread@1234
spring.datasource-settlement-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect

# Local sql server DB Configuration
spring.datasource-local-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource-local-db.url=*************************************************************;
spring.datasource-local-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect
spring.datasource-local-db.username=fpanel
spring.datasource-local-db.password=aA@123456

# SMPP sql server DB Configuration
spring.datasource-smpp-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource-smpp-db.url=******************************************************
spring.datasource-smpp-db.username=SMPP
spring.datasource-smpp-db.password=SMPP_2021
spring.datasource-smpp-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect


# F Notify DB Configuration
#spring.datasource-fnotify-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
#spring.datasource-fnotify-db.url=********************************************************
#spring.datasource-fnotify-db.username=Readonly
#spring.datasource-fnotify-db.password=Readonly@123
#spring.datasource-fnotify-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect


# Writing Oracle DB Configuration
spring.datasource-writing-db.url=******************************************
spring.datasource-writing-db.username=FEUPDATE
spring.datasource-writing-db.password=Fe_123_Update
spring.datasource-writing-db.driverClassName=oracle.jdbc.OracleDriver
spring.jpa.datasource-writing-db.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
spring.datasource-writing-db.hibernate.schema=SVISTA

tempFolder=tempFiles
tempFolderUrl=http://localhost:8085/tempFiles/

spring.resources.static-locations=classpath:${tempFolder}/
spring.mvc.static-path-pattern=/tempFiles/**

spring.jpa.hibernate.ddl-auto=update
spring.jpa.generate-ddl=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql = true
svboWebService=http://************:7007/sv/ApplicationService
server.address=************
server.port=8080

spring.batch.datasource.url=jdbc:h2:mem:dataflow
spring.batch.datasource.driverClassName=org.h2.Driver
spring.batch.datasource.username=sa
spring.batch.datasource.password=
spring.batch.datasource.database-platform=org.hibernate.dialect.H2Dialect
spring.batch.initialize-schema=always
spring.batch.job.enabled=false


FatoraServiceUrl=http://************:7777/
FatoraPaymentServiceUrl=http://************:7777/
FatoraFNotifyUrl=http://***********:5432/api/v1/

#Payment API Params
TERMINAL_ID_FIXED_VALUE =14740002
#Terminal , Merchant and Device
Base_Url=http://***********/
SECRET =KACxazQ4n6VkL5B
SIIB_SERVER_URL = /SIIBfatora/
FATORA_SERVER_URL = /fatora/