package com.sss.fatora.domain.middleware.terminals;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class TerminalRequestById {
    private String terminalNumber;
    private List<String> fields;
    private String sorting;
    private Integer maxResultCount;
    private Integer skipCount;
}
