package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.ActionRequest;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface ActionRequestDao extends GenericDao<ActionRequest, Long> {

        @Modifying
        @Query("UPDATE ActionRequest AR SET AR.archived =:archive ,AR.modifiedDate =:archivedDate ,AR.applicationUser.id=:userId "
                        +
                        " WHERE AR.Id IN :requestIds ")
        void archive(@Param("archive") Boolean archive,
                        @Param("requestIds") List<Long> requestIds,
                        @Param("archivedDate") Date archivedDate,
                        @Param("userId") Integer userId);

        @Modifying
        @Query("UPDATE ActionRequest AR SET AR.userRequestStatus =:userRequestStatus,AR.actionStatus=:actionStatus ,AR.applicationUser.id=:userId ,"
                        +
                        "AR.modifiedDate =:date " +
                        " WHERE AR.Id IN :requestIds ")
        void rejectRequest(@Param("requestIds") List<Long> actionIds,
                        @Param("userRequestStatus") String userRequestStatus,
                        @Param("actionStatus") String actionStatus,
                        @Param("userId") Integer userId,
                        @Param("date") Date date);

        @Query("SELECT AR FROM ActionRequest AR " +
            "WHERE AR.id = :requestId ")
        ActionRequest getRequestById(@Param("requestId") Long requestId);
}
