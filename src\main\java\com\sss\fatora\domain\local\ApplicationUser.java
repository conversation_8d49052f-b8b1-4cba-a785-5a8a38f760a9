package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;
import java.util.Set;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "Application_User")
public class ApplicationUser extends LocalDomain {

    public ApplicationUser(Integer id) {
        this.id = id;
    }


    /*@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "APPLICATION_USER_SEQ")
    @SequenceGenerator(sequenceName = "APPLICATION_USER_SEQ",initialValue = 1, allocationSize = 1, name = "APPLICATION_USER_SEQ")*/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "First_Name")
    @Nationalized
    private String firstName;

    @Column(name = "Last_Name")
    @Nationalized
    private String lastName;

    @Column(name = "Domain_Name")
    private String domainName;

    @Column(name = "Full_Name")
    @Nationalized
    private String fullName;

    @Column(name = "Email")
    @Nationalized
    private String email;

    @Column(name = "Mobile")
    @Nationalized
    private String mobile;

    @Column(name = "User_Type")
    @Nationalized
    private String userType;

    @Column(name = "Status")
    private Boolean status;

    @Column(name = "Monthly_Search_Limit")
    private Integer monthlySearchLimit;

    @Column(name = "User_Branch")
    private Long userBranch;

    @Column(name = "Max_Export_Limit")
    private Integer maxExportLimit;

    @PrePersist
    public void setFullName() {
        this.fullName = this.firstName+" "+this.lastName;
    }

    @Column(name = "Password", updatable = false)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @Nationalized
    private String password;

    @Column(name = "Force_Password_Updated")
    private Boolean forcePasswordUpdated = false;

    @ManyToOne
    @JoinColumn(name = "Bank_Id", referencedColumnName = "id")
    @JsonIgnoreProperties("applicationUsers")
    private Bank bank;

    @OneToMany(mappedBy = "applicationUser")
    @JsonIgnoreProperties("applicationUser")
    private Set<ApplicationUserPrivilege> applicationUserPrivileges;

    @OneToMany(mappedBy = "applicationUser")
    @JsonIgnoreProperties("applicationUser")
    private Set<Log> logs;

    @OneToMany(mappedBy = "applicationUser")
    @JsonIgnoreProperties("applicationUser")
    private Set<ActionRequest> actionRequests;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}
