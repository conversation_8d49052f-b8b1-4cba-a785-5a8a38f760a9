package com.sss.fatora.service.smpp;

import com.sss.fatora.dao.smpp.MessageLogDao;
import com.sss.fatora.dao.smpp.MessageLogTypeDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.smpp.MessageLog;
import com.sss.fatora.domain.smpp.MessageLogType;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MessageLogService extends GenericService<MessageLogDao,MessageLog,Long> {
    private final PrivilegeService privilegeService;
    final ConfigService configService;
    private final MessageLogTypeDao messageLogTypeDao;
    public MessageLogService(PrivilegeService privilegeService, ConfigService configService, MessageLogTypeDao messageLogTypeDao) {
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.messageLogTypeDao = messageLogTypeDao;
    }

    public Page<MessageLog> search(MessageLog messageLog, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException, ParseException {
        Page<MessageLog> messageLogs;
        String additionalConstraint = "";
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        String bins = "";
        if (messageLog == null)
            messageLog = new MessageLog();

        if (fromDate == null) {
            fromDate = new Date(*********).getTime();
        }
        if (toDate == null) {
            toDate = new Date(7258118400L * 1000).getTime();
        }
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");

        if (applicationUser.getBank() != null)
            bins = applicationUser.getBank().getBinCodes().stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));

        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            additionalConstraint = "substring(MessageLog.cardNo,1,6) IN (" + bins + ") " + "AND MessageLog.receivedDateToSMPP BETWEEN '" + f.format(DateUtils.getStartDayOfDate(fromDate)) + "' AND '" + f.format(DateUtils.getEndDayOfDate(toDate)) + "'";
        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()))
            additionalConstraint = "MessageLog.receivedDateToSMPP BETWEEN '" + f.format(DateUtils.getStartDayOfDate(fromDate)) + "' AND '" + f.format(DateUtils.getEndDayOfDate(toDate)) + "'";

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy()==null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("MessageLog.receivedDateToSMPP");
            pagination.setOrderType("DESC");
        }

        messageLogs = this.dynamicSearch(messageLog
                , pagination
                , additionalConstraint
                , filterOperator
                , projection,false);
        String stars = "";
//   *************Coded Card Number by Stars
        for(int j=0 ;j<messageLogs.getContent().size();j++){
            if(((Map<Object, Object>)messageLogs.getContent().get(j)).get("cardNo")!=null){
    for(int i=6;i<((Map<Object, Object>)messageLogs.getContent().get(j)).get("cardNo").toString().length() - 4;i++)
        {
            stars+="*";
        }

            String codedCardNo = ((Map<Object, Object>)messageLogs.getContent().get(j)).get("cardNo").toString().substring(0, 6) +
                    stars +((Map<Object, Object>)messageLogs.getContent().get(j)).get("cardNo").toString().substring(((Map<Object, Object>)messageLogs.getContent().get(j)).get("cardNo").toString().length() - 4);
             stars="";
            ((Map<Object,Object>) messageLogs.getContent().get(j)).put("cardNo",codedCardNo);}}
        return messageLogs;
    }

    public Page<MessageLog> mainSearch(MessageLog messageLog, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperators) throws IntrospectionException, ParseException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getBankSMSColumns());
        if (privileges == null || privileges.isEmpty())
            return Page.empty();
        projection = filterProjectionList(projection, privileges);
        Page<MessageLog> messageLogs = search(messageLog, fromDate, toDate, pagination, filterOperators, projection);
        return messageLogs;
    }

    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        List<String> projections = projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> "MessageLog." + s + " AS " + s)
                .collect(Collectors.toList());
        projections.add("MessageLog.receivedDateToSMPP AS receivedDateToSMPP");
        return projections;
    }

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.BANK_SMS_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    public List<MessageLogType> getMessageTypes() {
        return messageLogTypeDao.findAll();
    }
}
