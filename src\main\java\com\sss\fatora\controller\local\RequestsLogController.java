package com.sss.fatora.controller.local;

import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.RequestsLogDao;
import com.sss.fatora.domain.local.RequestsLog;
import com.sss.fatora.service.local.RequestsLogService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/requests-log")
public class RequestsLogController extends GenericController<RequestsLogService, RequestsLogDao, RequestsLog, Long> {

    @RequestMapping(value = "/by-request-id", method = RequestMethod.GET)
    public ResponseObject getLogsByRequestId(@ParameterName(value = "requestId") String requestId) {
        List<RequestsLog> requestsLogs = service.getLogsByRequestId(requestId);
        return requestsLogs != null ?
                ResponseObject.FETCHED_SUCCESS(requestsLogs, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
