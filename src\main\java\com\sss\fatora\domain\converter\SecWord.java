package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "secret_question","secret_answer"})
public class Sec<PERSON>ord extends GenericConverter{
    @ExcelProperty(name = "ns1:secret_question")
    private String secret_question;
    @ExcelProperty(name = "ns1:secret_answer")
    private String secret_answer;
}
