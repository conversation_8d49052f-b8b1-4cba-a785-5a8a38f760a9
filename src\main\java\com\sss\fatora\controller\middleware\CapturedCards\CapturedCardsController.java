package com.sss.fatora.controller.middleware.CapturedCards;

import com.sss.fatora.domain.middleware.CapturedCards.CaptureReasonsResponse;
import com.sss.fatora.domain.middleware.CapturedCards.CapturedCardRequest;
import com.sss.fatora.domain.middleware.CapturedCards.CapturedCardResponse;
import com.sss.fatora.service.middleware.CapturedCards.CapturedCardService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/captured-cards")
public class CapturedCardsController {
    private final CapturedCardService capturedCardService;

    public CapturedCardsController(CapturedCardService capturedCardService) {
        this.capturedCardService = capturedCardService;
    }

    @RequestMapping("/search")
    public ResponseObject search(@ParameterName("card") CapturedCardRequest capturedCardRequest,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination,
                                 HttpServletResponse response) {
        CapturedCardResponse capturedCardResponse = capturedCardService.search(capturedCardRequest, pagination, false, response);
        return capturedCardResponse != null ?
                ResponseObject.FETCHED_SUCCESS(capturedCardResponse, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/export")
    public ResponseObject export(@ParameterName("card") CapturedCardRequest capturedCardRequest,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination,
                                 HttpServletResponse response) {
        capturedCardService.search(capturedCardRequest, pagination, true, response);
        return ResponseObject.FETCHED_SUCCESS(null, null);
    }

    @RequestMapping("/capture-reasons")
    public ResponseObject getCaptureReasons(HttpServletResponse response){
        CaptureReasonsResponse captureReasons = capturedCardService.getCaptureReasons(response);
        return captureReasons != null ?
                ResponseObject.FETCHED_SUCCESS(captureReasons, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


}
