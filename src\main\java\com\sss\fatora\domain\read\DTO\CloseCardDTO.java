package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CloseCardDTO {
    @JsonProperty(value = "Card_Number")
    String Card_Number;
    @JsonProperty(value = "Card_Status")
    String Card_Status;
    @JsonProperty(value = "Expiry_Date")
    String Expiry_Date;
    @JsonProperty(value = "Customer_Number")
    String Customer_Number;

}
