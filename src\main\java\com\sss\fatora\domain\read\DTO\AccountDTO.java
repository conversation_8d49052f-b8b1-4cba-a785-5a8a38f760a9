package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AccountDTO {

    @JsonProperty(value = "Customer_Number")
    String Customer_Number;
    @JsonProperty(value = "Card_Number")
    String Card_Number;
    @JsonProperty(value = "Branch")
    String Branch;
    @JsonProperty(value = "Accounts")
    CustomerAccountDTO[] Accounts;

}
