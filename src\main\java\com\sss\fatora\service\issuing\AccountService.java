package com.sss.fatora.service.issuing;

import com.sss.fatora.dao.issuing.AccountDao;
import com.sss.fatora.domain.issuing.Account;
import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.security.model.CustomUserDetails;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AccountService {


    final AccountDao accountDao;

    public AccountService(AccountDao accountDao, CustomerService customerService) {
        this.accountDao = accountDao;
        this.customerService = customerService;
    }

    /**
     * @param customerId This Parameter Is For The Customer Id
     *                   <p>
     *                   This Function Get All Accounts For This Specific Customer
     */
    public List<Account> getAccountByCustomerId(Long customerId) {
        List<Account> requestedAccounts = accountDao.getAccountByCustomerId(customerId);
        return requestedAccounts;
    }

    final CustomerService customerService;

    public Map<String, Object> getAccountByCustomerNumber(String customerNumber,int status) {
        try {
            Map customerMap = customerService.getFirstByCustomerNumber(customerNumber);
            Customer customer = null;
            if (customerMap != null)
                customer = ((Customer) customerMap.get("Customer"));

            if (customer == null) {
                CustomUserDetails.getCurrentInstance().getErrorsList().add("Customer is Not Found");
                return null;
            }
            String customerBranch = customer.getCustomerBranch();
            List<Account> requestedAccounts=new ArrayList<>();
            if(status==1)
               requestedAccounts = accountDao.getActiveAccountByCustomerId(customer.getId(),"ACSTACTV");
           else  requestedAccounts = accountDao.getAccountByCustomerId(customer.getId());

            Map<String, Object> response = new HashMap<>();
            response.put("customerBranch", customerBranch);
            response.put("accounts", requestedAccounts);
            return response;
        } catch (Exception exception) {
            exception.printStackTrace();
            return null;
        }
    }


}
