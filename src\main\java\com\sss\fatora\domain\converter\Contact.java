package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "command","contact_type","preferred_lang","contact_data"})
public class Contact extends GenericConverter{
    @ExcelProperty(name = "ns1:command6")
    private String command;
    @ExcelProperty(name = "ns1:contact_type")
    private String contact_type;
    @ExcelProperty(name = "ns1:preferred_lang")
    private String preferred_lang;
    private ContactData contact_data;
}
