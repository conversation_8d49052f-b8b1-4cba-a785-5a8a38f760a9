package com.sss.fatora.domain.read;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "FAT_FP_CARDS_BO")
public class CardsBo {
    @Id
    @Column(name = "CARD_NUMBER")
    String cardNumber;
    @Column(name = "CARD_TYPE_ID")
    Integer cardTypeId;
    @Column(name = "CUSTOMER_NUMBER")
    String customerNumber;
    @Column(name = "CARDHOLDER_NUMBER")
    String cardHolderNumber;
    @Column(name = "CARDHOLDER_NAME")
    String cardHolderName;
    @Column(name = "CONTRACT_NUMBER")
    String contractNumber;
    @Column(name = "AGENT_NUMBER")
    String agentNumber;
    @Column(name = "AGENT_ID")
    String agentId;
    @Column(name = "COMMUN_METHOD")
    String communMethod;
    @Column(name = "COMMUN_ADDRESS")
    String communAdress;
    @Column(name = "PRODUCT_NUMBER")
    String productNumber;

    @Column(name = "STATUS")
    String status;

    @Column(name = "STATUS_DESC")
    String statusDesc;

    @Column(name = "STATE")
    String state;

    @Column(name = "STATE_DESC")
    String stateDesc;

    @Column(name = "ISS_DATE")
    Date issueDate;

    @Column(name = "EXPIR_DATE")
    Date expiryDate;
    @Column (name= "PREFERRED_LANG")
    String preferredLang;


}
