package com.sss.fatora.service.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.sss.fatora.dao.local.IssueApplicationDao;
import com.sss.fatora.domain.issuing.XmlIssueError;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.application.*;
import com.sss.fatora.domain.local.application.dto.*;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.service.issuing.IdTypeService;
import com.sss.fatora.service.read.*;
import com.sss.fatora.utils.constants.ApplicationStatus;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.RestStatus;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.HttpService;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sss.fatora.utils.constants.ActionStatus.CONNECTION_ERROR;


@Service
@Transactional("localDBTransactionManager")
public class IssueApplicationService extends GenericService<IssueApplicationDao, IssueApplication, Long> {
    final Environment environment;
    final RestTemplate restTemplate;
    final AgentsService agentsService;
    final ProductsService productsService;
    final LanguagesService languagesService;
    final CountryCodesService countryCodesService;
    final CurrencyCodesService currencyCodesService;
    final AccountTypeService accountTypeService;
    final IdTypeService idTypeService;
    final HttpService httpService;
    final PrivilegeService privilegeService;

    public IssueApplicationService(Environment environment,
                                   RestTemplate restTemplate,
                                   AgentsService agentsService,
                                   ProductsService productsService,
                                   LanguagesService languagesService,
                                   CountryCodesService countryCodesService,
                                   CurrencyCodesService currencyCodesService,
                                   AccountTypeService accountTypeService,
                                   IdTypeService idTypeService,
                                   HttpService httpService,
                                   PrivilegeService privilegeService){
        this.environment = environment;
        this.restTemplate = restTemplate;
        this.agentsService = agentsService;
        this.productsService = productsService;
        this.languagesService = languagesService;
        this.countryCodesService = countryCodesService;
        this.currencyCodesService = currencyCodesService;
        this.accountTypeService = accountTypeService;
        this.idTypeService = idTypeService;
        this.httpService = httpService;
        this.privilegeService = privilegeService;
    }

    public Map getFormInfo() {
        Map<String, Object> formInfoMap = new HashMap<>();
        formInfoMap.put("agents", agentsService.getAllAgentsByBankCode());
        formInfoMap.put("languages", languagesService.getAll());
        formInfoMap.put("countryCodes", countryCodesService.getCountryCodes());
        formInfoMap.put("currencyCodes", currencyCodesService.getAll());
        formInfoMap.put("accountType", accountTypeService.getAll());
        formInfoMap.put("products", productsService.getAllProductsByBankCode());
        formInfoMap.put("IdTypes", idTypeService.getIdTypes());
        return formInfoMap;
    }

    /**
     * @param issueApplication This Is The Object To Be Used
     *
     * This Function Is Used To Create A Card Locally And Then Create It In Fatora Server (using their APi)
     *
     * Note :'Instant ' Mean That The Card Is Created Quicker Than ( normalCardCreation() )
     * */
    public ResponseEntity instantCardCreation(IssueApplication issueApplication) throws Exception{
        issueApplication.setInstant(1);
        issueApplication.setApplicationNumber(generateApplicationNumber(issueApplication));
        issueApplication.setNumberOfAccounts(issueApplication.getTransAccountList().size());
        ResponseEntity response = cardCreation(issueApplication);
        return response;
    }

    /**
     * @param issueApplication This Is The Object To Be Used
     *
     * This Function Is Used To Just Create A Card Locally
     *
     * Note :'Normal' Mean That The Card Is Created Slower Than ( instantCardCreation() )
     * */
    public IssueApplication normalCardCreation(IssueApplication issueApplication) throws Exception{
        if (issueApplication.getInstant()==null){
            return null;
        }
        issueApplication.setApplicationNumber(generateApplicationNumber(issueApplication));
        issueApplication.setStatus(ApplicationStatus.DRAFT.getStatus());
        issueApplication.setNumberOfAccounts(issueApplication.getTransAccountList().size());
        createInDB(issueApplication);
        return issueApplication;
    }

    /**
     * @param issueApplications This Is A List Of Objects
     *
     * This Function Is Used To request Create API Of Any Type Of Locally Created Cards In Fatora Server
     *
     * */
    public  List<ResponseEntity> submitCard(List<IssueApplication> issueApplications) throws Exception{
        List<ResponseEntity> responseEntities = new ArrayList<ResponseEntity>();
        ResponseEntity responseEntity;
        for (IssueApplication issueApplication : issueApplications) {
//            ObjectMapper objectMapper = new ObjectMapper();
            if (issueApplication.getApplicationNumber() == null)
                issueApplication.setApplicationNumber(this.generateApplicationNumber(issueApplication));
//            issueApplication.setTransAccountList(objectMapper.readValue(issueApplication.getStringAccounts(), new TypeReference<List<LocalAccount>>() {}));
            issueApplication.setTransAccountList(issueApplication.getTransAccountList());
            issueApplication.setSubmitDate(new Date());
            if (CustomUserDetails.getCurrentInstance() != null)
                issueApplication.setModifierId(CustomUserDetails.getCurrentInstance().getApplicationUser().getId());
            issueApplication.setNumberOfAccounts(issueApplication.getTransAccountList().size());
            responseEntity = cardCreation(issueApplication);
            responseEntities.add(responseEntity);
        }
        return responseEntities;
    }

    /**
     * @param issueApplication This Is The Object To Be Used
     *
     * This Function Convert The Received Object To DTO ( convertToDTO() ) Then Request The Api From
     * Fatora Server For Adding A Card ( sendCard() ) Then When The Response Is Received We Set Some
     * Values To The Object And Then Save The Application ( createInDB() )
     *
     * */
    public ResponseEntity cardCreation(IssueApplication issueApplication) throws Exception {
        IssueApplicationDTO applicationDTO = convertToDTO(issueApplication);
        ResponseEntity<IssueApplicationResponseDTO> response = sendCard(applicationDTO);
        if (issueApplication.getCardholderNumber() == null) {
            issueApplication.setCardholderNumber(response.getBody().getCardData().getCardholderNumber());
        }
        issueApplication.setStatus(ApplicationStatus.SUBMIT.getStatus());
        if (response.getBody().getCardData().getCardNumber() != null) {
            issueApplication.setCardStatus(RestStatus.SUCCESS.getStatus());
            issueApplication.setCardNumber(response.getBody().getCardData().getCardNumber());
        } else {
            issueApplication.setCardStatus(RestStatus.FAILED.getStatus());
//        }
        if (response.getBody().getErrorMessage() != null) {
            if (!response.getBody().getErrorMessage().startsWith("<") ||
                    response.getBody().getErrorMessage().contains("Application Number is not Unique")) {
                issueApplication.setCardResponseError(response.getBody().getErrorMessage());
            } else {
                XmlMapper xmlMapper = new XmlMapper();
                xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                XmlIssueError res = xmlMapper.readValue(response.getBody().getErrorMessage(), XmlIssueError.class);
                issueApplication.setCardResponseError(res.getFaultString());
            }
        } else
            issueApplication.setCardResponseError(CONNECTION_ERROR.getType());
    }
        issueApplication.setCardResponseCode(response.getBody().getContainerResponseCode());
        issueApplication.setCardholderNumber(response.getBody().getCardData().getCardholderNumber());
        createInDB(issueApplication);
        return response;
    }


    /**
     * @param issueApplication This Is The Entity To Be Saved In Db
     *
     * This Function Save The Application In The Db
     * */
    public void createInDB(IssueApplication issueApplication) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        String Accounts = objectMapper.writeValueAsString(issueApplication.getTransAccountList());
        if (applicationUser.getBank() != null){
            issueApplication.setBankCode(applicationUser.getBank().getCode());
        }
        issueApplication.setStringAccounts(Accounts);
        this.merge(issueApplication);
    }

    /**
     * This Function Is For Generate A Random Number For Application Number
     * */
    public String generateApplicationNumber(IssueApplication issueApplication){
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        Random random = new Random();
        Integer int_random = random.nextInt(*********);
        String applicationNumber = timestamp.getTime() + String.valueOf(int_random);
        return applicationNumber;
    }

    /**
     * @param issueApplication This Parameter Represents The Application To Be Send
     *
     * This Function Request The Api From Fatora Server For Adding Card
     *
     * */
    public ResponseEntity sendCard(IssueApplicationDTO issueApplication){
        String url = environment.getProperty("FatoraServiceUrl") +environment.getProperty("createCard");
        String username = environment.getProperty("BasicAuthUsername");
        String password = environment.getProperty("BasicAuthPassword");
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(username, password);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<IssueApplicationDTO> request = new HttpEntity<IssueApplicationDTO>(issueApplication, headers);
        ResponseEntity<IssueApplicationResponseDTO> response = restTemplate.postForEntity(url,request,IssueApplicationResponseDTO.class,new Object());
        return response;
    }

    /**
     * @param issueApplication This Parameter Is For Setting The Value Of The IssueApplicationDTO
     *
     * This Function Convert IssueApplication Object To IssueApplicationDTO Object
     * */
    public IssueApplicationDTO convertToDTO(IssueApplication issueApplication){
        IssueApplicationDTO applicationDTO = new IssueApplicationDTO();
        applicationDTO.setApplicationNumber(issueApplication.getApplicationNumber());
        applicationDTO.setInstant(issueApplication.getInstant());
        applicationDTO.setCard(setCardVariables(issueApplication));
        applicationDTO.setCustomer(setCustomerVariables(issueApplication));
        List<LocalAccountDTO> accountDTOS = new ArrayList<>();
        for (LocalAccount account : issueApplication.getTransAccountList()) {
            LocalAccountDTO accountDTO = setAccountVariables(account);
            accountDTOS.add(accountDTO);
        }
        applicationDTO.setAccounts(accountDTOS);
        return applicationDTO;
    }

    /**
     * @param account This Parameter Is For Setting The Value Of The LocalAccountDTO
     *
     * This Function Convert LocalAccount Object To LocalAccountDTO Object
     * */
    public LocalAccountDTO setAccountVariables(LocalAccount account){
        LocalAccountDTO accountDTO = new LocalAccountDTO();
        accountDTO.setAccountNumber(account.getAccountNumber());
        accountDTO.setAccountType(account.getAccountType());
        accountDTO.setAccountCurrency(account.getCurrency());
        accountDTO.setIsATMDefault(account.getIs_atm_default());
        accountDTO.setIsPOSDefault(account.getIs_pos_default());
        return accountDTO;
    }

    /**
     * @param issueApplication This Parameter Is For Setting The Value Of The LocalCardDTO
     *
     * This Function Get The Values Of LocalCard Object From IssueApplication And Set Them Into
     * LocalCardDTO Object
     * */
    public LocalCardDTO setCardVariables(IssueApplication issueApplication){
        LocalCardDTO cardDTO = new LocalCardDTO();
        LocalCardholderDTO cardholderDTO = new LocalCardholderDTO();
        LocalPersonDTO personDTO = new LocalPersonDTO();
        personDTO.setFirstName(issueApplication.getCardholderPersonFirstName());
        personDTO.setLastName(issueApplication.getCardholderPersonLastName());
        personDTO.setFatherName(issueApplication.getCardholderPersonFatherName());
        personDTO.setMotherName(issueApplication.getCardholderPersonMotherName());
        personDTO.setGender(issueApplication.getCardholderPersonGender());
        personDTO.setDateOfBirth(issueApplication.getCardholderPersonDateOfBirth());
        personDTO.setPlaceOfBirth(issueApplication.getCardholderPersonPlaceOfBirth());
        personDTO.setIdNumber(issueApplication.getCardholderPersonIdNumber());
        personDTO.setIdType(issueApplication.getCardholderPersonIdType());
        cardholderDTO.setCardholderNumber(issueApplication.getCardholderNumber());
        cardholderDTO.setCardholderName(issueApplication.getCardholderName());
        cardholderDTO.setMobile(issueApplication.getCardholderMobileNumber());
        cardholderDTO.setPerson(personDTO);
        cardDTO.setContractType(issueApplication.getCardContractType());
        cardDTO.setCardProduct(issueApplication.getCardProduct());
        cardDTO.setCardType(issueApplication.getCardType());
        cardDTO.setDeliveryBranch(issueApplication.getCardDeliveryBranch());
        cardDTO.setCardholder(cardholderDTO);
        return cardDTO;
    }

    /**
     * @param issueApplication This Parameter Is For Setting The Value Of The LocalCustomerDTO
     *
     * This Function Get The Values Of LocalCustomer Object From IssueApplication And Set Them Into
     * LocalCustomerDTO Object
     * */
    public LocalCustomerDTO setCustomerVariables(IssueApplication issueApplication){
        LocalCustomerDTO customerDTO = new LocalCustomerDTO();
        LocalPersonDTO personDTO = new LocalPersonDTO();
        personDTO.setFirstName(issueApplication.getCustomerPersonFirstName());
        personDTO.setLastName(issueApplication.getCustomerPersonLastName());
        personDTO.setFatherName(issueApplication.getCustomerPersonFatherName());
        personDTO.setMotherName(issueApplication.getCustomerPersonMotherName());
        personDTO.setGender(issueApplication.getCustomerPersonGender());
        personDTO.setDateOfBirth(issueApplication.getCustomerPersonDateOfBirth());
        personDTO.setPlaceOfBirth(issueApplication.getCustomerPersonPlaceOfBirth());
        personDTO.setIdNumber(issueApplication.getCustomerPersonIdNumber());
        personDTO.setIdType(issueApplication.getCustomerPersonIdType());
        customerDTO.setCustomerNumber(issueApplication.getCustomerNumber());
        customerDTO.setBranch(issueApplication.getCustomerBranch());
        customerDTO.setNationality(issueApplication.getCustomerNationality());
        customerDTO.setMobile(issueApplication.getCustomerMobileNumber());
        customerDTO.setEmail(issueApplication.getCustomerEmailAddress());
        customerDTO.setCountry(issueApplication.getCustomerCountry());
        customerDTO.setRegion(issueApplication.getCustomerRegion());
        customerDTO.setCity(issueApplication.getCustomerCity());
        customerDTO.setStreet(issueApplication.getCustomerStreet());
        customerDTO.setHouse(issueApplication.getCustomerHouse());
        customerDTO.setApartment(issueApplication.getCustomerApartment());
        customerDTO.setPerson(personDTO);
        return customerDTO;
    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Check What Type Of Search Is Requested
     *
     * */
    public Page<IssueApplication> mainSearch(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination,Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, JsonProcessingException {
        if ("draft".equals(issueApplication.getStatus())) {
            return draftsSearch(issueApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("archive".equals(issueApplication.getStatus())) {
            return archiveSearch(issueApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("submit".equals(issueApplication.getStatus())) {
            return submittedSearch(issueApplication, fromDate, toDate, pagination, filterOperator);
        }
        return null;
    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Get Domain Columns And Then Request Search Function ( search() )
     * ( Draft Type )
     *
     * */
    public Page<IssueApplication> draftsSearch(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, JsonProcessingException {
        List<String> projection;
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
//        Boolean havePermission = privilegeService.privilegeFoundByUserIdAndPrivilegeName(applicationUser.getId(),"Add Instant Card");
//        if (havePermission){
        projection = Stream.of("id", "status", "cardNumber", "applicationNumber", "cardholderNumber", "customerNumber","instant","numberOfAccounts")
                    .collect(Collectors.toList());
//        }else{
//            projection = Stream.of("id", "status", "cardNumber", "applicationNumber", "cardholderNumber", "customerNumber","numberOfAccounts")
//                    .collect(Collectors.toList());
//            issueApplication.setInstant(0);
//        }
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("IssueApplication.creationDate");
            pagination.setOrderType("DESC");
        }
        Page<IssueApplication> terminals = search(issueApplication, fromDate, toDate, pagination, filterOperator, projection);

        ObjectMapper objectMapper = new ObjectMapper();
        terminals.getContent().stream().forEach(row -> {
            try {
                row.setTransAccountList(objectMapper.readValue(row.getStringAccounts(), new TypeReference<List<LocalAccount>>(){}));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        });
        return terminals;
    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Get Domain Columns And Then Request Search Function ( search() )
     * ( Submit Type )
     *
     * */
    public Page<IssueApplication> submittedSearch(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws JsonProcessingException, InvocationTargetException, IntrospectionException, IllegalAccessException, NoSuchFieldException {
        List<String> projection;
        projection = Stream.of("id", "status", "cardNumber", "applicationNumber", "cardholderNumber", "customerNumber","instant","numberOfAccounts")
                .collect(Collectors.toList());
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("IssueApplication.submitDate");
            pagination.setOrderType("DESC");
        }
        Page<IssueApplication> terminals = search(issueApplication, fromDate, toDate, pagination, filterOperator, projection);
        ObjectMapper objectMapper = new ObjectMapper();
        terminals.getContent().stream().forEach(row -> {
            try {
                row.setTransAccountList(objectMapper.readValue(row.getStringAccounts(), new TypeReference<List<LocalAccount>>(){}));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        });
        return terminals;
    }

    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *
     * This Function Get Domain Columns And Then Request Search Function ( search() )
     * ( Archive Type )
     *
     * */
    public Page<IssueApplication> archiveSearch(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, JsonProcessingException {
        List<String> projection;
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        projection = Stream.of("id", "status", "cardNumber", "applicationNumber", "cardholderNumber", "customerNumber","instant","numberOfAccounts")
                .collect(Collectors.toList());
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("IssueApplication.creationDate");
            pagination.setOrderType("DESC");
        }
        Page<IssueApplication> terminals = search(issueApplication, fromDate, toDate, pagination, filterOperator, projection);
        ObjectMapper objectMapper = new ObjectMapper();
        terminals.getContent().stream().forEach(row -> {
            try {
                row.setTransAccountList(objectMapper.readValue(row.getStringAccounts(), new TypeReference<List<LocalAccount>>(){}));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        });
        return terminals;
    }


    /**
     * @param issueApplication  This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection This Is A List Of Columns For Select Statement In Search Query
     *
     * This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (IssueApplication)
     * Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     *
     * */
    public Page<IssueApplication> search(IssueApplication issueApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection) throws NoSuchFieldException, IllegalAccessException, IntrospectionException, InvocationTargetException {
        Page<IssueApplication> issueApplications;
        String additionalConstraint = "";
        getRouting(issueApplication);
        if (issueApplication == null)
            issueApplication = new IssueApplication();

        if (fromDate == null) {
            fromDate = new Date(1612178075113L).getTime();
        }
        if (toDate == null) {
            toDate = new Date(7258118400L * 1000).getTime();
        }
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");
        additionalConstraint = "IssueApplication.creationDate between '" + f.format(fromDate) + "' AND '" + f.format(toDate) + "'";
//        if (pagination == null)
//            pagination = new Pagination();
//        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
//            pagination.setOrderBy("IssueApplication.creationDate");
//            pagination.setOrderType("DESC");
//        }
        issueApplications = this.dynamicSearch(issueApplication
                , pagination
                , additionalConstraint
                , filterOperator
                , null,false);
        return issueApplications;
    }


    /**
     * @param filter  This Parameter Is For The Value Of Filters Send From The Front-End
     *                This Function Check If The User Is A Bank User, It Adds A New Value
     *                In The Filter
     * */
    public void getRouting(IssueApplication filter) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getBank() != null) {
            String bankCode = applicationUser.getBank().getCode();
            filter.setBankCode(bankCode);
        }
    }

    /**
     * @param idList This Parameter Is A List Of Applications Ids
     *
     * This Function Moves All The Applications In The List From Submit Module To Draft Module
     * */
    public List<IssueApplication> moveToDraft(List<Long> idList) {
        List<IssueApplication> draftApplicationList = dao.getApplicationByIdList(idList);
        for (IssueApplication application : draftApplicationList) {
            if (application.getCardStatus().equals(RestStatus.FAILED.getStatus())) {
                application.setStatus(ApplicationStatus.DRAFT.getStatus());
                dao.save(application);
            } else return null;
        }
        return draftApplicationList;
    }

    /**
     * @param idList This Parameter Is A List Of Applications Ids
     *
     * This Function Moves All The Applications In The List From Submit Module To Archive Module
     * */
    public List<IssueApplication> moveToArchive(List<Long> idList) throws Exception {
        List<IssueApplication> archiveApplicationList = dao.getApplicationByIdList(idList);
        for (IssueApplication application : archiveApplicationList) {
            application.setStatus(ApplicationStatus.ARCHIVE.getStatus());
            application.setArchiveDate(new Date());
            dao.save(application);
        }
        return archiveApplicationList;
    }

    /**
     * @param issueApplicationList This Parameter Is A List Of All Applications To Be Deleted
     *
     * This Function Is For Delete All The Applications Sent By The Front End
     * */
    public Boolean deleteApplications(List<IssueApplication> issueApplicationList){
        for (IssueApplication issueApplication : issueApplicationList) {
            dao.delete(issueApplication);
        }
        return true;
    }
}
