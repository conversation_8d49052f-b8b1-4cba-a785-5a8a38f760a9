package com.sss.fatora.controller.middleware.acquiring.customers;

import com.sss.fatora.domain.middleware.acquiring.AcqCustomers.AcqCustomer;
import com.sss.fatora.domain.middleware.acquiring.AcqCustomers.GetCustomerByNumberRequest;
import com.sss.fatora.domain.middleware.acquiring.AcqCustomers.GetCustomersWithFilterRequest;
import com.sss.fatora.service.middleware.acquiring.customers.AcqCustomerService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/customers")
public class AcqCustomerController {
    private final AcqCustomerService acqCustomerService;

    public AcqCustomerController(AcqCustomerService acqCustomerService) {
        this.acqCustomerService = acqCustomerService;
    }

    @PostMapping("/by-customer-number")
    public ResponseObject getCustomersByCustomerNumber(@RequestBody GetCustomerByNumberRequest acqGetCustomerRequest) throws Exception {
        AcqCustomer acqCustomer = acqCustomerService.getCustomerByNumber(acqGetCustomerRequest);
        return ResponseObject.FETCHED_SUCCESS(acqCustomer,null);
    }

    @PostMapping("/filter")
    public ResponseObject getCustomersWithFilter(@RequestBody GetCustomersWithFilterRequest getCustomersWithFilterRequest){
        List<AcqCustomer> acqCustomers = acqCustomerService.getCustomersWithFilter(getCustomersWithFilterRequest);
        return ResponseObject.FETCHED_SUCCESS(acqCustomers,null);
    }
}
