package com.sss.fatora.utils.service;


import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.TempContentModel;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Base64;
import java.util.Date;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * ContentUtilService is server to manage tempfiles in resources (write,fetch,delete,..)
 */

@Service
@PropertySource("classpath:application.properties")
public class ContentUtilService {

    private static final Logger LOGGER = Logger.getLogger(com.sss.fatora.utils.service.ContentUtilService.class.getName());
    private final String tempFolderName;
    private final String tempFolderPath;
    private final String temFolderUrl;


    @Autowired
    ContentUtilService(Environment environment) throws IOException {
        tempFolderName = environment.getProperty("tempFolder");
        temFolderUrl = environment.getProperty("tempFolderUrl");
        File tempDir = new ClassPathResource(tempFolderName).getFile();

        tempFolderPath = tempDir.getPath();

    }


    public TempContentModel makeTempModel(byte[] content, String fileName, String mimeType, String extension) {
        try {
            File toBeAdded = new File(tempFolderPath + "/" + fileName);
            FileUtils.writeByteArrayToFile(toBeAdded, content);
            TempContentModel tempContentModel = new TempContentModel();
            tempContentModel.setName(fileName);
            tempContentModel.setContentExtension(extension);
            tempContentModel.setUrl(temFolderUrl + fileName);
            tempContentModel.setPhysicalPath(toBeAdded.getPath());
            tempContentModel.setRelativePath(toBeAdded.getAbsolutePath());
            tempContentModel.setCreationDate(new Date());
            tempContentModel.setContentType(mimeType);
            //  tempContentModel.setContent(content);
            return tempContentModel;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public TempContentModel makeTempModel(byte[] content, String mimeType, String extension) {
        return makeTempModel(content, StringUtils.generateRandomString() + "." + extension.replace(".", ""), mimeType,
                extension);
    }

    public TempContentModel makeTempModel(File toBeAdded) {
        try {
            String fileName = toBeAdded.getName();
            TempContentModel tempContentModel = new TempContentModel();
            tempContentModel.setName(fileName);
            tempContentModel.setUrl(temFolderUrl + fileName);
            tempContentModel.setPhysicalPath(toBeAdded.getPath());
            tempContentModel.setRelativePath(toBeAdded.getAbsolutePath());
            tempContentModel.setCreationDate(new Date());
            return tempContentModel;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public File makeTempFile(byte[] content) {
        String fileName = StringUtils.generateRandomString();
        File toBeAdded = new File(tempFolderPath + "/" + fileName);
        try {
            FileUtils.writeByteArrayToFile(toBeAdded, content);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return toBeAdded;
    }

    public File makeTempFile() {
        String fileName = StringUtils.generateRandomString();
        File toBeAdded = new File(tempFolderPath + "/" + fileName);
        return toBeAdded;
    }

    public File makeExcelFile() {
        String fileName = StringUtils.generateRandomString();
        File toBeAdded = new File(tempFolderPath + "/" + fileName + ".xlsx");
        return toBeAdded;
    }


    public byte[] getFromTempAsBytes(String path) throws IOException {
        File file = new File(tempFolderPath + path);
        if (file.exists()) {
            return FileUtils.readFileToByteArray(file);
        }
        return null;
    }

    public String getContentFromTempContentModel(TempContentModel tempContentModel) throws IOException {

        File file = new File(tempContentModel.getPhysicalPath());
        if (file.exists()) {
            return Base64.getEncoder().encodeToString(FileUtils.readFileToByteArray(file));
        }
        return null;
    }


    public TempContentModel makeTempContentModel(TempContentModel remoteTempContent) throws IOException {

        File file = this.fetchAsFile(remoteTempContent);
        TempContentModel tempContentModel = this.makeTempModel(file);
        tempContentModel.setContentType(tempContentModel.getContentType());
        return tempContentModel;
    }

    public File fetchAsFile(TempContentModel tempContentModel) throws IOException {
        File contentFile = new File(tempContentModel.getPhysicalPath());
        if (contentFile.exists()) {
            return contentFile;
        } else {
            contentFile = this.makeTempFile();
            FileUtils.copyURLToFile(
                    new URL(tempContentModel.getContentURL()),
                    contentFile);
        }
        return contentFile;
    }

    public byte[] fetchAsBytes(TempContentModel tempContentModel) throws IOException {
        File in = fetchAsFile(tempContentModel);
        return FileUtils.readFileToByteArray(in);
    }


    @Scheduled(fixedRate = 30 * 60 * 1000)
    private void cleanTempFiles() {
        try {
            LOGGER.info("Temp files cleaner starts");
            File tempFolder = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + tempFolderName);
            File[] tempFiles = tempFolder.listFiles();
            int deletedFile = 0;
            if (tempFiles != null && tempFiles.length > 2) {
                LOGGER.info("Start scanning " + tempFiles.length + " Files ...!");
                for (File file : tempFiles) {
                    if (!file.isDirectory()) {
                        BasicFileAttributes attr = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
                        Date fileCreationDate = new Date(attr.creationTime().toMillis());
                        long diff = DateUtils.getDateDiff(new Date(), fileCreationDate, TimeUnit.MINUTES);
                        if (diff >= 30) {
                            try {
                                file.delete();
                                deletedFile++;
                            } catch (Exception ignored) {
                            }
                        }
                    }
                }
            }
            if (deletedFile > 0) {
                LOGGER.warning(deletedFile + " Files has been Deleted ...!");
            } else {
                LOGGER.warning("no files found for delete ...!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getTempFolderName() {
        return tempFolderName;
    }


    public String getTempFolderPath() {
        return tempFolderPath;
    }

    public String getTemFolderUrl() {
        return temFolderUrl;
    }

    public void WriteDataToExcel(TreeMap<String, Object[]> treeMap, String fileName) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook();
        // spreadsheet object
        XSSFSheet spreadsheet = workbook.createSheet("Captured Cards");
        // creating a row object
        XSSFRow row;
        Set<String> keyid = treeMap.keySet();

        int rowid = 0;

        // writing the data into the sheets...
        for (String key : keyid) {
            row = spreadsheet.createRow(rowid++);
            Object[] objectArr = treeMap.get(key);
            int cellid = 0;

            for (Object obj : objectArr) {
                Cell cell = row.createCell(cellid++);
                cell.setCellValue((String) obj);
            }
        }
        // .xlsx is the format for Excel Sheets...
        // writing the workbook into the file...
        FileOutputStream out = new FileOutputStream(
                new File(temFolderUrl + fileName));
        workbook.write(out);
        out.close();
    }
}
