package com.sss.fatora.service.middleware.devices;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.middleware.device.*;
import com.sss.fatora.domain.middleware.device.Sims.AssignSimRequest;
import com.sss.fatora.domain.middleware.response.ListMiddlewareResponse;
import com.sss.fatora.domain.middleware.response.ObjectMiddlewareResponse;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.export.service.middleware.ExportDeviceService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.service.middleware.terminals.TerminalsService;
import com.sss.fatora.utils.constants.PaymentMiddlewareRequestType;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.ObjectConverter;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class DeviceService {
    private final PrivilegeService privilegeService;
    private final Environment environment;
    private final ConfigService configService;
    final RestTemplate restTemplate;
    private final ExportDeviceService exportDeviceService;

    private final ObjectConverter objectConverter;
    private static final Logger LOGGER = Logger.getLogger(TerminalsService.class.getName());

    public DeviceService(PrivilegeService privilegeService, Environment environment, ConfigService configService,
                         RestTemplate restTemplate, ExportDeviceService exportDeviceService,ObjectConverter objectConverter) {
        this.privilegeService = privilegeService;
        this.environment = environment;
        this.configService = configService;
        this.restTemplate = restTemplate;
        this.exportDeviceService = exportDeviceService;
        this.objectConverter = objectConverter;
    }

    public DeviceResponse search(DeviceRequest deviceRequest, Pagination pagination) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankPrefix = applicationUser.getBank().getPrefix();
            deviceRequest.setBank(bankPrefix);
        }
//        pagination.setOrderBy("serialNumber");
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetPOSMachines");
        List<String> fields = new ArrayList<>();
        Map<String, List<String>> fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH.getType(), null, fields);
        return setUpDeviceRequest(deviceRequest, pagination, url, fieldsMap.get("fields"));
    }

    public DeviceResponse getWithDetails(DeviceRequest deviceRequest) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetPOSMachines");
        List<String> fields = new ArrayList<>();
        Map<String, List<String>> fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH_WITH_DETAILS.getType(), null, fields);
        return setUpDeviceRequest(deviceRequest, null, url, fieldsMap.get("fields"));
    }

    private DeviceResponse setUpDeviceRequest(DeviceRequest deviceRequest, Pagination pagination, String url, List<String> projection) {
        HttpHeaders headers = new HttpHeaders();
        if (deviceRequest == null) {
            deviceRequest = new DeviceRequest();
        }
        if (pagination == null) {
            deviceRequest.setMaxResultCount(10);
            deviceRequest.setSkipCount(0);
        } else {
            deviceRequest.setSorting(pagination.getOrderBy());
            deviceRequest.setMaxResultCount(pagination.getSize());
            deviceRequest.setSkipCount(pagination.getStart());
        }
        deviceRequest.setFields(projection);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(deviceRequest, headers);
        LOGGER.info(" url :" + url);
        LOGGER.info(" \nRequest :" + new ObjectMapper().valueToTree(request).toString());
        ResponseEntity<DeviceResponse> response = restTemplate.postForEntity(url, request, DeviceResponse.class);
        LOGGER.info(" \nResponse :" + new ObjectMapper().valueToTree(response).toString());
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    private Map<String, List<String>> setFieldsByPrivilege(String type, List<String> projectionHeaders, List<String> projection) {
        List<String> privileges = getPrivilegesNamesByUserId();
        if (PaymentMiddlewareRequestType.SEARCH.getType().equalsIgnoreCase(type)) {
            projection = configService.getDeviceMainColumns();
        } else if (PaymentMiddlewareRequestType.SEARCH_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getDeviceDetailsColumns();
        } else if (PaymentMiddlewareRequestType.GET_HOSTS.getType().equalsIgnoreCase(type)) {
            projection = configService.getHostColumns();
        } else if (PaymentMiddlewareRequestType.EXPORT_WITHOUT_DETAILS.getType().equalsIgnoreCase(type)) {
            projectionHeaders = configService.getDeviceMainHeaders();
            projection = configService.getDeviceMainExportColumns();
        } else if (PaymentMiddlewareRequestType.EXPORT_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            projectionHeaders = configService.getDeviceWithDetailsHeaders();
            projection = configService.getDeviceWithDetailsColumns();
        } else {
            projection = configService.getDeviceMainColumns();
        }
        return filterProjectionList(projectionHeaders, projection, privileges);
    }

    public DeviceResponse getHosts(DeviceRequest deviceRequest) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetHosts");
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankPrefix = applicationUser.getBank().getPrefix();
            deviceRequest.setBank(bankPrefix);
        }
        return setUpDeviceRequest(deviceRequest, null, url, configService.getHostColumns());
    }

    public SIMDetailsResponse getSIMDetails(DeviceRequest deviceRequest) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetSimById");
        return setUpSIMDetailsRequest(deviceRequest, null, url);
    }

    private SIMDetailsResponse setUpSIMDetailsRequest(DeviceRequest deviceRequest, Pagination pagination, String url) {
        HttpHeaders headers = new HttpHeaders();
        if (deviceRequest == null) {
            deviceRequest = new DeviceRequest();
        }
        if (pagination == null) {
            deviceRequest.setMaxResultCount(10);
            deviceRequest.setSkipCount(0);
        } else {
            deviceRequest.setSorting(pagination.getOrderBy());
            deviceRequest.setMaxResultCount(pagination.getSize());
            deviceRequest.setSkipCount(pagination.getStart());
        }
        deviceRequest.setFields(configService.getSIMDetailsColumns());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(deviceRequest, headers);
        ResponseEntity<SIMDetailsResponse> response = restTemplate.postForEntity(url, request, SIMDetailsResponse.class);
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    public DeviceResponse export(DeviceRequest deviceRequest, Pagination pagination, Boolean withDetails, HttpServletResponse response) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankPrefix = applicationUser.getBank().getPrefix();
            deviceRequest.setBank(bankPrefix);
        }
//        pagination.setOrderBy("serialNumber");
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetPOSMachines");
        List<String> headers = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        pagination.setStart(0);
        pagination.setSize(0);
        if (withDetails) {
            Map<String, List<String>> fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_WITH_DETAILS.getType(), headers, fields);
            DeviceResponse deviceList = setUpDeviceRequest(deviceRequest, pagination, url, fieldsMap.get("fields"));
            try {
                exportDeviceService.export(deviceList.getResult().getItems(), response, fieldsMap.get("headers"), fieldsMap.get("fields"));
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        } else {
            Map<String, List<String>> fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_WITHOUT_DETAILS.getType(), headers, fields);
            DeviceResponse deviceList = setUpDeviceRequest(deviceRequest, pagination, url, fieldsMap.get("fields"));
            try {
                exportDeviceService.export(deviceList.getResult().getItems(), response, fieldsMap.get("headers"), fieldsMap.get("fields"));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = Objects.requireNonNull(CustomUserDetails.getCurrentInstance()).getApplicationUser();
        return privilegeService.getPrivilegesById(PrivilegeType.DEVICE_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
    }

    private Map<String, List<String>> filterProjectionList(List<String> headers, List<String> projection, List<String> privileges) {
        List<String> projections = new ArrayList<>();
//                projection.stream()
//                .filter(element -> privileges.contains("Devices_" + element))
//                .collect(Collectors.toList());
        int index = 0;
        List<String> headersTemp = new ArrayList<>();

        for (String proj : projection) {
            if (privileges.contains("Devices_" + proj)) {
                projections.add(proj);
                if (proj.equalsIgnoreCase("simId") || proj.equalsIgnoreCase("terminalId"))
                    continue;
                if (headers != null)
                    headersTemp.add(headers.get(index));
            }
            index++;
        }
        if (headers != null) {
            headers.clear();
            headers.addAll(headersTemp);
        }
        projection.clear();
        projection.addAll(projections);
        Map<String, List<String>> returnedMap = new HashMap<>();
        returnedMap.put("headers", headers);
        returnedMap.put("fields", projection);
        return returnedMap;
    }

    public List<Bank> getBanks() {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetAllBanks");
        return objectConverter.getResponseList(url);
    }

    public List<Owner> getOwners() {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetAllOwners");

        return objectConverter.getResponseList(url);
    }

    public List<Custodian> getCustodians() {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetAllCustodians");
        return objectConverter.getResponseList(url);
    }

    public List<Status> getStatus() {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetAllStatus");
        return objectConverter.getResponseList(url);
    }

    public void update(String deviceSerialNumber, int newId, String type) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String url = environment.getProperty("Base_Url") + environment.getProperty("UpdateUrl") + type;
        String idKeyName = "new" + type + "Id";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put(idKeyName, newId);
        requestBody.put("deviceSerialNumber", deviceSerialNumber);

        HttpEntity<Object> request = new HttpEntity<>(requestBody, headers);

        ResponseEntity<ObjectMiddlewareResponse> response = restTemplate.postForEntity(url, request, ObjectMiddlewareResponse.class);

        if (response.getBody() != null && !response.getBody().getSuccess()) {
            throw new Exception("Error during update " + type);
        }
    }


    public void assignSim(AssignSimRequest assignRequest) throws Exception {
        String url = environment.getProperty("Base_Url") + environment.getProperty("AssignSIM");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(assignRequest, headers);
        headers.setContentType(MediaType.APPLICATION_JSON);
        ResponseEntity<ObjectMiddlewareResponse> response = restTemplate.postForEntity(url, request,ObjectMiddlewareResponse.class );

        if (response.getBody() != null && !response.getBody().getSuccess()) {
            throw new Exception("error during assign sim to the device with serial number of "+ assignRequest.getDeviceSerialNumber());
        }
    }
}
/** ********************* **/
/**    pOwErEd bY eLiE    **/
/** ********************* **/
