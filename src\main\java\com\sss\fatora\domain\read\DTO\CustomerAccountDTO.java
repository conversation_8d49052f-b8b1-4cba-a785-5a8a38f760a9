package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CustomerAccountDTO {
    @JsonProperty(value = "Account_Number")
    String Account_Number;
    @JsonProperty(value = "Account_Type")
    String Account_Type;
    @JsonProperty(value = "Account_Currency")
    String Account_Currency;

    @JsonProperty(value = "Is_ATM_Default")
    String Is_ATM_Default;

    @JsonProperty(value = "Is_POS_Default")
    String Is_POS_Default;
    @JsonProperty(value = "Link_Flag")
    Integer Link_Flag;

}
