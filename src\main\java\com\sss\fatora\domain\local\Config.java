package com.sss.fatora.domain.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.GenericDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "Config")
public class Config extends GenericDomain {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "Name")
    @Nationalized
    private String name;

    @Column(name = "Value")
    @Nationalized
    private String value;

    @Column(name = "Active")
    private Boolean active=true;

    @Column(name = "Description")
    private String description;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}
