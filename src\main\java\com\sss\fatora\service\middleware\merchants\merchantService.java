package com.sss.fatora.service.middleware.merchants;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.dao.local.AcquiringRequestDao;
import com.sss.fatora.domain.local.AcquiringActionRequest;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.middleware.Merchants.MerchantRequest;
import com.sss.fatora.domain.middleware.Merchants.MerchantResponse;
import com.sss.fatora.domain.middleware.Merchants.dto.UpdateMerchantRequest;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.export.service.middleware.ExportMerchantService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.*;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class merchantService {
    private final PrivilegeService privilegeService;
    private final Environment environment;
    private final ConfigService configService;

    private final ObjectMapper objectMapper;

    private final AcquiringRequestDao acquiringRequestDao;

    final RestTemplate restTemplate;
    private final ExportMerchantService exportMerchantService;

    public merchantService(PrivilegeService privilegeService, Environment environment, ConfigService configService,
                           RestTemplate restTemplate, ExportMerchantService exportMerchantService,
                           ObjectMapper objectMapper,AcquiringRequestDao acquiringRequestDao) {
        this.privilegeService = privilegeService;
        this.environment = environment;
        this.configService = configService;
        this.restTemplate = restTemplate;
        this.exportMerchantService = exportMerchantService;
        this.objectMapper = objectMapper;
        this.acquiringRequestDao = acquiringRequestDao;
    }

    public MerchantResponse search(MerchantRequest merchantRequest, Pagination pagination) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            merchantRequest.setBank(bankCode);
        }
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetMerchants");
        List<String> fields = new ArrayList<>();
        Map<String, List<String>> fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.
                SEARCH.getType(), null, fields);
        return setUpMerchantRequest(merchantRequest, pagination, url, null, fieldsMap.get("fields"));
    }

    public MerchantResponse getWithDetails(MerchantRequest merchantRequest) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetMerchants");
        List<String> fields = new ArrayList<>();
        Map<String, List<String>> fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.
                SEARCH_WITH_DETAILS.getType(), null, fields);
        return setUpMerchantRequest(merchantRequest, null, url, null, fieldsMap.get("fields"));
    }

    public MerchantResponse export(MerchantRequest merchantRequest, Pagination pagination, boolean withDetails,
                                   HttpServletResponse response) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            merchantRequest.setBank(bankCode);
        }
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetMerchants");
        List<String> fields = new ArrayList<>();
        MerchantResponse merchantList = new MerchantResponse();
        Map<String, List<String>> fieldsMap = new HashMap<>();
        pagination.setStart(0);
        pagination.setSize(0);
        pagination.setMaxResult(0);
        if (withDetails) {
            fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.
                    EXPORT_WITH_DETAILS.getType(), configService.getMerchantWithDetailsHeaders(), fields);
            merchantList = setUpMerchantRequest(merchantRequest, pagination, url, response, fieldsMap.get("fields"));
        } else {
            fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.
                    EXPORT_WITHOUT_DETAILS.getType(), configService.getMerchantMainHeaders(), fields);
            merchantList = setUpMerchantRequest(merchantRequest, pagination, url, response, fieldsMap.get("fields"));
            fieldsMap.get("fields").remove("feePercentage");
        }
        try {
            exportMerchantService.export(merchantList.getResult().getItems(), response, fieldsMap.get("headers"), fieldsMap.get("fields"));
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private MerchantResponse setUpMerchantRequest(MerchantRequest merchantRequest, Pagination pagination, String url,
                                                  HttpServletResponse response, List<String> fields) {
        HttpHeaders headers = new HttpHeaders();
        if (merchantRequest == null) {
            merchantRequest = new MerchantRequest();
        }
        if (pagination == null) {
            merchantRequest.setMaxResultCount(10);
            merchantRequest.setSkipCount(0);
        } else {
            merchantRequest.setSorting(pagination.getOrderBy());
            merchantRequest.setMaxResultCount(pagination.getSize());
            merchantRequest.setSkipCount(pagination.getStart());
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        merchantRequest.setFields(fields);
        return sendMerchantRequest(merchantRequest, url, headers);
    }

    private MerchantResponse sendMerchantRequest(MerchantRequest merchantRequest, String url, HttpHeaders headers) {
        HttpEntity<Object> request = new HttpEntity<>(merchantRequest, headers);
        ResponseEntity<MerchantResponse> response = restTemplate.postForEntity(url, request, MerchantResponse.class);
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    private Map<String, List<String>> setFieldsByPrivilege(String type, List<String> projectionHeaders, List<String> projection) {
        List<String> privileges = getPrivilegesNamesByUserId();
        if (PaymentMiddlewareRequestType.EXPORT_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            List<String> fields = new ArrayList<>();
            fields.addAll(configService.getMerchantExportDetailColumns());
            projection = fields;
        } else if (PaymentMiddlewareRequestType.EXPORT_WITHOUT_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getMerchantMainExportColumns();
            projection.add("feePercentage");

        } else if (PaymentMiddlewareRequestType.SEARCH_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getMerchantColumnsWithDetails();
        } else if (PaymentMiddlewareRequestType.SEARCH.getType().equalsIgnoreCase(type)) {
            projection = configService.getMerchantMainColumns();
            projection.add("feePercentage");
        }
        return filterProjectionList(projectionHeaders, projection, privileges);
    }

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.MERCHANT_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    private Map<String, List<String>> filterProjectionList(List<String> headers, List<String> projection, List<String> privileges) {
        List<String> projections = new ArrayList<>();
        int index = 0;
        List<String> headersTemp = new ArrayList<>();

        for (String proj : projection) {
            if (proj.equalsIgnoreCase("agentShortDesc") || proj.equalsIgnoreCase("agentNumber"))
                if (privileges.contains("Merchants_" + "branch")) {
                    projections.add(proj);
                    if (headers != null)
                        headersTemp.add(headers.get(index));
                }
            if (privileges.contains("Merchants_" + proj)) {
                projections.add(proj);
                if (headers != null && headers.size() > index)
                    headersTemp.add(headers.get(index));
            }
            index++;
        }
        if (headers != null) {
            headers.clear();
            headers.addAll(headersTemp);
        }
        projection.clear();
        projection.addAll(projections);
        Map<String, List<String>> returnedMap = new HashMap<>();
        returnedMap.put("headers", headers);
        returnedMap.put("fields", projection);
        return returnedMap;
    }

    public void update(UpdateMerchantRequest merchantRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        Map<String, String> oldValue = new HashMap<>();
        Map<String, String> newValue = new HashMap<>();
        AcquiringActionType actionType = merchantRequest.getActionType();


        if (merchantRequest.getActionType().getAction().equals(AcquiringActionType.CHANGE_MERCHANT_EMAIL.getAction())) {
            if (!merchantRequest.getOldEmail().equals(merchantRequest.getNewEmail())) {
                oldValue.put("email", merchantRequest.getOldEmail());
                newValue.put("email", merchantRequest.getNewEmail());
                acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_MERCHANT_EMAIL.getAction());
            }
        } else if (merchantRequest.getActionType().getAction().equals(AcquiringActionType.CHANGE_MERCHANT_MOBILE.getAction())) {
            if (!merchantRequest.getOldMobileNumber().equals(merchantRequest.getNewMobileNumber())) {
                oldValue.put("merchantNumber", merchantRequest.getOldMobileNumber());
                newValue.put("merchantNumber", merchantRequest.getNewMobileNumber());
                acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_MERCHANT_MOBILE.getAction());
            }
        } else if (merchantRequest.getActionType().getAction().equals(AcquiringActionType.CHANGE_MERCHANT_MCC.getAction())) {
            if (!merchantRequest.getOldMcc().equals(merchantRequest.getNewMcc())) {
                oldValue.put("mcc", merchantRequest.getOldMcc());
                newValue.put("mcc", merchantRequest.getNewMcc());
                acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_MERCHANT_MCC.getAction());
            }
        } else if (merchantRequest.getActionType().getAction().equals(AcquiringActionType.CHANGE_MERCHANT_NAME.getAction())) {
            if (!merchantRequest.getOldMerchantName().equals(merchantRequest.getNewMerchantName())) {
                oldValue.put("merchantName", merchantRequest.getOldMerchantName());
                newValue.put("merchantName", merchantRequest.getNewMerchantName());
                acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_MERCHANT_NAME.getAction());
            }
        }
        acquiringRequestEntity.setEntityName(ModuleName.MERCHANT.getName());
        acquiringRequestEntity.setCreationDate(new Date());
        acquiringRequestEntity.setEntityId(merchantRequest.getMerchantNumber());


        try {
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));
        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }
}
