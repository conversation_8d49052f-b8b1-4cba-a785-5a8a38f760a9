package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.converter.Application;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@XmlRootElement(name = "card")
@Setter
@Getter
@NoArgsConstructor
@Table(name = "Application")
@Entity
public class LocalApplication extends LocalDomain {
    public LocalApplication(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String status;
    private String cardNumber;
    private String applicationNumber;
    private String contractNumber;
    private String cardHolderNumber;
    private String customerNumber;
    private String firstName;
    private String lastName;
    private String productNumber;
    private String cardHolderName;
    private String mobile;
    private String branch;
    private String deliveryBranch;
    private String wsdlStatus;
    private String error;
    private String bankCode;
    private Integer numberOfAccounts;
    @JsonFormat(pattern = "yyyy-MM-dd")
    Date archiveDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    Date submitDate;
    @JsonIgnore
    private String application;
    @Transient
    private Application applicationObject;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }

}
