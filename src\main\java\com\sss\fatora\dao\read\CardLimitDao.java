package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.CardLimit;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CardLimitDao extends GenericReadDao<CardLimit,Integer>{

    @Query("SELECT CL from CardLimit CL " +
            "where CL.cardNumber like :cardNumber")
    List<CardLimit> getByCardNumber(@Param("cardNumber") String cardNumber);
}
