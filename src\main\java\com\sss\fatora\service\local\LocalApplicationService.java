package com.sss.fatora.service.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;


import com.sss.fatora.dao.local.LocalApplicationDao;
import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.Application;
import com.sss.fatora.domain.converter.Applications;
import com.sss.fatora.domain.local.*;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.converter.ApplicationService;
import com.sss.fatora.service.converter.ExcelFileService;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.service.read.*;
import com.sss.fatora.utils.constants.*;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.PaginationService;
import org.apache.tomcat.jni.Local;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Transactional("localDBTransactionManager")
public class LocalApplicationService extends GenericService<LocalApplicationDao, LocalApplication, Integer> {
    final PaginationService paginationService;
    final ApplicationService applicationConverterService;
    final AgentsService agentsService;
    final LanguagesService languagesService;
    final CountryCodesService countryCodesService;
    final CurrencyCodesService currencyCodesService;
    final AccountTypeService accountTypeService;
    final ProductsService productsService;
    final CardsBoService cardsBoService;
    final AccountBoService accountBoService;
    final MessageSource messageSource;
    final ExcelFileService excelFileService;

    public LocalApplicationService(PaginationService paginationService, ApplicationService applicationConverterService,
                                   AgentsService agentsService,
                                   LanguagesService languagesService,
                                   CountryCodesService countryCodesService,
                                   CurrencyCodesService currencyCodesService,
                                   AccountTypeService accountTypeService,
                                   ProductsService productsService,
                                   CardsBoService cardsBoService,
                                   AccountBoService accountBoService,
                                   MessageSource messageSource,
                                   ExcelFileService excelFileService) {
        this.paginationService = paginationService;
        this.applicationConverterService = applicationConverterService;
        this.agentsService = agentsService;
        this.languagesService = languagesService;
        this.countryCodesService = countryCodesService;
        this.currencyCodesService = currencyCodesService;
        this.accountTypeService = accountTypeService;
        this.productsService = productsService;
        this.cardsBoService = cardsBoService;
        this.accountBoService = accountBoService;
        this.messageSource = messageSource;
        this.excelFileService = excelFileService;

    }

    public Map<String, String> prepareApplicationValues() {
        Map<String, String> fixedValues = new HashMap<>();


        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //   int int_random = ThreadLocalRandom.current().nextInt();
        Random random = new Random();
        Integer int_random = random.nextInt(*********);
        String applicationNumber = timestamp.getTime() + String.valueOf(int_random);
        fixedValues.put("application_number", applicationNumber);
        fixedValues.put("application_type", "APTPISSA");
        fixedValues.put("application_flow_id", "1001");
        fixedValues.put("application_status", "APST0006");
        fixedValues.put("operator_id", "A0393");
        fixedValues.put("institution_id", "1001");
        fixedValues.put("customer_type", "ENTTPERS");
        fixedValues.put("command", "CMMDCREX");
        fixedValues.put("customer_category", "CCTGORDN");
        fixedValues.put("customer_relation", "RSCBEMPL");
        fixedValues.put("resident", "1");
        fixedValues.put("contract_type", "CNTPBANK");
        fixedValues.put("card_type", "6001");
        fixedValues.put("card_id", "card_1");
        fixedValues.put("contact_type", "CNTTNTFC");
        fixedValues.put("preferred_lang", "LANGENG");
        fixedValues.put("commun_method", "CMNM0001");
        fixedValues.put("secret_question", "SEQUWORD");
        fixedValues.put("secret_answer", "qwerty");
        fixedValues.put("account_link_flag", "1");
        fixedValues.put("address_type", "ADTPHOME");
        fixedValues.put("language", "LANGENG");
        fixedValues.put("id_type", "IDTP0001");
        return fixedValues;
    }

//    public Application prepareApplication(Application application) throws IllegalAccessException {
//
//        Application preparedApplication = applicationConverterService.prepareApplicationObjectFromPanel(application, prepareApplicationValues());
//        return preparedApplication;
//    }

    public Map getFormInfo() {
        Map<String, Object> formInfoMap = new HashMap<>();

        formInfoMap.put("agents", agentsService.getAllAgentsByBankCode());
        formInfoMap.put("languages", languagesService.getAll());
        formInfoMap.put("countryCodes", countryCodesService.getCountryCodes());
        formInfoMap.put("currencyCodes", currencyCodesService.getAll());
        formInfoMap.put("accountType", accountTypeService.getAll());
        formInfoMap.put("products", productsService.getAllProductsByBankCode());
        return formInfoMap;
    }


    public List<LocalApplication> requestCard(List<LocalApplication> applicationList) throws Exception {

        List<LocalApplication> requestApplicationList = new ArrayList<>();
        Boolean accountExist = false;
        Boolean customerExist = false;
        for (LocalApplication application : applicationList) {
            String customerNumber = application.getApplicationObject().getCustomer().getCustomer_number();
            String personNumber = application.getApplicationObject().getCustomer().getPerson().getIdentity_card().getId_series();

            LocalApplication copyApplication = dao.getApplicationById(application.getId());
            application.setBranch(copyApplication.getBranch());
            application.setDeliveryBranch(copyApplication.getDeliveryBranch());
            application.setMobile(copyApplication.getMobile());
            application.setCardHolderName(copyApplication.getCardHolderName());
            application.setLastName(copyApplication.getLastName());
            application.setFirstName(copyApplication.getFirstName());
            application.setBankCode(productsService.getBankCodeByProductNumber(Long.valueOf(copyApplication.getProductNumber())));
            application.setProductNumber(copyApplication.getProductNumber());
            application.setSubmitDate(new Date());
            if (cardsBoService.checkCustomer(customerNumber) != null) {
                //  errorList.add(application.getCustomerNumber() + " Customer is already exist");
                CustomUserDetails.getCurrentInstance().getErrorsList().add(customerNumber + " " + messageSource.getMessage("customer_exist", null, null));
                application.setError(customerNumber + " " + messageSource.getMessage("customer_exist", null, null));
                application.setWsdlStatus(WsdlStatus.FAILED.getStatus());
                customerExist = true;
            }

//            if (cardsBoService.checkPerson(personNumber) != null) {
//                CustomUserDetails.getCurrentInstance().getErrorsList().add(customerNumber + " " + messageSource.getMessage("customer_exist", null, null));
//                application.setError(customerNumber + " " + messageSource.getMessage("customer_exist", null, null));
//                application.setWsdlStatus(WsdlStatus.FAILED.getStatus());
//                customerExist = true;
//            }

            List<Account> accountList = application.getApplicationObject().getCustomer().getContract().getAccount();
            application.setNumberOfAccounts(accountList.size());
            for (Account account : accountList) {
                if (accountBoService.getAccountBoByNumber(account.getAccount_number()) != null) {
                    CustomUserDetails.getCurrentInstance().getErrorsList().add(account.getAccount_number() + " " + messageSource.getMessage("account_exist", null, null));
                    application.setError(application.getError() + " / " + account.getAccount_number() + " " + messageSource.getMessage("account_exist", null, null));
                    application.setWsdlStatus(WsdlStatus.FAILED.getStatus());
                    accountExist = true;
                }
            }
            if (!accountExist && !customerExist) {
                Application preparedApplication = applicationConverterService.prepareApplicationObjectFromPanel(application.getApplicationObject(), prepareApplicationValues());
                if ("14".equals(application.getBankCode())) {
                    List<Account> sgbAccount = getSGBAccounts(preparedApplication.getCustomer().getContract().getAccount());
                    preparedApplication.getCustomer().getContract().setAccount(sgbAccount);
                }

                String body = applicationConverterService.serializeObjectToXML(preparedApplication, "soap");
                SoapResponse response = applicationConverterService.connectToService(body);
                application.setWsdlStatus(WsdlStatus.INPROGRESS.getStatus());

                for (Account account : accountList) {
                    if (account.getAccount_number().length() >= 26) {
                        String newAccountNumber = account.getAccount_number().substring(5);
                        account.setAccount_number(newAccountNumber);
                    }

                }

                application.getApplicationObject().getCustomer().getContract().setAccount(accountList);
                if (response.getBody().getFault() == null) {
                    if (response.getBody().getApplication().getError() == null) {
                        application.setApplicationNumber(response.getBody().getApplication().getApplication_number());
                        application.setCardNumber(response.getBody().getApplication().getCustomer().getContract().getCard().getCard_number());
                        application.setCardHolderNumber(response.getBody().getApplication().getCustomer().getContract().getCard().getCardholder().getCardholder_number());
                        application.setContractNumber(response.getBody().getApplication().getCustomer().getContract().getContract_number());
                        application.setCustomerNumber(response.getBody().getApplication().getCustomer().getCustomer_number());
                        application.setWsdlStatus(WsdlStatus.SUCCESS.getStatus());
                    } else {
                        application.setWsdlStatus(WsdlStatus.FAILED.getStatus());
                        application.setError(application.getError() + " / " + response.getBody().getApplication().getError().getError_desc());
                    }
                } else {
                    application.setWsdlStatus(WsdlStatus.FAILED.getStatus());
                    application.setError(application.getError() + " / " + response.getBody().getFault().getFaultString());
                }
            }
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonApplication = objectMapper.writeValueAsString(application.getApplicationObject());
            application.setApplication(jsonApplication);
            application.setStatus(ApplicationStatus.SUBMIT.getStatus());
            dao.save(application);
            requestApplicationList.add(application);

        }
//        if(CustomUserDetails.getCurrentInstance().getErrorsList().size() > 0)
//            return null;
//        else
        return requestApplicationList;
    }


    public LocalApplication saveDraft(Application application) throws Exception {
        LocalApplication localApplication = new LocalApplication();
        //convert object to json string
        ObjectMapper objectMapper = new ObjectMapper();
        application.getCustomer().getContract().getCard().getCardholder().setCardholder_number(application.getCustomer().getCustomer_number());
        localApplication.setFirstName(application.getCustomer().getPerson().getPerson_name().getFirst_name());
        localApplication.setLastName(application.getCustomer().getPerson().getPerson_name().getSurname());
        localApplication.setStatus(ApplicationStatus.DRAFT.getStatus());
        localApplication.setProductNumber(application.getCustomer().getContract().getProduct_number());
        localApplication.setCardHolderName(application.getCustomer().getContract().getCard().getCardholder().getCardholder_name());
        localApplication.setCardHolderNumber(application.getCustomer().getCustomer_number());
        localApplication.setCustomerNumber(application.getCustomer().getCustomer_number());
        localApplication.setMobile(application.getCustomer().getContract().getCard().getCardholder().getContact().get(0).getContact_data().getCommun_address());
        localApplication.setDeliveryBranch(application.getCustomer().getContract().getCard().getDelivery_agent_number());
        localApplication.setBranch(application.getAgent_number());
        String bankCode = productsService.getBankCodeByProductNumber(Long.valueOf(application.getCustomer().getContract().getProduct_number()));
        localApplication.setBankCode(bankCode);
        //Edit SGB Accounts
        if ("14".equals(bankCode)) {
            List<Account> accounts = application.getCustomer().getContract().getAccount();
            if (checkSgbAccount(accounts)) {
                CustomUserDetails.getCurrentInstance().getErrorsList().add(messageSource.getMessage("sgb_account_longer_than_required", null, null));
                return null;
            }
        }
        String jsonApplication = objectMapper.writeValueAsString(application);
        localApplication.setApplication(jsonApplication);
        return dao.save(localApplication);
    }

    private boolean checkSgbAccount(List<Account> accounts) {
        for (Account account : accounts) {
            if (account.getAccount_number().length() > 21) {
                return true;
            }
        }
        return false;
    }

    public List<LocalApplication> saveDraftFromExcel(TempContentModel tempContentModel) throws Exception {
        Applications excelApplications = excelFileService.convertExcelToDatabase(tempContentModel);
        List<LocalApplication> savedLocalApplications = new ArrayList<LocalApplication>();
        try {


            for (Application application : excelApplications.getApplications()
            ) {
                LocalApplication localApplication = new LocalApplication();
                //convert object to json string
                ObjectMapper objectMapper = new ObjectMapper();
                application.getCustomer().getContract().getCard().getCardholder().setCardholder_number(application.getCustomer().getCustomer_number());
                String jsonApplication = objectMapper.writeValueAsString(application);
                localApplication.setApplication(jsonApplication);
                localApplication.setFirstName(application.getCustomer().getPerson().getPerson_name().getFirst_name());
                localApplication.setLastName(application.getCustomer().getPerson().getPerson_name().getSurname());
                localApplication.setStatus(ApplicationStatus.DRAFT.getStatus());
                localApplication.setProductNumber(application.getCustomer().getContract().getProduct_number());
                localApplication.setCardHolderName(application.getCustomer().getContract().getCard().getCardholder().getCardholder_name());
                localApplication.setCardHolderNumber(application.getCustomer().getCustomer_number());
                localApplication.setCustomerNumber(application.getCustomer().getCustomer_number());
                localApplication.setMobile(application.getCustomer().getContract().getCard().getCardholder().getContact().get(0).getContact_data().getCommun_address());
                localApplication.setDeliveryBranch(application.getCustomer().getContract().getCard().getDelivery_agent_number());
                localApplication.setBranch(application.getAgent_number());
                localApplication.setBankCode(productsService.getBankCodeByProductNumber(Long.valueOf(application.getCustomer().getContract().getProduct_number())));
                dao.save(localApplication);
                savedLocalApplications.add(localApplication);

            }
            return savedLocalApplications;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public LocalApplication editDraft(LocalApplication application) throws Exception {

        try {
            ObjectMapper objectMapper = new ObjectMapper();


            Integer accountNum = application.getApplicationObject().getCustomer().getContract().getAccount().size();
            Application jsonApplication = application.getApplicationObject();
            application.setApplicationObject(jsonApplication);
            application.setCardHolderNumber(jsonApplication.getCustomer().getContract().getCard().getCardholder().getCardholder_number());
            application.setStatus(ApplicationStatus.DRAFT.getStatus());
            application.setNumberOfAccounts(accountNum);

            application.setCardHolderName(jsonApplication.getCustomer().getContract().getCard().getCardholder().getCardholder_name());
            application.setMobile(jsonApplication.getCustomer().getContact().getContact_data().getCommun_address());
            application.setBranch(jsonApplication.getAgent_number());
            application.setDeliveryBranch(jsonApplication.getCustomer().getContract().getCard().getDelivery_agent_number());
            application.setFirstName(jsonApplication.getCustomer().getPerson().getPerson_name().getFirst_name());
            application.setLastName(jsonApplication.getCustomer().getPerson().getPerson_name().getSurname());
            application.setProductNumber(jsonApplication.getCustomer().getContract().getProduct_number());
            String bankCode = productsService.getBankCodeByProductNumber(Long.valueOf(jsonApplication.getCustomer().getContract().getProduct_number()));
            application.setBankCode(bankCode);
            //Edit SGB Accounts
            if ("14".equals(bankCode)) {
                List<Account> accounts = application.getApplicationObject().getCustomer().getContract().getAccount();
                if (checkSgbAccount(accounts)) {
                    CustomUserDetails.getCurrentInstance().getErrorsList().add(messageSource.getMessage("sgb_account_longer_than_required", null, null));
                    return null;
                }
            }
            String stringApplication = objectMapper.writeValueAsString(application.getApplicationObject());
            application.setApplication(stringApplication);
            application.setModifiedDate(new Date());
            dao.save(application);
            return application;
        } catch (Exception e) {
            return null;
        }
    }

    private List<Account> getSGBAccounts(List<Account> accounts) {
        accounts.forEach(account -> {
            StringBuilder sgbAccount = new StringBuilder();
            sgbAccount.append("14");
            for (int i = 0; i < 24 - account.getAccount_number().length(); ++i) {
                sgbAccount.append("0");
            }
            sgbAccount.append(account.getAccount_number());
            account.setAccount_number(sgbAccount.toString());
        });
        return accounts;
    }


    public Page<LocalApplication> getApplicationByStatus(String status, Pagination pagination, Long countPage) throws JsonProcessingException {
        try {
            Page<LocalApplication> applicationListPage = dao.getApplicationByStatus(status, paginationService.getPagination(pagination));
            List<LocalApplication> applicationList = applicationListPage.getContent();
            ObjectMapper objectMapper = new ObjectMapper();
            for (LocalApplication application : applicationList) {
                Application applicationObject = objectMapper.readValue(application.getApplication(), Application.class);
                application.setApplicationObject(applicationObject);
            }
            countPage = new Long(0);
            countPage = applicationListPage.getTotalElements();
            return applicationListPage;
        } catch (Exception e) {
            return null;
        }

    }

    public Page<LocalApplication> getSubmittedApplications(Pagination pagination, Long countPage) throws JsonProcessingException {
        try {
            Page<LocalApplication> applicationListPage = dao.getSubmittedApplications(paginationService.getPagination(pagination));
            List<LocalApplication> applicationList = applicationListPage.getContent();
            //List<Application> applicationList = new ArrayList<>();
            ObjectMapper objectMapper = new ObjectMapper();
            for (LocalApplication application : applicationList) {

                Application applicationObject = objectMapper.readValue(application.getApplication(), Application.class);
                application.setApplicationObject(applicationObject);
            }
            countPage = new Long(0);
            countPage = applicationListPage.getTotalElements();
            return applicationListPage;
        } catch (Exception e) {
            return null;
        }

    }

    public List<LocalApplication> moveToArchive(List<Long> idList) throws Exception {

        List<LocalApplication> archiveApplicationList = dao.getApplicationByIdList(idList);
        for (LocalApplication localApplication : archiveApplicationList) {

            localApplication.setStatus(ApplicationStatus.ARCHIVE.getStatus());
            localApplication.setArchiveDate(new Date());
            dao.save(localApplication);
            // archiveApplicationList.add(application);
        }
        return archiveApplicationList;
    }

    public List<LocalApplication> moveToDraft(List<Long> idList) {

        List<LocalApplication> draftApplicationList = dao.getApplicationByIdList(idList);

        for (LocalApplication application : draftApplicationList) {
            if (application.getWsdlStatus().equals(WsdlStatus.FAILED.getStatus())) {
                application.setStatus(ApplicationStatus.DRAFT.getStatus());
                dao.save(application);
            } else return null;

        }
        return draftApplicationList;
    }

    public Boolean deleteApplications(List<LocalApplication> applicationList) throws Exception {
        try {
            for (LocalApplication localApplication : applicationList) {
                dao.delete(localApplication);

                return true;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return null;
    }

    public Page<LocalApplication> search(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection) throws NoSuchFieldException, IllegalAccessException, IntrospectionException, InvocationTargetException {
        Page<LocalApplication> localApplications;
        String additionalConstraint = "";
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();

        if (localApplication == null)
            localApplication = new LocalApplication();

        if (fromDate == null) {
            fromDate = new Date(1612178075113L).getTime();
        }
        if (toDate == null) {
            toDate = new Date(7258118400L * 1000).getTime();
        }
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");

         /*if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
             List<Privilege> privileges = privilegeService.getPrivilegesById(PrivilegeType.LOG_PRIVILEGE.getType(), applicationUser.getId());
             if (!privileges.stream().anyMatch(privilege -> privilege.getName().equalsIgnoreCase("View All Logs"))) {
                 log.setBank(new Bank(applicationUser.getBank().getId()));
             }

             additionalConstraint = "LocalApplication.creationDate BETWEEN '" + f.format(fromDate) + "' AND '" + f.format(toDate) + "'";
         } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()))
          */
        //put product number in filter to get only user bank's cards
        if (CustomUserDetails.getCurrentInstance().getApplicationUser().getBank() != null) {
            localApplication.setProductNumber(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank().getCode() + "*");
            filterOperator.put("productNumber", Operator.CONTAINS_WITH_STAR);
        }

        additionalConstraint = "LocalApplication.creationDate between '" + f.format(fromDate) + "' AND '" + f.format(toDate) + "'";

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("LocalApplication.creationDate");
            pagination.setOrderType("DESC");
        }

        localApplications = this.dynamicSearch(localApplication
                , pagination
                , additionalConstraint
                , filterOperator
                , null,false);

        return localApplications;

    }

    public Page<LocalApplication> mainSearch(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, JsonProcessingException {
        if (CustomUserDetails.getCurrentInstance().getApplicationUser().getBank() != null)
            localApplication.setBankCode(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank().getCode());
        if ("draft".equals(localApplication.getStatus())) {
            return draftsSearch(localApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("archive".equals(localApplication.getStatus())) {
            return archiveSearch(localApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("submit".equals(localApplication.getStatus())) {
            return submittedSearch(localApplication, fromDate, toDate, pagination, filterOperator);
        }
        return null;
    }


    public Page<LocalApplication> draftsSearch(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, JsonProcessingException {
    /*List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
*/
        List<String> projection;
        projection = Stream.of("id", "status", "cardNumber", "applicationNumber", "contractNumber", "cardHolderNumber", "customerNumber", "firstName", "lastName", "application","numberOfAccounts")
                .collect(Collectors.toList());
        Page<LocalApplication> terminals = search(localApplication, fromDate, toDate, pagination, filterOperator, projection);
        ObjectMapper objectMapper = new ObjectMapper();
        for (LocalApplication application : terminals.getContent()) {

            Application applicationObject = objectMapper.readValue(application.getApplication(), Application.class);
            application.setApplicationObject(applicationObject);
        }
        return terminals;
    }

    public Page<LocalApplication> submittedSearch(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, JsonProcessingException {
    /*List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
*/
        List<String> projection;
        projection = Stream.of("id", "status", "cardNumber", "applicationNumber", "contractNumber", "cardHolderNumber", "customerNumber", "firstName", "lastName", "application","numberOfAccounts")
                .collect(Collectors.toList());
        Page<LocalApplication> terminals = search(localApplication, fromDate, toDate, pagination, filterOperator, projection);
        ObjectMapper objectMapper = new ObjectMapper();
        for (LocalApplication application : terminals.getContent()) {

            Application applicationObject = objectMapper.readValue(application.getApplication(), Application.class);
            application.setApplicationObject(applicationObject);
        }
        return terminals;
    }

    public Page<LocalApplication> archiveSearch(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException, JsonProcessingException {
    /*List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
*/
        List<String> projection;
        projection = Stream.of("id", "status", "cardNumber", "applicationNumber", "contractNumber", "cardHolderNumber", "customerNumber", "firstName", "lastName", "application","numberOfAccounts")
                .collect(Collectors.toList());
        Page<LocalApplication> terminals = search(localApplication, fromDate, toDate, pagination, filterOperator, projection);
        ObjectMapper objectMapper = new ObjectMapper();
        for (LocalApplication application : terminals.getContent()) {

            Application applicationObject = objectMapper.readValue(application.getApplication(), Application.class);
            application.setApplicationObject(applicationObject);
        }
        return terminals;
    }

}
