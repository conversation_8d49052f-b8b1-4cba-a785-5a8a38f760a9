package com.sss.fatora.controller.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.AcquiringRequestDao;
import com.sss.fatora.dao.local.ActionRequestDao;
import com.sss.fatora.domain.local.AcquiringActionRequest;
import com.sss.fatora.domain.local.ActionRequest;
import com.sss.fatora.service.local.AcquiringActionRequestService;
import com.sss.fatora.service.local.ActionRequestService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import com.sss.fatora.utils.model.TempContentModel;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Optional;
import java.util.List;

@RestController
@RequestMapping("/acquiringActionRequests")
public class AcquiringController extends GenericController<AcquiringActionRequestService, AcquiringRequestDao, AcquiringActionRequest, Long> {

    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResponseObject save(@ParameterName("domain") AcquiringActionRequest acquiringActionRequest,
                               @ParameterName(value = "tempContentModel", required = false) TempContentModel tempContentModel) {
        try {
            AcquiringActionRequest request = service.saveRequest(acquiringActionRequest);
            return ResponseObject.ADDED_SUCCESS(request, null);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResponseObject searchAcquiringActionRequest(@ParameterName(value = "filter", required = false) AcquiringActionRequest acquiringActionRequest,
                                              @ParameterName(value = "fromDate", required = false) Long fromDate,
                                              @ParameterName(value = "toDate", required = false) Long toDate,
                                              @ParameterName(value = "fromModifiedDate", required = false) Long fromModifiedDate,
                                              @ParameterName(value = "toModifiedDate", required = false) Long toModifiedDate,
                                              @ParameterName(value = "fromApprovalDate", required = false) Long fromApprovalDate,
                                              @ParameterName(value = "toApprovalDate", required = false) Long toApprovalDate,
                                              @ParameterName(value = "pagination", required = false) Pagination pagination,
                                              @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                              @RequestParam(value = "userId", required = false) Integer userId) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException, JsonProcessingException {
        if (acquiringActionRequest == null)
            acquiringActionRequest = new AcquiringActionRequest();
        Page<AcquiringActionRequest> actionRequests = service.search(acquiringActionRequest, fromDate, toDate,fromModifiedDate,toModifiedDate,
                fromApprovalDate,toApprovalDate,pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(), userId);
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", actionRequests != null ? actionRequests.getTotalElements() : 0);
        return actionRequests != null ?
                ResponseObject.FETCHED_SUCCESS(actionRequests.getContent(), extra) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


    @RequestMapping(value = "/approve", method = RequestMethod.POST)
    public ResponseObject approveAcquiringActionRequest(@ParameterName(value = "requestIds") List<Long> requestIds) {
        Object request = service.approveRequest(requestIds);
        return  ResponseObject.UPDATED_SUCCESS(request, null) ;
    }

    // @RequestMapping(value = "/reject", method = RequestMethod.POST)
    // public ResponseObject rejectActionRequest(@ParameterName(value = "requestIds") List<Long> requestIds) {
    //     Boolean done = service.rejectRequest(requestIds);
    //     return done != null ?
    //             ResponseObject.FETCHED_SUCCESS(done, null) :
    //             ResponseObject.FETCHING_FAILED(null, null);
    // }

    // @RequestMapping(value = "/archive", method = RequestMethod.POST)
    // public ResponseObject archive(@ParameterName("requestIds") List<Long> requestIds, @RequestParam("status") Boolean status) {

    //     Boolean done = service.archive(requestIds, status);
    //     return done != null ?
    //             ResponseObject.UPDATED_SUCCESS(done, null) :
    //             ResponseObject.UPDATING_FAILED(null, null);
    // }

    // @RequestMapping(value = "/reprocess", method = RequestMethod.POST)
    // public ResponseObject reprocessActionRequest(@ParameterName(value = "actions") List<ActionRequest> actionRequest) {
    //     List<ActionRequest> requests = service.reprocessActionRequest(actionRequest);
    //     return requests != null ?
    //             ResponseObject.UPDATED_SUCCESS(requests, null) :
    //             ResponseObject.UPDATING_FAILED(null, null);
    // }
}
