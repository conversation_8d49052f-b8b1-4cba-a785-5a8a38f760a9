package com.sss.fatora.domain.read.DTO.Payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class PaymentRequest {
    @JsonProperty(value = "cardNumber")
    String cardNumber;
    @JsonProperty(value = "date")
    String date;
    @JsonProperty(value = "terminalId")
    String terminalId;
    @JsonProperty(value = "transactionId")
    String transactionId;
    //IN PROD
    @JsonProperty(value = "transactionType")
//    @JsonProperty(value = "transaction_type")
    Integer transactionType;
    @JsonProperty(value = "cardStatus")
    Integer cardStatus;

}
