package com.sss.fatora.service.write;

import com.sss.fatora.dao.write.CrefTabDao;
import com.sss.fatora.domain.write.CrefTab;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.log.LogCardNumber;
import com.sss.fatora.utils.log.LogOldValue;
import com.sss.fatora.utils.log.LogStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional("writingDBTransactionManager")
public class CrefTabService {

    CrefTabDao crefTabDao;

    public CrefTabService(CrefTabDao crefTabDao) {
        this.crefTabDao = crefTabDao;
    }


    /**
     * @param cardNo This Parameter Represent The Number Of The Card
     * @param expDate This Parameter Represent The Expiration Date Of This Card
     *
     * This Function Reset The PIN Number Of A Specific Card
     * */
    @Log(actionType = ActionType.CARD_RESET)
    public Boolean resetPin(@LogCardNumber String cardNo, Long expDate) {
        try {
            if (crefTabDao.resetPin(cardNo, expDate) ==1 ) {
                return true;
            }
            else return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * @param cardNo This Parameter Represent The Number Of The Card
     * @param expDate This Parameter Represent The Expiration Date Of This Card
     * @param status This Parameter Represent The New Status Of The Card As An Integer ( Used For The Update )
     * @param oldStatus This Parameter Represent The Old Status Of The Card As An String ( Used For The Log )
     * @param newStatus This Parameter Represent The New Status Of The Card As An String ( Used For The Log )
     *
     *  This Function Change The Status Of The Card
     * */
    @Log(actionType = ActionType.CHANGE_STATUS)
    public Boolean changeStatus(@LogCardNumber String cardNo, Long expDate, Integer status, @LogOldValue String oldStatus, @LogStatus String newStatus) {
        try {

            if (crefTabDao.changeStatus(status, cardNo, expDate)==1) {
                return true;
            }
            else return null;
        } catch (Exception e) {
            return null;
        }
    }



}
