package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.TransactionDataVW;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface TransactionDao extends GenericReadDao<TransactionDataVW, Long> {

    @Query("SELECT DISTINCT T FROM TransactionDataVW T " +
            "WHERE " +
            "( T.addressName LIKE %:#{#transaction.addressName}% ) " +
            "AND ( T.atmId LIKE %:#{#transaction.atmId}% ) " +
            "AND ( T.hpan LIKE %:#{#transaction.hpan}% ) AND (substring(T.hpan,0,6) IN :bins) " +
            "AND ( T.addressCity LIKE %:#{#transaction.addressCity}% ) " +
            "AND ( T.addressStreet LIKE %:#{#transaction.addressStreet}% ) " +
            /*"AND ( T.addressState LIKE %:#{#transaction.addressState}% ) " +*/
            "AND ( T.addressCountry LIKE %:#{#transaction.addressCountry}% ) " +
            "AND ( T.addressPostalCode LIKE %:#{#transaction.addressPostalCode}%) "
    )
    Page<TransactionDataVW> searchByExternalUser(TransactionDataVW transaction,
                                              List<String> bins,
                                              Pageable pagination);



    @Query("SELECT DISTINCT T FROM TransactionDataVW T " +
            "WHERE " +
            "( T.addressName LIKE %:#{#transaction.addressName}% ) " +
            "AND ( T.atmId LIKE %:#{#transaction.atmId}% ) " +
            "AND ( T.hpan LIKE %:#{#transaction.hpan}% ) " +
            "AND ( T.addressCity LIKE %:#{#transaction.addressCity}% ) " +
            "AND ( T.addressStreet LIKE %:#{#transaction.addressStreet}% ) " +
            /*"AND ( T.addressState LIKE %:#{#transaction.addressState}% ) " +*/
            "AND ( T.addressCountry LIKE %:#{#transaction.addressCountry}% ) " +
            "AND ( T.addressPostalCode LIKE %:#{#transaction.addressPostalCode}%) "
    )
    Page<TransactionDataVW> searchByInternalUser(TransactionDataVW transaction,
                                                 Pageable pagination);
}
