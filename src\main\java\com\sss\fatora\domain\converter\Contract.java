package com.sss.fatora.domain.converter;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;
import java.util.List;

@Setter
@Getter
@XmlType(propOrder = {"command", "contract_type","product_id", "product_number","contract_number", "card", "service", "account"})
public class Contract extends GenericConverter {
    @ExcelProperty(name = "ns1:command2")
    private String command;
    @ExcelProperty(name = "ns1:contract_type")
    private String contract_type;
    @ExcelProperty(name = "ns1:product_number")
    private String product_number;
    private String contract_number;
   // @ExcelProperty(name = "ns1:start_date")
    // private String start_date;

    private String id;

    @XmlAttribute(name = "id")
    public void setId(String id) {
        this.id = id;
    }

    private String product_id;

    private Card card;

    @JacksonXmlProperty(localName = "service")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Service> service;

    @JacksonXmlProperty(localName = "account")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Account> account;
}
