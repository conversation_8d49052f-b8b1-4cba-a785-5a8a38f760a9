package com.sss.fatora.domain.middleware.Merchants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class MerchantRequest {
    private String bank;
    private String merchantName;
    private String merchantNumber;
    private String mcc;
    private String customerNumber;
    private String mccDesc;
    private String agentNumber;
    private String agentShortDesc;
    private String accountNumber;
    private String mobileNumber;
    private String emailAddress;
    private String country;
    private String region;
    private String street;
    private String house;
    private String feePercentage;
    private List<String> fields;

    private String sorting;
    private Integer maxResultCount;
    private Integer skipCount;
}
