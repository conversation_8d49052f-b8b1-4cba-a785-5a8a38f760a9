package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.AccountVW;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AccountVWDao extends GenericReadDao<AccountVW, Long> {

    @Query("SELECT A FROM AccountVW A " +
            "WHERE A.cardNo=:cardNo ")
    List<AccountVW> getAccountsByCardNumber(@Param("cardNo") String cardNo);

    @Query("SELECT A FROM AccountVW A " +
            "WHERE A.cardNo=:cardNumber " +
            "AND A.posPrimary=true")
    AccountVW getPosAccountByNumber(@Param("cardNumber") String cardNumber);

    @Query("SELECT A FROM AccountVW A " +
            "WHERE A.cardNo=:cardNumber " +
            "AND A.atmPrimary=true")
    AccountVW getAtmAccountByNumber(@Param("cardNumber") String cardNumber);

    @Query("SELECT A FROM AccountVW A " +
            "WHERE A.acctNo=:accountNumber ")
    AccountVW getAccountByNumber(@Param("accountNumber") String accountNumber);
}
