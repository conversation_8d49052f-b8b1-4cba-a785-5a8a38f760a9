package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.AcquiringActionRequest;
import com.sss.fatora.domain.local.AcquiringRequestsLog;
import com.sss.fatora.domain.local.RequestsLog;
import com.sss.fatora.domain.middleware.acquiring.dto.GetAcquiringRequestFilterDto;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

//@Transactional("localDBTransactionManager")
public interface AcquiringRequestDao extends GenericDao<AcquiringActionRequest, Long> {// JpaRepository<AcquiringRequestEntity,
                                                                                       // Long>{

        @Query("SELECT AR FROM AcquiringActionRequest AR WHERE " +
                        "(:#{#requestDto.requestStatus} is null or AR.requestStatus = :#{#requestDto.requestStatus} ) "
                        +
                        "and (:#{#requestDto.actionType} is null or AR.actionType = :#{#requestDto.actionType} )" +
                        "and (:#{#requestDto.entityName} is null or AR.entityName like %:#{#requestDto.entityName}% )" +
                        "and (:#{#requestDto.entityId} is null or AR.entityId = :#{#requestDto.entityId})" +
                        // "and (:#{#requestDto.archived} is null or AR.archived =
                        // :#{#requestDto.archived})" +
                        " order by AR.creationDate DESC ")
        List<AcquiringActionRequest> search(@Param("requestDto") GetAcquiringRequestFilterDto requestDto);

        List<AcquiringRequestsLog> getAcquiringLogsByRequestId(@Param("id") Long id);

        AcquiringActionRequest getRequestById(Long Id);

        // void rejectRequest(List<Long> requestIds, String userRequestStatus, String
        // actionStatus, Integer id, Date date);

}
// package com.sss.fatora.dao.local;

// import com.sss.fatora.dao.generic.GenericDao;
// import com.sss.fatora.domain.local.AcquiringActionRequest;
// import com.sss.fatora.domain.local.AcquiringRequestsLog;
// import com.sss.fatora.domain.local.ActionRequest;
// import com.sss.fatora.domain.local.RequestsLog;
// import
// com.sss.fatora.domain.middleware.acquiring.dto.GetAcquiringRequestFilterDto;

// import org.springframework.data.jpa.repository.Modifying;
// import org.springframework.data.jpa.repository.Query;
// import org.springframework.data.repository.query.Param;

// import java.util.Date;
// import java.util.List;

// public interface AcquiringRequestDao extends
// GenericDao<AcquiringActionRequest, Long> {

// // @Modifying
// // @Query("UPDATE AcquiringActionRequest AR SET AR.archived =:archive
// // ,AR.modifiedDate =:archivedDate ,AR.applicationUser.id=:userId "
// // +
// // " WHERE AR.Id IN :requestIds ")
// // void archive(@Param("archive") Boolean archive,
// // @Param("requestIds") List<Long> requestIds,
// // @Param("archivedDate") Date archivedDate,
// // @Param("userId") Integer userId);

// // @Modifying
// // @Query("UPDATE AcquiringActionRequest AR SET AR.userRequestStatus
// // =:userRequestStatus,AR.actionStatus=:actionStatus
// // ,AR.applicationUser.id=:userId ,"
// // +
// // "AR.modifiedDate =:date " +
// // " WHERE AR.Id IN :requestIds ")
// // void rejectRequest(@Param("requestIds") List<Long> actionIds,
// // @Param("userRequestStatus") String userRequestStatus,
// // @Param("actionStatus") String actionStatus,
// // @Param("userId") Integer userId,
// // @Param("date") Date date);

// @Query("SELECT AR FROM AcquiringActionRequest AR " +
// "WHERE AR.id = :requestId ")
// AcquiringActionRequest getRequestById(@Param("requestId") Long requestId);

// List<AcquiringActionRequest> getAcquiringRequestsLogs(@Param("id") Long id);

// // @Query("SELECT AR FROM AcquiringActionRequest AR " +
// // "WHERE AR.Id = :Id ")
// // List<AcquiringActionRequest> getAcquiringLogsByRequestId(@Param("Id") Long
// // id);

// }
