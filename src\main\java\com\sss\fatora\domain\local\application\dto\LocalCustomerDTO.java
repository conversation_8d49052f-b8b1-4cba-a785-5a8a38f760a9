package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalCustomerDTO {

    @JsonProperty(value = "Customer_Number")
    String customerNumber;
    @JsonProperty(value = "Nationality")
    String nationality;
    @JsonProperty(value = "Branch")
    String branch;
    @JsonProperty(value = "Mobile_Number")
    String mobile;
    @JsonProperty(value = "Email_Address")
    String email;
    @JsonProperty(value = "Country")
    String country;
    @JsonProperty(value = "Region")
    String region;
    @JsonProperty(value = "City")
    String city;
    @JsonProperty(value = "Street")
    String street;
    @JsonProperty(value = "House")
    String house;
    @JsonProperty(value = "Apartment")
    String apartment;
    @JsonProperty(value = "Person")
    LocalPersonDTO person;

}
