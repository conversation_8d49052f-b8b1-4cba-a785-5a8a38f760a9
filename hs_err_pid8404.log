#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000000006d94a6b3, pid=8404, tid=3812
#
# JRE version: Java(TM) SE Runtime Environment (8.0_31-b13) (build 1.8.0_31-b13)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.31-b07 mixed mode windows-amd64 compressed oops)
# Problematic frame:
# V  [jvm.dll+0x11a6b3]
#
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   http://bugreport.java.com/bugreport/crash.jsp
#

---------------  T H R E A D  ---------------

Current thread (0x000000001b0c7000):  JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_in_vm, id=3812, stack(0x000000001b4c0000,0x000000001b5c0000)]

siginfo: ExceptionCode=0xc0000005, reading address 0x0000000700000033

Registers:
RAX=0x000000001b5bf501, RBX=0x0000000700000033, RCX=0x00000000203a1b20, RDX=0x0000000000000000
RSP=0x000000001b5bf570, RBP=0x00000000203a1b20, RSI=0x000000001b5bf688, RDI=0x0000000002863180
R8 =0x000000001b0c7000, R9 =0x0000000000000001, R10=0x0000000000008000, R11=0x000000001b5bf4c0
R12=0x0000000000000000, R13=0x0000000019bba328, R14=0x0000000000000000, R15=0x0000000000000000
RIP=0x000000006d94a6b3, EFLAGS=0x0000000000010216

Top of Stack: (sp=0x000000001b5bf570)
0x000000001b5bf570:   000000001b0c7000 000000001b0c7000
0x000000001b5bf580:   000000001b5bf638 000000006db47c1b
0x000000001b5bf590:   000000001b0c7000 000000006db6447d
0x000000001b5bf5a0:   000000000000013d 000000006ede9149
0x000000001b5bf5b0:   000000001fbd77d8 000000006edf2912
0x000000001b5bf5c0:   000000001b0c7000 0000000000000000
0x000000001b5bf5d0:   0000000000000000 0000000000000000
0x000000001b5bf5e0:   00000000203a1b20 000000006edf1a09
0x000000001b5bf5f0:   000000001b5bf688 000000001b5bf660
0x000000001b5bf600:   0000000000000001 000000001fbd77d8
0x000000001b5bf610:   00000000203a1b20 000000006edd53b3
0x000000001b5bf620:   000000001b5bf750 0000000000000001
0x000000001b5bf630:   0000000000000001 000000001fbd77d8
0x000000001b5bf640:   0000000000000001 0000000000000000
0x000000001b5bf650:   0000000000000000 0000000000000000
0x000000001b5bf660:   0000000000000001 000000006edd5571 

Instructions: (pc=0x000000006d94a6b3)
0x000000006d94a693:   28 48 85 c9 75 07 33 c0 48 83 c4 28 c3 48 89 5c
0x000000006d94a6a3:   24 20 48 8b 19 48 85 db 74 20 48 83 fb 37 74 1a
0x000000006d94a6b3:   48 8b 13 48 8b cb ff 52 10 84 c0 74 0d 48 8b c3
0x000000006d94a6c3:   48 8b 5c 24 20 48 83 c4 28 c3 33 c0 48 8b 5c 24 


Register to memory mapping:

RAX=0x000000001b5bf501 is pointing into the stack for thread: 0x000000001b0c7000
RBX=0x0000000700000033 is an unknown value
RCX=0x00000000203a1b20 is an unknown value
RDX=0x0000000000000000 is an unknown value
RSP=0x000000001b5bf570 is pointing into the stack for thread: 0x000000001b0c7000
RBP=0x00000000203a1b20 is an unknown value
RSI=0x000000001b5bf688 is pointing into the stack for thread: 0x000000001b0c7000
RDI=0x0000000002863180 is an unknown value
R8 =0x000000001b0c7000 is a thread
R9 =0x0000000000000001 is an unknown value
R10=0x0000000000008000 is an unknown value
R11=0x000000001b5bf4c0 is pointing into the stack for thread: 0x000000001b0c7000
R12=0x0000000000000000 is an unknown value
R13=0x0000000019bba328 is an unknown value
R14=0x0000000000000000 is an unknown value
R15=0x0000000000000000 is an unknown value


Stack: [0x000000001b4c0000,0x000000001b5c0000],  sp=0x000000001b5bf570,  free space=1021k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x11a6b3]
V  [jvm.dll+0x33447d]
C  [jdwp.dll+0x21a09]
C  [jdwp.dll+0x53b3]
C  [jdwp.dll+0x5571]
C  [jdwp.dll+0xf09c]
C  [jdwp.dll+0x1f2c9]
C  [jdwp.dll+0x1f49e]
V  [jvm.dll+0x1ac128]
V  [jvm.dll+0x22c194]
V  [jvm.dll+0x28576a]
C  [msvcr100.dll+0x21d9f]
C  [msvcr100.dll+0x21e3b]
C  [KERNEL32.DLL+0x17c24]
C  [ntdll.dll+0x6d721]


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x0000000020569800 JavaThread "DestroyJavaVM" [_thread_blocked, id=11148, stack(0x0000000002700000,0x0000000002800000)]
  0x0000000020570800 JavaThread "http-nio-8085-Acceptor" daemon [_thread_in_native, id=16832, stack(0x0000000023ce0000,0x0000000023de0000)]
  0x0000000020569000 JavaThread "http-nio-8085-ClientPoller" daemon [_thread_in_native, id=15820, stack(0x0000000023be0000,0x0000000023ce0000)]
  0x000000002056c800 JavaThread "http-nio-8085-exec-10" daemon [_thread_blocked, id=16944, stack(0x0000000023ae0000,0x0000000023be0000)]
  0x000000002056f800 JavaThread "http-nio-8085-exec-9" daemon [_thread_blocked, id=14200, stack(0x00000000239e0000,0x0000000023ae0000)]
  0x0000000020568000 JavaThread "http-nio-8085-exec-8" daemon [_thread_blocked, id=10504, stack(0x00000000238e0000,0x00000000239e0000)]
  0x000000002056d800 JavaThread "http-nio-8085-exec-7" daemon [_thread_blocked, id=19064, stack(0x00000000237e0000,0x00000000238e0000)]
  0x000000002056a800 JavaThread "http-nio-8085-exec-6" daemon [_thread_blocked, id=8776, stack(0x00000000236e0000,0x00000000237e0000)]
  0x000000002056e000 JavaThread "http-nio-8085-exec-5" daemon [_thread_blocked, id=6296, stack(0x00000000235e0000,0x00000000236e0000)]
  0x000000002056c000 JavaThread "http-nio-8085-exec-4" daemon [_thread_blocked, id=14788, stack(0x00000000234e0000,0x00000000235e0000)]
  0x000000002056f000 JavaThread "http-nio-8085-exec-3" daemon [_thread_blocked, id=14080, stack(0x00000000233e0000,0x00000000234e0000)]
  0x0000000020567800 JavaThread "http-nio-8085-exec-2" daemon [_thread_blocked, id=5236, stack(0x00000000232e0000,0x00000000233e0000)]
  0x000000002056b000 JavaThread "http-nio-8085-exec-1" daemon [_thread_blocked, id=12072, stack(0x00000000231e0000,0x00000000232e0000)]
  0x0000000020123000 JavaThread "http-nio-8085-BlockPoller" daemon [_thread_in_native, id=13008, stack(0x00000000230e0000,0x00000000231e0000)]
  0x0000000020124800 JavaThread "scheduling-1" [_thread_blocked, id=14392, stack(0x0000000022fe0000,0x00000000230e0000)]
  0x0000000020120000 JavaThread "container-0" [_thread_blocked, id=15408, stack(0x0000000022ae0000,0x0000000022be0000)]
  0x000000002011d800 JavaThread "Catalina-utility-2" [_thread_blocked, id=15852, stack(0x00000000229e0000,0x0000000022ae0000)]
  0x000000002011f000 JavaThread "Catalina-utility-1" [_thread_blocked, id=10884, stack(0x00000000228e0000,0x00000000229e0000)]
  0x000000002011e800 JavaThread "HikariPool-4 housekeeper" daemon [_thread_blocked, id=16884, stack(0x00000000221e0000,0x00000000222e0000)]
  0x0000000020123800 JavaThread "HikariPool-3 housekeeper" daemon [_thread_blocked, id=1696, stack(0x0000000021fe0000,0x00000000220e0000)]
  0x000000001ff88800 JavaThread "HikariPool-2 housekeeper" daemon [_thread_blocked, id=14316, stack(0x00000000219e0000,0x0000000021ae0000)]
  0x000000001ff4a800 JavaThread "InterruptTimer" daemon [_thread_blocked, id=18060, stack(0x00000000218e0000,0x00000000219e0000)]
  0x000000001ff47800 JavaThread "oracle.jdbc.driver.BlockSource.ThreadedCachingBlockSource.BlockReleaser" daemon [_thread_blocked, id=8580, stack(0x00000000217e0000,0x00000000218e0000)]
  0x000000001ff46000 JavaThread "Timer-0" daemon [_thread_blocked, id=16764, stack(0x00000000216e0000,0x00000000217e0000)]
  0x000000001d434800 JavaThread "HikariPool-1 housekeeper" daemon [_thread_blocked, id=12540, stack(0x0000000020ce0000,0x0000000020de0000)]
  0x000000001d1c3000 JavaThread "mssql-jdbc-shared-timer-core-0" daemon [_thread_blocked, id=17548, stack(0x00000000209e0000,0x0000000020ae0000)]
  0x000000001d0d0800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=17592, stack(0x000000001e210000,0x000000001e310000)]
  0x000000001bb38800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=12944, stack(0x000000001c700000,0x000000001c800000)]
  0x000000001b9c7800 JavaThread "Service Thread" daemon [_thread_blocked, id=13272, stack(0x000000001c2c0000,0x000000001c3c0000)]
  0x000000001b949800 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=5648, stack(0x000000001c1c0000,0x000000001c2c0000)]
  0x000000001b948800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=18576, stack(0x000000001c0c0000,0x000000001c1c0000)]
  0x000000001b947800 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=15880, stack(0x000000001bfc0000,0x000000001c0c0000)]
  0x000000001b0ce000 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=8888, stack(0x000000001b6c0000,0x000000001b7c0000)]
  0x000000001b0cb000 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=15836, stack(0x000000001b5c0000,0x000000001b6c0000)]
=>0x000000001b0c7000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_in_vm, id=3812, stack(0x000000001b4c0000,0x000000001b5c0000)]
  0x0000000019bb3000 JavaThread "Attach Listener" daemon [_thread_blocked, id=17160, stack(0x000000001afc0000,0x000000001b0c0000)]
  0x0000000019bb2800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=15240, stack(0x000000001aec0000,0x000000001afc0000)]
  0x0000000002958800 JavaThread "Finalizer" daemon [_thread_blocked, id=13384, stack(0x000000001adc0000,0x000000001aec0000)]
  0x0000000002955800 JavaThread "Reference Handler" daemon [_thread_blocked, id=3824, stack(0x000000001acc0000,0x000000001adc0000)]

Other Threads:
  0x0000000019b24800 VMThread [stack: 0x000000001abc0000,0x000000001acc0000] [id=5912]
  0x000000001bb41000 WatcherThread [stack: 0x000000001c800000,0x000000001c900000] [id=13120]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap:
 PSYoungGen      total 395776K, used 346680K [0x0000000780600000, 0x000000079ea80000, 0x00000007c0000000)
  eden space 377856K, 88% used [0x0000000780600000,0x0000000794bedde0,0x0000000797700000)
  from space 17920K, 72% used [0x0000000797700000,0x00000007983a0338,0x0000000798880000)
  to   space 17920K, 0% used [0x000000079d900000,0x000000079d900000,0x000000079ea80000)
 ParOldGen       total 144384K, used 26544K [0x0000000701200000, 0x0000000709f00000, 0x0000000780600000)
  object space 144384K, 18% used [0x0000000701200000,0x0000000702bec2d0,0x0000000709f00000)
 Metaspace       used 73165K, capacity 76887K, committed 77056K, reserved 1116160K
  class space    used 9796K, capacity 10424K, committed 10496K, reserved 1048576K

Card table byte_map: [0x0000000011e50000,0x0000000012450000] byte_map_base: 0x000000000e647000

Marking Bits: (ParMarkBitMap*) 0x000000006e00d4f0
 Begin Bits: [0x0000000012c50000, 0x0000000015c08000)
 End Bits:   [0x0000000015c08000, 0x0000000018bc0000)

Polling page: 0x0000000000960000

CodeCache: size=245760Kb used=13591Kb max_used=13591Kb free=232168Kb
 bounds [0x0000000002a90000, 0x00000000037e0000, 0x0000000011a90000]
 total_blobs=7790 nmethods=7124 adapters=585
 compilation: enabled

Compilation events (10 events):
Event: 75.764 Thread 0x000000001b949800 7216       1       org.aspectj.weaver.patterns.ExactAnnotationTypePattern::matches (7 bytes)
Event: 75.765 Thread 0x000000001b949800 nmethod 7216 0x0000000003174f50 code [0x00000000031750c0, 0x0000000003175208]
Event: 75.766 Thread 0x000000001b949800 7212       1       sun.rmi.transport.Transport$$Lambda$108/675873163::run (8 bytes)
Event: 75.766 Thread 0x000000001b949800 nmethod 7212 0x00000000033a7710 code [0x00000000033a7880, 0x00000000033a7af8]
Event: 75.768 Thread 0x000000001b949800 7213       1       sun.rmi.transport.Transport::lambda$setContextClassLoader$72 (9 bytes)
Event: 75.768 Thread 0x000000001b949800 nmethod 7213 0x00000000031749d0 code [0x0000000003174b40, 0x0000000003174d98]
Event: 75.771 Thread 0x000000001b949800 7210       1       sun.rmi.transport.Transport$$Lambda$108/675873163::get$Lambda (9 bytes)
Event: 75.771 Thread 0x000000001b949800 nmethod 7210 0x0000000003174610 code [0x0000000003174760, 0x00000000031748b0]
Event: 75.772 Thread 0x000000001b949800 7211       1       sun.rmi.transport.Transport$$Lambda$108/675873163::<init> (10 bytes)
Event: 75.773 Thread 0x000000001b949800 nmethod 7211 0x00000000032c52d0 code [0x00000000032c5420, 0x00000000032c5550]

GC Heap History (10 events):
Event: 11.478 GC heap before
{Heap before GC invocations=10 (full 2):
 PSYoungGen      total 291840K, used 10236K [0x0000000780600000, 0x0000000795f00000, 0x00000007c0000000)
  eden space 280576K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000791800000)
  from space 11264K, 90% used [0x0000000792380000,0x0000000792d7f2d0,0x0000000792e80000)
  to   space 11776K, 0% used [0x0000000791800000,0x0000000791800000,0x0000000792380000)
 ParOldGen       total 68608K, used 12331K [0x0000000701200000, 0x0000000705500000, 0x0000000780600000)
  object space 68608K, 17% used [0x0000000701200000,0x0000000701e0af78,0x0000000705500000)
 Metaspace       used 33630K, capacity 35479K, committed 35496K, reserved 1079296K
  class space    used 4528K, capacity 4849K, committed 4864K, reserved 1048576K
Event: 11.573 GC heap after
Heap after GC invocations=10 (full 2):
 PSYoungGen      total 291840K, used 0K [0x0000000780600000, 0x0000000795f00000, 0x00000007c0000000)
  eden space 280576K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000791800000)
  from space 11264K, 0% used [0x0000000792380000,0x0000000792380000,0x0000000792e80000)
  to   space 11776K, 0% used [0x0000000791800000,0x0000000791800000,0x0000000792380000)
 ParOldGen       total 101888K, used 16069K [0x0000000701200000, 0x0000000707580000, 0x0000000780600000)
  object space 101888K, 15% used [0x0000000701200000,0x00000007021b14c0,0x0000000707580000)
 Metaspace       used 33630K, capacity 35479K, committed 35496K, reserved 1079296K
  class space    used 4528K, capacity 4849K, committed 4864K, reserved 1048576K
}
Event: 16.127 GC heap before
{Heap before GC invocations=11 (full 2):
 PSYoungGen      total 291840K, used 280576K [0x0000000780600000, 0x0000000795f00000, 0x00000007c0000000)
  eden space 280576K, 100% used [0x0000000780600000,0x0000000791800000,0x0000000791800000)
  from space 11264K, 0% used [0x0000000792380000,0x0000000792380000,0x0000000792e80000)
  to   space 11776K, 0% used [0x0000000791800000,0x0000000791800000,0x0000000792380000)
 ParOldGen       total 101888K, used 16069K [0x0000000701200000, 0x0000000707580000, 0x0000000780600000)
  object space 101888K, 15% used [0x0000000701200000,0x00000007021b14c0,0x0000000707580000)
 Metaspace       used 53242K, capacity 55381K, committed 55808K, reserved 1097728K
  class space    used 7173K, capacity 7566K, committed 7680K, reserved 1048576K
Event: 16.160 GC heap after
Heap after GC invocations=11 (full 2):
 PSYoungGen      total 292352K, used 11766K [0x0000000780600000, 0x0000000799e00000, 0x00000007c0000000)
  eden space 280576K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000791800000)
  from space 11776K, 99% used [0x0000000791800000,0x000000079237db70,0x0000000792380000)
  to   space 17408K, 0% used [0x0000000798d00000,0x0000000798d00000,0x0000000799e00000)
 ParOldGen       total 101888K, used 26667K [0x0000000701200000, 0x0000000707580000, 0x0000000780600000)
  object space 101888K, 26% used [0x0000000701200000,0x0000000702c0afe0,0x0000000707580000)
 Metaspace       used 53242K, capacity 55381K, committed 55808K, reserved 1097728K
  class space    used 7173K, capacity 7566K, committed 7680K, reserved 1048576K
}
Event: 17.974 GC heap before
{Heap before GC invocations=12 (full 2):
 PSYoungGen      total 292352K, used 147543K [0x0000000780600000, 0x0000000799e00000, 0x00000007c0000000)
  eden space 280576K, 48% used [0x0000000780600000,0x0000000788a98110,0x0000000791800000)
  from space 11776K, 99% used [0x0000000791800000,0x000000079237db70,0x0000000792380000)
  to   space 17408K, 0% used [0x0000000798d00000,0x0000000798d00000,0x0000000799e00000)
 ParOldGen       total 101888K, used 26667K [0x0000000701200000, 0x0000000707580000, 0x0000000780600000)
  object space 101888K, 26% used [0x0000000701200000,0x0000000702c0afe0,0x0000000707580000)
 Metaspace       used 56836K, capacity 58967K, committed 59160K, reserved 1099776K
  class space    used 7580K, capacity 7973K, committed 8064K, reserved 1048576K
Event: 17.994 GC heap after
Heap after GC invocations=12 (full 2):
 PSYoungGen      total 389632K, used 12859K [0x0000000780600000, 0x0000000799a00000, 0x00000007c0000000)
  eden space 376320K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000797580000)
  from space 13312K, 96% used [0x0000000798d00000,0x000000079998ec28,0x0000000799a00000)
  to   space 17920K, 0% used [0x0000000797700000,0x0000000797700000,0x0000000798880000)
 ParOldGen       total 101888K, used 26675K [0x0000000701200000, 0x0000000707580000, 0x0000000780600000)
  object space 101888K, 26% used [0x0000000701200000,0x0000000702c0cfe0,0x0000000707580000)
 Metaspace       used 56836K, capacity 58967K, committed 59160K, reserved 1099776K
  class space    used 7580K, capacity 7973K, committed 8064K, reserved 1048576K
}
Event: 17.994 GC heap before
{Heap before GC invocations=13 (full 3):
 PSYoungGen      total 389632K, used 12859K [0x0000000780600000, 0x0000000799a00000, 0x00000007c0000000)
  eden space 376320K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000797580000)
  from space 13312K, 96% used [0x0000000798d00000,0x000000079998ec28,0x0000000799a00000)
  to   space 17920K, 0% used [0x0000000797700000,0x0000000797700000,0x0000000798880000)
 ParOldGen       total 101888K, used 26675K [0x0000000701200000, 0x0000000707580000, 0x0000000780600000)
  object space 101888K, 26% used [0x0000000701200000,0x0000000702c0cfe0,0x0000000707580000)
 Metaspace       used 56836K, capacity 58967K, committed 59160K, reserved 1099776K
  class space    used 7580K, capacity 7973K, committed 8064K, reserved 1048576K
Event: 18.324 GC heap after
Heap after GC invocations=13 (full 3):
 PSYoungGen      total 389632K, used 0K [0x0000000780600000, 0x0000000799a00000, 0x00000007c0000000)
  eden space 376320K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000797580000)
  from space 13312K, 0% used [0x0000000798d00000,0x0000000798d00000,0x0000000799a00000)
  to   space 17920K, 0% used [0x0000000797700000,0x0000000797700000,0x0000000798880000)
 ParOldGen       total 144384K, used 26517K [0x0000000701200000, 0x0000000709f00000, 0x0000000780600000)
  object space 144384K, 18% used [0x0000000701200000,0x0000000702be5680,0x0000000709f00000)
 Metaspace       used 56836K, capacity 58967K, committed 59160K, reserved 1099776K
  class space    used 7580K, capacity 7973K, committed 8064K, reserved 1048576K
}
Event: 64.490 GC heap before
{Heap before GC invocations=14 (full 3):
 PSYoungGen      total 389632K, used 376320K [0x0000000780600000, 0x0000000799a00000, 0x00000007c0000000)
  eden space 376320K, 100% used [0x0000000780600000,0x0000000797580000,0x0000000797580000)
  from space 13312K, 0% used [0x0000000798d00000,0x0000000798d00000,0x0000000799a00000)
  to   space 17920K, 0% used [0x0000000797700000,0x0000000797700000,0x0000000798880000)
 ParOldGen       total 144384K, used 26517K [0x0000000701200000, 0x0000000709f00000, 0x0000000780600000)
  object space 144384K, 18% used [0x0000000701200000,0x0000000702be5680,0x0000000709f00000)
 Metaspace       used 63336K, capacity 66307K, committed 66560K, reserved 1107968K
  class space    used 8415K, capacity 8928K, committed 8960K, reserved 1048576K
Event: 64.511 GC heap after
Heap after GC invocations=14 (full 3):
 PSYoungGen      total 395776K, used 12928K [0x0000000780600000, 0x000000079ea80000, 0x00000007c0000000)
  eden space 377856K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000797700000)
  from space 17920K, 72% used [0x0000000797700000,0x00000007983a0338,0x0000000798880000)
  to   space 17920K, 0% used [0x000000079d900000,0x000000079d900000,0x000000079ea80000)
 ParOldGen       total 144384K, used 26544K [0x0000000701200000, 0x0000000709f00000, 0x0000000780600000)
  object space 144384K, 18% used [0x0000000701200000,0x0000000702bec2d0,0x0000000709f00000)
 Metaspace       used 63336K, capacity 66307K, committed 66560K, reserved 1107968K
  class space    used 8415K, capacity 8928K, committed 8960K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Internal exceptions (10 events):
Event: 255.027 Thread 0x000000002056a800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000794a5c090) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 255.032 Thread 0x000000002056a800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000794a6c9a0) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 255.037 Thread 0x000000002056a800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000794a82620) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 255.045 Thread 0x000000002056a800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 91> (0x0000000794a9bca0) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\interpreter\interpreterRuntime.cpp, line 366]
Event: 255.045 Thread 0x000000002056a800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000794a9ce10) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 255.054 Thread 0x000000002056a800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 94> (0x0000000794ac8c70) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\interpreter\interpreterRuntime.cpp, line 366]
Event: 255.055 Thread 0x000000002056a800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException': 89> (0x0000000794aca638) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\interpreter\interpreterRuntime.cpp, line 366]
Event: 255.059 Thread 0x000000002056a800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000794acd360) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 255.064 Thread 0x000000002056a800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000794ae5ca8) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 255.082 Thread 0x000000002056a800 Exception <a 'java/security/PrivilegedActionException'> (0x0000000794b12540) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]

Events (10 events):
Event: 255.058 loading class com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer
Event: 255.058 loading class com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer done
Event: 255.063 loading class com/fasterxml/jackson/databind/ser/std/AsArraySerializerBase
Event: 255.063 loading class com/fasterxml/jackson/databind/ser/std/AsArraySerializerBase done
Event: 255.082 loading class com/fasterxml/jackson/core/io/NumberOutput
Event: 255.082 loading class com/fasterxml/jackson/core/io/NumberOutput done
Event: 255.111 loading class org/springframework/web/context/request/WebRequest
Event: 255.111 loading class org/springframework/web/context/request/WebRequest done
Event: 255.112 loading class org/springframework/web/method/annotation/SessionAttributesHandler
Event: 255.113 loading class org/springframework/web/method/annotation/SessionAttributesHandler done


Dynamic libraries:
0x00007ff7781e0000 - 0x00007ff778214000 	C:\Program Files\Java\jdk1.8.0_31\bin\java.exe
0x00007ffbae960000 - 0x00007ffbaeb50000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffbad220000 - 0x00007ffbad2d2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffbac560000 - 0x00007ffbac805000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffbad6a0000 - 0x00007ffbad743000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffbadbc0000 - 0x00007ffbadc5e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffbadee0000 - 0x00007ffbadf77000 	C:\WINDOWS\System32\sechost.dll
0x00007ffbaceb0000 - 0x00007ffbacfcf000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbad080000 - 0x00007ffbad214000 	C:\WINDOWS\System32\USER32.dll
0x00007ffbabb30000 - 0x00007ffbabb51000 	C:\WINDOWS\System32\win32u.dll
0x00007ffbace80000 - 0x00007ffbacea6000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffbabb90000 - 0x00007ffbabd28000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffbac920000 - 0x00007ffbac9be000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffbab8d0000 - 0x00007ffbab9ca000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffbaaca0000 - 0x00007ffbaaf24000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.18362.1556_none_9e7e372fe45e41d5\COMCTL32.dll
0x00007ffbad360000 - 0x00007ffbad696000 	C:\WINDOWS\System32\combase.dll
0x00007ffbac830000 - 0x00007ffbac8b1000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbad050000 - 0x00007ffbad07e000 	C:\WINDOWS\System32\IMM32.DLL
0x000000006e090000 - 0x000000006e162000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\msvcr100.dll
0x000000006d830000 - 0x000000006e08a000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\server\jvm.dll
0x00007ffbae910000 - 0x00007ffbae918000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb9bb90000 - 0x00007ffb9bb99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffbadfa0000 - 0x00007ffbae00f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffba8890000 - 0x00007ffba88b4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffba8860000 - 0x00007ffba888d000 	C:\WINDOWS\SYSTEM32\winmmbase.dll
0x00007ffbac9c0000 - 0x00007ffbaca0a000 	C:\WINDOWS\System32\cfgmgr32.dll
0x000000006d820000 - 0x000000006d82f000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\verify.dll
0x000000006d7f0000 - 0x000000006d818000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\java.dll
0x000000006edd0000 - 0x000000006ee05000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\jdwp.dll
0x000000006edc0000 - 0x000000006edc8000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\npt.dll
0x000000006ed90000 - 0x000000006edb3000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\instrument.dll
0x000000006d7d0000 - 0x000000006d7e6000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\zip.dll
0x00007ffbae150000 - 0x00007ffbae837000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffbaddd0000 - 0x00007ffbade78000 	C:\WINDOWS\System32\shcore.dll
0x00007ffbabd30000 - 0x00007ffbac4ab000 	C:\WINDOWS\System32\windows.storage.dll
0x00007ffbab860000 - 0x00007ffbab87e000 	C:\WINDOWS\System32\profapi.dll
0x00007ffbab880000 - 0x00007ffbab8ca000 	C:\WINDOWS\System32\powrprof.dll
0x00007ffbab810000 - 0x00007ffbab820000 	C:\WINDOWS\System32\UMPDC.dll
0x00007ffbae010000 - 0x00007ffbae062000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffbab840000 - 0x00007ffbab851000 	C:\WINDOWS\System32\kernel.appcore.dll
0x00007ffbac810000 - 0x00007ffbac827000 	C:\WINDOWS\System32\cryptsp.dll
0x000000006ed80000 - 0x000000006ed89000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\dt_socket.dll
0x00007ffbaa940000 - 0x00007ffbaa9a7000 	C:\WINDOWS\system32\mswsock.dll
0x000000006d7b0000 - 0x000000006d7ca000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\net.dll
0x00007ffbaa6c0000 - 0x00007ffbaa78b000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffbae070000 - 0x00007ffbae078000 	C:\WINDOWS\System32\NSI.dll
0x00007ffbaa680000 - 0x00007ffbaa6ba000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb9e0e0000 - 0x00007ffb9e0ea000 	C:\Windows\System32\rasadhlp.dll
0x00007ffb9f320000 - 0x00007ffb9f397000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffbabb60000 - 0x00007ffbabb86000 	C:\WINDOWS\System32\bcrypt.dll
0x000000006ee10000 - 0x000000006ee1d000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\management.dll
0x000000006d790000 - 0x000000006d7a1000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\nio.dll
0x00007ffbaa4f0000 - 0x00007ffbaa523000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffbab700000 - 0x00007ffbab725000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffbaab60000 - 0x00007ffbaab6c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb9f7c0000 - 0x00007ffb9f7d6000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffba0740000 - 0x00007ffba075c000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffb83ce0000 - 0x00007ffb83cf6000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffb83cc0000 - 0x00007ffb83cda000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffb83c90000 - 0x00007ffb83c9e000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffba6fe0000 - 0x00007ffba6ffc000 	C:\WINDOWS\system32\NLAapi.dll
0x00007ffb83d80000 - 0x00007ffb83d95000 	C:\WINDOWS\system32\wshbth.dll
0x0000000180000000 - 0x0000000180286000 	F:\xampp\tomcat\bin\tcnative-1.dll
0x00007ffbab9d0000 - 0x00007ffbabb21000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffbab820000 - 0x00007ffbab832000 	C:\WINDOWS\System32\MSASN1.dll
0x000000006ed50000 - 0x000000006ed74000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\sunec.dll
0x00007ffbab200000 - 0x00007ffbab3f4000 	C:\WINDOWS\SYSTEM32\dbghelp.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:1082,suspend=y,server=n -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2020.1\captureAgent\debugger-agent.jar -Dfile.encoding=UTF-8 
java_command: com.sss.fatora.FatoraApplication
java_class_path (initial): C:\Program Files\Java\jdk1.8.0_31\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\deploy.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\access-bridge-64.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\cldrdata.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\dnsns.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\jaccess.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\jfxrt.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\localedata.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\nashorn.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunec.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunjce_provider.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunmscapi.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunpkcs11.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\zipfs.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\javaws.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jfxswt.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\management-agent.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\plugin.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\rt.jar;F:\Projects\Fatora\target\classes;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.13\commons-codec-1.13.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.19\commons-compress-1.19.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curv
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-10.0.2
PATH=F:\programs\apache-maven-3.6.2\bin;C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-10.0.2\bin;C:\Program Files\Java\jre-10.0.2\bin;C:\Program Files\Java\jdk-10.0.2;C:\Program Files\Java\jre-10.0.2;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;F:\xampp\tomcat\bin;F:\xampp\mysql\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files\Microsoft SQL Server\120\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\120\Tools\Binn\ManagementStudio\;C:\Program Files (x86)\Microsoft SQL Server\120\DTS\Binn\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\RabbitMQ Server\rabbitmq_server-3.8.1\ebin;C:\Program Files\RabbitMQ Server;F:\programs\oracle\Oracle_Database_12c_Release_2_v12.2.0.1.0;search-ms:displayname=Search%20Results%20in%20Oracle_Database_12c_Release_2_v12.2.0.1.0&crumb=location:F%3A%5Cprograms%5Coracle%5COracle_Database_12c_Release_2_v12.2.0.1.0\bin;F:\app\pc\virtual\product\12.2.0\dbhome_1\bin;F:\intellij\IntelliJ IDEA 2019.3.1\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Microsoft SQL Server\110\Tools\Binn\;F:\xampp\php;C:\ProgramData\ComposerSetup\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-10.0.2\bin;C:\Program Files\Java\jdk-10.0.2;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\heroku\bin;F:\Flutter\flutter\bin;C:\Users\<USER>\SDK\tools;C:\Users\<USER>\SDK\platform-tools;F:\programs\Sdk;F:\programs\Sdk\build-tools;F:\programs\Sdk\tools\bin;F:\programs\Sdk\cmdline-tools\latest\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin
USERNAME=pc
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 69 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 8.1 , 64 bit Build 9600 

CPU:total 4 (2 cores per cpu, 2 threads per core) family 6 model 69 stepping 1, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2

Memory: 4k page, physical 12502096k(4564960k free), swap 18531408k(3684492k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.31-b07) for windows-amd64 JRE (1.8.0_31-b13), built on Dec 17 2014 21:00:28 by "java_re" with MS VC++ 10.0 (VS2010)

time: Tue Jun 15 14:48:25 2021
elapsed time: 256 seconds (0d 0h 4m 16s)

