package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

@Setter
@Getter
public class Account1 extends Account {
    @ExcelProperty(name = "id8")
    private String id;
    @ExcelProperty(name = "ns1:command9")
    private String command;
    @ExcelProperty(name = "account_1:account_number")
    private String account_number;
    @ExcelProperty(name = "account_1:currency")
    private String currency;
    @ExcelProperty(name = "account_1:account_type")
    private String account_type;
    private AccountObject account_object;

    @XmlAttribute(name = "id")
    public String getId() {
        return id;
    }


    public void setId(String id) {
        this.id = id;
    }
}
