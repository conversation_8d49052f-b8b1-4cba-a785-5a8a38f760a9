package com.sss.fatora.utils.constants;

public enum  ApplicationStatus {
    DRAFT("draft"),
    SUBMIT("submit"),
//    INPROGRESS("in_progress"),
//    SUCCESS("success"),
//    FAILED("failed");
    ARCHIVE("archive");
    private String status;

    ApplicationStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
