package com.sss.fatora.controller.issuing;

import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.read.DTO.CardsBoDTO;
import com.sss.fatora.service.issuing.CustomerService;
import com.sss.fatora.service.read.CardsBoService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.List;

@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Autowired
    CustomerService customerService;

    @Autowired
    CardsBoService cardsBoService;

    @RequestMapping(method = RequestMethod.GET)
    public ResponseObject getCustomer(@RequestParam(value = "customerNumber")String customerNumber){
        Map requestedCustomer = customerService.getCustomerByNumber(customerNumber);
        return requestedCustomer != null ?
                ResponseObject.FETCHED_SUCCESS(requestedCustomer,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }

//    @PreAuthorize("hasAnyAuthority('Admin','Search Cards')")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResponseObject search(@ParameterName(value = "filter", required = false) Customer customer,
                                 @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination) throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {
        if (customer == null)
            customer = new Customer();
        Page<Customer> customers = customerService.mainSearch(customer, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", customers != null ? customers.getTotalElements() : 0);
        return customers != null ?
                ResponseObject.FETCHED_SUCCESS(customers.getContent(), extra) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/get-with-details", method = RequestMethod.POST)
    public ResponseObject getWithDetails(@RequestParam("customerNumber") String customerNumber) throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {

        Customer filter = new Customer();
        Customer card = customerService.getWithDetails(filter, customerNumber);
        return card != null ?
                ResponseObject.FETCHED_SUCCESS(card, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
    @RequestMapping(value = "/get-cards-by-customer-number", method = RequestMethod.GET)
    public ResponseObject getCardsByCustomerNumber(@RequestParam("customerNumber") String customerNumber)  {

       List <CardsBoDTO> cardsBoList
          = cardsBoService.getCardsByCustomerNumber( customerNumber);
        return cardsBoList != null ?
                ResponseObject.FETCHED_SUCCESS(cardsBoList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


}
