package com.sss.fatora.domain.middleware.Merchants.dto;
import com.sss.fatora.utils.constants.AcquiringActionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class UpdateMerchantRequest {
    private AcquiringActionType actionType;
    private String oldMerchantName;
    private String merchantNumber;
    private String oldMobileNumber;
    private String oldEmail;
    private String oldMcc;
    private String newMerchantName;
    private String newMobileNumber;
    private String newEmail;
    private String newMcc;
}
