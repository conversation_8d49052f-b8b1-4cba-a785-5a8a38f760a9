package com.sss.fatora;

import com.sss.fatora.domain.read.CountryCodes;
import com.sss.fatora.service.generic.GenericService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.annotation.RequestScope;

@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableCaching
@EnableScheduling
public class FatoraApplication {

	public static void main(String[] args) {
		SpringApplication.run(FatoraApplication.class, args);
	}


	@Bean
	@RequestScope
	public RestTemplate getRestTemplate(){
		return new RestTemplate();
	}
}
