package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_CARD_ACCOUNT")
public class AccountVW {

    @Id
    @Column(name = "ACCT_NO")
    private String acctNo;

    @Column(name = "CARD_NO")
    private String cardNo;
    @Column(name = "ACCT_TYPE_BO")
    private String accountType;
    @Column(name = "CURRENCY_TYPE")
    private String currencyType;
    @Column(name = "IS_ATM_PRIMARY")
    private Boolean atmPrimary;
    @Column(name = "IS_POS_PRIMARY")
    private Boolean posPrimary;
    @Column(name = "COUNT_LIMIT")
    private Long countLimit;
    @Column(name = "COUNT_USED")
    private Long countUsed;
    @Column(name = "AMOUNT_LIMIT")
    private Long amountLimit;
    @Column(name = "AMOUNT_USED")
    private Long amountUsed;

    @Transient
    private String accountNumber;
}
