package com.sss.fatora.domain.middleware.Merchants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class Merchant {
    private String bank;
    private String merchantName;
    private String merchantNumber;
    private String customerNumber;
    private String mcc;
    private String mccDesc;
    private String agentNumber;
    private String agentShortDesc;
    private String accountNumber;
    private String mobileNumber;
    private String emailAddress;
    private String country;
    private String region;
    private String street;
    private String house;
    private String feePercentage;
    private String latitude;
    private String longitude;
}
