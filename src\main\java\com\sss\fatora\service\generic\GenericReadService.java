package com.sss.fatora.service.generic;

import com.sss.fatora.dao.read.GenericReadDao;
import com.sss.fatora.dao.specification.SpecificationsBuilder;
import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.domain.generic.GenericDomain;
import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.smpp.MessageLog;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.log.Loggable;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.PaginationService;
import com.sss.fatora.utils.service.RefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.Transient;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Transactional("readingDBTransactionManager")
public class GenericReadService<Dao extends GenericReadDao, Domain, IdClass extends Serializable> {
    @Autowired
    protected Dao dao;
    @Autowired
    PaginationService paginationService;
    @Autowired
    private RefService refService;
    @Autowired
    @Qualifier("readingDBEntityManagerFactory")
    private EntityManager readingEntityManager;

    @Autowired
    @Qualifier("settlementDBEntityManagerFactory")
    private EntityManager settlementEntityManager;

    @Autowired
    @Qualifier("issuingDBEntityManagerFactory")
    private EntityManager issuingEntityManager;
    @Autowired
    @Qualifier("smppDBEntityManagerFactory")
    private EntityManager smppEntityManager;

    /**
     * This Function Add Joins To The Query If Needed And Add Constrains
     */
    private SpecificationsBuilder rec(SpecificationsBuilder builder, Object domain, String parent,
            Map<String, Operator> filterOperator)
            throws IntrospectionException, InvocationTargetException, IllegalAccessException, NoSuchFieldException {
        for (PropertyDescriptor pd : Introspector.getBeanInfo(domain.getClass()).getPropertyDescriptors()) {
            Class<?> classType = pd.getPropertyType();
            Object propertyValue = pd.getReadMethod().invoke(domain);
            Boolean isTransient = false;
            if (!classType.equals(Class.class))
                isTransient = pd.getReadMethod().isAnnotationPresent(Transient.class);

            if (propertyValue != null && !classType.equals(Class.class) && !isTransient) {
                if (propertyValue instanceof GenericDomain) {
                    builder.join(" LEFT JOIN " + parent + "." + pd.getName() + " AS " + pd.getName() + " ");
                    rec(builder, propertyValue, pd.getName(), filterOperator);
                } else if (propertyValue instanceof Set) {
                    builder.join(" LEFT JOIN Fetch " + parent + "." + pd.getName() + " AS " + pd.getName() + " ");
                    if (!((Set) propertyValue).isEmpty())
                        rec(builder, ((Set) propertyValue).iterator().next(), pd.getName(), filterOperator);
                } else {
                    builder.add(parent + "." + pd.getName(), propertyValue, filterOperator.get(pd.getName()));
                }
            }
        }
        return builder;
    }

    /**
     * @param filter         This Parameter Have Filters To Be Used In The Where
     *                       Clause Of The Query
     * @param filterOperator There Is An Specific Operator For Every Filter
     *                       {@link com.sss.fatora.utils.constants.Operator}
     * @param projection     This Is A List Of Columns For Select Statement In
     *                       Search Query
     *                       <p>
     *                       This Function Build The Final Query As A String And
     *                       Select The Correct Db ( EntityManager )
     *                       Where The Query Will Be Executed And Then Return The
     *                       Result
     */
    public Page dynamicSearch(Domain filter, Pagination pagination, String additionalConstraints,
            Map<String, Operator> filterOperator, List<String> projection,Boolean isCount, Function... filterFunctions)
            throws IllegalAccessException, IntrospectionException, InvocationTargetException, NoSuchFieldException {
        SpecificationsBuilder builder = new SpecificationsBuilder(filter.getClass());
        SpecificationsBuilder specifications = rec(builder, filter, filter.getClass().getSimpleName(), filterOperator);
        builder.addConstraint(additionalConstraints);
        String buildQuery = specifications.buildWithoutDistinct(pagination, projection);

        Query query;
        Long count;
        if(isCount){
        if (filter instanceof SettlementTransaction) {
            query = settlementEntityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCountWithoutDistinct(), settlementEntityManager);
        } else if (filter instanceof Customer) {
            query = issuingEntityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCountWithoutDistinct(), issuingEntityManager);
        } else if (filter instanceof MessageLog) {
            query = smppEntityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCount(), smppEntityManager);
        } else {
            query = readingEntityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCountWithoutDistinct(), readingEntityManager);
        }

        }else{
                  if (filter instanceof SettlementTransaction) {
            query = settlementEntityManager.createQuery(buildQuery);
            count = Long.parseLong("999");
        } else if (filter instanceof Customer) {
            query = issuingEntityManager.createQuery(buildQuery);
            count = Long.parseLong("999");
        } else if (filter instanceof MessageLog) {
            query = smppEntityManager.createQuery(buildQuery);
            count = Long.parseLong("999");
        } else {
            query = readingEntityManager.createQuery(buildQuery);
            count = Long.parseLong("999");
        }  
        }


        Pagination usedPagination = this.getPagination(pagination, count);
        if (pagination != null) {
            query.setMaxResults(usedPagination.getSize());
            query.setFirstResult(usedPagination.getStart() * usedPagination.getSize());
        }
        // to put search query in the filter for Log Aspect
        if (filter instanceof Loggable) {
            ((Loggable) filter).setQuery(buildQuery);
        }
        List<Domain> res = query.getResultList();
        if (filterFunctions != null && filterFunctions.length > 0) {
            Stream stream = res.stream();
            for (Function filterFunction : filterFunctions) {
                stream = stream.map(filterFunction);
            }
            res = (List) stream.collect(Collectors.toList());
        }
        if (res.size() > 0) {
        
            Page<Domain> result = new PageImpl<Domain>(res, PageRequest.of(usedPagination.getStart(),
                    usedPagination.getSize()), count);
            return result;
        } else
            return null;
    }

    // private <Domain> void ifCardDataVW(Domain filter , SpecificationsBuilder
    // specifications) {
    // if(filter.getClass().getSimpleName().equalsIgnoreCase("CardDataVW") ){
    // specifications.join(" LEFT JOIN AccountVW AS AccountVW ON AccountVW.cardNo =
    // CardDataVW.crefNo ");
    // }
    // }

    /**
     * @param queryStr This Parameter Contains The Query To Be Executed
     *                 <p>
     *                 This Function Create The Query In The Specified Entity
     *                 Manager And Then Get A Count
     *                 Of The Result Of The Query
     */
    private Long getCountByQuery(String queryStr, EntityManager entityManager) {
        Query query = entityManager.createQuery(queryStr);
        return (Long) query.getSingleResult();
    }

    /**
     * This Generic Function Add Pagination If Needed To The Dynamic Search
     */
    private Pagination getPagination(Pagination pagination, Long count) {
        if (pagination == null) {
            pagination = new Pagination();
        }
        if (pagination.getSize() == null)
            pagination.setSize(Math.toIntExact(count));
        if (pagination.getStart() == null)
            pagination.setStart(0);
        return pagination;
    }

    public List<Domain> getAll() {
        try {
            return dao.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void addInnerJoin(Object filter, SpecificationsBuilder specifications) {
        // if (filter instanceof Messages) {
        // specifications.join(" INNER JOIN MessageTypes AS MessageTypes ON
        // Messages.messageTypeId = MessageTypes.id ");
        // }

    }
}
