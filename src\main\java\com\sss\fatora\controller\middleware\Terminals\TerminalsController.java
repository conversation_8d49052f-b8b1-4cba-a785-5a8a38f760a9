package com.sss.fatora.controller.middleware.Terminals;

import com.sss.fatora.domain.middleware.terminals.*;
import com.sss.fatora.domain.middleware.terminals.clients.clientRequest;
import com.sss.fatora.domain.middleware.terminals.clients.clientsResponse;
import com.sss.fatora.domain.middleware.terminals.dto.ATM.AddATMRequest;
import com.sss.fatora.domain.middleware.terminals.dto.ATM.ChangeDenominationsRequest;
import com.sss.fatora.domain.middleware.terminals.dto.EPOS.*;
import com.sss.fatora.domain.middleware.terminals.dto.POS.*;
import com.sss.fatora.domain.middleware.terminals.mcc.mccListResponse;
import com.sss.fatora.service.middleware.terminals.TerminalsService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/terminals")
public class TerminalsController {
    private final TerminalsService terminalsService;

    public TerminalsController(TerminalsService terminalsService) {
        this.terminalsService = terminalsService;
    }

    @RequestMapping("/POS")
    public ResponseObject getPOSTerminals(@ParameterName("terminal") TerminalRequest terminalRequest,
            @ParameterName(value = "pagination", required = false) Pagination pagination) {
        TerminalResponse posTerminals = terminalsService.getPOSTerminals(terminalRequest, pagination);
        return posTerminals != null ? ResponseObject.FETCHED_SUCCESS(posTerminals, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/POS/details")
    public ResponseObject getPOSTerminalsDetails(@ParameterName("terminal") TerminalRequestById terminalRequest,
            @ParameterName(value = "pagination", required = false) Pagination pagination) {
        TerminalResponse posTerminals = terminalsService.getPOSTerminalById(terminalRequest, pagination);
        return posTerminals != null ? ResponseObject.FETCHED_SUCCESS(posTerminals, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/EPOS")
    public ResponseObject getEPOSTerminals(@ParameterName("terminal") TerminalRequest terminalRequest,
            @ParameterName(value = "pagination", required = false) Pagination pagination) {
        TerminalResponse posTerminals = terminalsService.getEPOSTerminals(terminalRequest, pagination);
        return posTerminals != null ? ResponseObject.FETCHED_SUCCESS(posTerminals, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/EPOS/details")
    public ResponseObject getEPOSTerminalsDetails(@ParameterName("terminal") TerminalRequestById terminalRequest,
            @ParameterName(value = "pagination", required = false) Pagination pagination) {
        TerminalResponse posTerminals = terminalsService.getEPOSTerminalById(terminalRequest, pagination);
        return posTerminals != null ? ResponseObject.FETCHED_SUCCESS(posTerminals, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/ATM")
    public ResponseObject getATMTerminals(@ParameterName("terminal") TerminalRequest terminalRequest,
            @ParameterName(value = "pagination", required = false) Pagination pagination) {
        TerminalResponse posTerminals = terminalsService.getATMTerminals(terminalRequest, pagination, false);
        return posTerminals != null ? ResponseObject.FETCHED_SUCCESS(posTerminals, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/ATM/details")
    public ResponseObject getATMTerminalsWithDetails(@ParameterName("terminal") TerminalRequest terminalRequest,
            @ParameterName(value = "pagination", required = false) Pagination pagination) {
        TerminalResponse posTerminals = terminalsService.getATMTerminals(terminalRequest, pagination, true);
        return posTerminals != null ? ResponseObject.FETCHED_SUCCESS(posTerminals, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/EPOS/eGate")
    public ResponseObject getEGateData(@ParameterName("client") clientRequest clientRequest) {
        clientsResponse clients = terminalsService.getClientEGateData(clientRequest);
        return clients != null ? ResponseObject.FETCHED_SUCCESS(clients, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/by-merchant")
    public ResponseObject GetTerminalsByMerchantNumber(@ParameterName("terminal") TerminalRequest terminalRequest) {
        TerminalResponse terminals = terminalsService.GetTerminalsByMerchantNumber(terminalRequest);
        return terminals != null ? ResponseObject.FETCHED_SUCCESS(terminals, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/mcc-list")
    public ResponseObject getMCCList() {
        mccListResponse mccList = terminalsService.getMCCList();
        return mccList != null ? ResponseObject.FETCHED_SUCCESS(mccList, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/terminal-customer")
    public ResponseObject getTerminalCustomer(String terminalNumber, String bank) {
        TerminalResponse terminalCustomer = terminalsService.getTerminalCustomer(terminalNumber);
        return terminalCustomer != null ? ResponseObject.FETCHED_SUCCESS(terminalCustomer, null)
                : ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/POS/export")
    public ResponseObject exportPOSTerminals(@ParameterName("terminal") TerminalRequest terminalRequest,
            @ParameterName("withDetails") Boolean withDetails,
            HttpServletResponse response) {
        TerminalResponse posTerminals = terminalsService.exportPOSTerminals(terminalRequest, withDetails, response);
        return ResponseObject.FETCHED_SUCCESS(posTerminals, null);
    }

    @RequestMapping("/EPOS/export")
    public ResponseObject exportEPOSTerminals(@ParameterName("terminal") TerminalRequest terminalRequest,
            @ParameterName("withDetails") Boolean withDetails,
            HttpServletResponse response) {
        TerminalResponse eposTerminals = terminalsService.exportEPOSTerminals(terminalRequest, withDetails, response);
        return ResponseObject.FETCHED_SUCCESS(eposTerminals, null);
    }

    @RequestMapping("/ATM/export")
    public ResponseObject exportATMTerminals(@ParameterName("terminal") TerminalRequest terminalRequest,
            @ParameterName("withDetails") Boolean withDetails,
            HttpServletResponse response) {
        TerminalResponse atmTerminals = terminalsService.exportATMTerminals(terminalRequest, withDetails, response);
        return ResponseObject.FETCHED_SUCCESS(atmTerminals, null);
    }

    // POS

    @PostMapping("/POS/add")
    public ResponseObject addPos(@RequestBody AddTerminalRequest terminalRequest) {
        terminalsService.addPos(terminalRequest);
        return ResponseObject.ADDED_SUCCESS(null, null);
    }

    @PostMapping("/POS/close-terminal")
    public ResponseObject closePosTerminal(@RequestBody ClosePOSTerminalRequest closePOSTerminalRequest) {
        terminalsService.closePosTerminal(closePOSTerminalRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    @PostMapping("/POS/replace-pos-device")
    public ResponseObject replacePOSDevice(@RequestBody ReplacePOSDeviceRequest replacePOSDeviceRequest) {
        terminalsService.replacePOSDevice(replacePOSDeviceRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    @PostMapping("/POS/change-account")
    public ResponseObject changeAccountPOS(@RequestBody @Valid ChangeAccountPOSRequest changeAccountPOSRequest) {
        terminalsService.changeAccountPOS(changeAccountPOSRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    @PostMapping("/POS/edit-gps-coordinates")
    public ResponseObject editGPSCoordinates(
            @RequestBody @Valid EditGPSCoordinatesPOSRequest editGPSCoordinatesPOSRequest) {
        terminalsService.editGPSCoordinates(editGPSCoordinatesPOSRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    // E-POS
    @PostMapping("/EPOS/add")
    public ResponseObject addEPos(@ParameterName("terminalRequest") AddEPosRequest terminalRequest,
            @RequestPart(value = "logo", required = true) MultipartFile logo) {
        terminalsService.addEPos(terminalRequest, logo);
        return ResponseObject.ADDED_SUCCESS(null, null);
    }

    @PostMapping("/EPOS/close-terminal")
    public ResponseObject closeEPosTerminal(@RequestBody CloseEPOSTerminalRequest closeEPOSTerminalRequest) {
        terminalsService.closeEPosTerminal(closeEPOSTerminalRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    @PostMapping("/EPOS/manage-terminals-ids")
    public ResponseObject manageEPOSTerminalsIds(@RequestBody ManageEPOSTerminalRequest manageEPOSTerminalRequest) {
        terminalsService.manageEPOSTerminalsIds(manageEPOSTerminalRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    @PostMapping("/EPOS/change-account")
    public ResponseObject changeAccountEPOS(@RequestBody @Valid ChangeAccountEPOSRequest changeAccountEPOSRequest) {
        terminalsService.changeAccountEPOS(changeAccountEPOSRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    @PostMapping("/EPOS/change-egate-data")
    public ResponseObject changeEGateDataEPOS(@RequestBody @Valid ChangeEGateDataEPOSRequest changeEGateDataEPOSRequest,
            @RequestPart(value = "logo", required = false) MultipartFile logo) {
        terminalsService.changeEGateDataEPOS(changeEGateDataEPOSRequest, logo);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

    // ATM
    @PostMapping("/ATM/add")
    public ResponseObject addATM(@RequestBody AddATMRequest terminalRequest) {
        terminalsService.addATM(terminalRequest);
        return ResponseObject.ADDED_SUCCESS(null, null);
    }

    @PostMapping("/ATM/change-denominations")
    public ResponseObject changeDenominations(@RequestBody ChangeDenominationsRequest terminalRequest) {
        terminalsService.changeDenominations(terminalRequest);
        return ResponseObject.ADDED_SUCCESS(null, null);
    }

    @PostMapping("/ATM/change-account")
    public ResponseObject changeAccountATM(@RequestBody @Valid ChangeAccountPOSRequest changeAccountPOSRequest) {
        terminalsService.changeAccountATM(changeAccountPOSRequest);
        return ResponseObject.UPDATED_SUCCESS(null, null);
    }

}
