package com.sss.fatora.controller.read;

import com.sss.fatora.domain.read.CardLimit;
import com.sss.fatora.service.read.CardLimitService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/card-limits")
public class CardLimitController {
    final private CardLimitService cardLimitService;

    public CardLimitController(CardLimitService cardLimitService) {
        this.cardLimitService = cardLimitService;
    }

    @RequestMapping(value = "/by-cardNo", method = RequestMethod.GET)
    public ResponseObject getByCardNumber(@RequestParam("cardNumber") String cardNumber){
        List<CardLimit> cardLimitList = cardLimitService.getByCardNumber(cardNumber);
        return cardLimitList != null ?
                ResponseObject.FETCHED_SUCCESS(cardLimitList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
