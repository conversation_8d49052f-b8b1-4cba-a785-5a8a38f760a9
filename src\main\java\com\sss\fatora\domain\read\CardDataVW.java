package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_CARDS")
//@Table(name = "CARD_DATA_VW")
//we must put the name in fatora server like this  (SVISTA.CARD_DATA_VW)
public class CardDataVW implements Loggable {
    @Id
    @Column(name = "CREF_NUM")
    Long crefNoInt;
    @Column(name = "Cref_No")
    String crefNo;
    @Column(name = "Issue_Dt")
    Date issueDate;
    @Column(name = "Exp_Dt")
    Long expDate;
    @Column(name = "Statu_Desc")
    String statusDesc;
    @Column(name = "NAME")
    String name;
    @Column(name = "Embos_Name")
    String embosName;
    @Column(name = "CUSTOMER_NUMBER")
    String customerNumberCards;
    @Column(name = "SURNAME")
    String surname;
    @Column(name = "Mobile_Phone")
    String mobilePhone;
    @Column(name = "PIN_COUNTER")
    Long pinCnt;
    @Column(name = "STATUS")
    Long status;
    @Column(name = "ADDR")
    String firstAddress;
    @Column(name = "ADDR2")
    String secondAddress;
    @Column(name = "CITY")
    String city;
    @Column(name = "AGENT_CODE")
    String agentCode;
//    @Column(name = "Count_Limit")
//    Long countLimit;
//    @Column(name = "Count_Used")
//    Long countUsed;
//    @Column(name = "Amount_Limit")
//    Long amountLimit;
//    @Column(name = "Amount_Used")
//    Long amountUsed;
    @Column(name = "PRODUCT")
    String product;
    @Column(name = "BANK_CODE")
    String bankCode;


    @Column(name = "LAST_UPDATE")
    String lastUpdate;
    @Column(name = "PRIM_ATM_ACCTP")
    Long primATMAcctp;
    @Column(name = "PRIM_POS_ACCTP")
    Long primPOSAcctp;
    @Column(name = "LANG_CODE")
    String langCode;
    @Column(name = "PIN_MAILER")
    String pinMailer;
    @Column(name = "CARD_TYPE")
    String cardType;
    @Column(name = "USER_DATA")
    String userData;
    @Column(name = "CARDHOLDER_ID")
    String cardHolderId;
    @Column(name = "COUNTRY")
    String country;
    @Column(name = "VIP_CODE")
    Long vipCode;
    @Column(name = "CRD_STAT_DATE")
    String crdStatDate;
    @Column(name = "CONTRACT_ID")
    String contractId;
    @Column(name = "IS_RESIDENT")
    Long resident;
    @Column(name = "SCHEME_ID")
    Long schemeId;
    @Column(name = "ADDRESS_ID")
    String addressId;
    @Column(name = "FEE_SCHEME")
    Long feeSCheme;
    @Column(name = "IS_PRIMARY")
    Long primary;
    @Column(name = "BIRTHDATE")
    String birthdate;
    @Column(name = "CARD_ID")
    String cardId;
    @Column(name = "PIN_CHG_UTRNNO")
    Long pinChgUtrnno;

    @Column(name = "CARD_REGION")
    String cardRegion;

    @Column(name = "LAST_ATC")
    Long lastAtc;

    @Transient
    @JsonIgnore
    String query;

    @Override
    @Transient
    public String fetchId() {
        if (this.crefNo != null)
            return this.getCrefNo();
        return null;
    }

    @Override
    @Transient
    public String getRelatedEntity() {
//        return this.getClass().getSimpleName();
        return "Cards";
    }

    @Override
    public void setQuery(String query) {
        this.query = query;
    }

    @Override
    public String getQuery() {
        return this.query;
    }


}
