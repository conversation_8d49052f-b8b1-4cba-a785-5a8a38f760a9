package com.sss.fatora.utils.log;

import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.LogType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Log {
    ActionType actionType();
}
