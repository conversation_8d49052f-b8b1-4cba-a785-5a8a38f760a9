package com.sss.fatora.controller.converter;


import com.sss.fatora.domain.converter.Applications;
import com.sss.fatora.domain.local.ActionRequest;
import com.sss.fatora.service.converter.ExcelFileService;
import com.sss.fatora.service.local.ActionRequestService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.BulkTemplateType;
import com.sss.fatora.utils.model.ResponseObject;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/excel-converter")
public class ExcelFileController {
    @Autowired
    ExcelFileService excelFileService;
    private final ContentUtilService contentUtilService;

    @Autowired
    ActionRequestService actionRequestService;

    @Autowired
    public ExcelFileController(ContentUtilService contentUtilService) {
        this.contentUtilService = contentUtilService;
    }

    @RequestMapping(value = "convert-to-xml", method = RequestMethod.POST)
    public void convertToXml(@ParameterName("file") TempContentModel tempContentModel, HttpServletResponse response) throws Exception {
        String data = excelFileService.convertExcelToXml(tempContentModel);
        response.getOutputStream().write(data.getBytes());
        response.setContentType("application/xml");

    }

    @RequestMapping(value = "import-draftT", method = RequestMethod.POST)
    public ResponseObject importDraftT(@ParameterName(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        Applications applications = excelFileService.convertExcelToDatabase(tempContentModel);
        return applications != null ?
                ResponseObject.ADDED_SUCCESS(applications, null) :
                ResponseObject.ADDING_FAILED(null, null);
        // response.getOutputStream().write(data.getBytes());
        //response.setContentType("application/xml");

    }

    @RequestMapping(value = "import-draft", method = RequestMethod.POST)
    public ResponseObject importDraft(@ParameterName("file") TempContentModel tempContentModel, HttpServletResponse response) throws Exception {
        Applications applications = excelFileService.convertExcelToDatabase(tempContentModel);
        return applications != null ?
                ResponseObject.ADDED_SUCCESS(applications, null) :
                ResponseObject.ADDING_FAILED(null, null);
        // response.getOutputStream().write(data.getBytes());
        //response.setContentType("application/xml");

    }

    @RequestMapping(value = "upload", method = RequestMethod.POST)
    public ResponseObject uploadToServer(@RequestParam(value = "content", required = false) MultipartFile multipartFile) throws IOException, IllegalAccessException {
        try {
            Assert.notNull(multipartFile, "upload_null_file");
            int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
            String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
//            if (Arrays.asList(illegalExtensions).stream().anyMatch(s -> s.equalsIgnoreCase(extension))) {
//                throw new IllegalAccessException();
//            }
            TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                    multipartFile.getContentType(), extension);
            return ResponseObject.ADDED_SUCCESS(tempContentModel, null);
        } catch (Exception exception) {
            throw exception;
        }
    }

    @RequestMapping(value = "import-close-card", method = RequestMethod.POST)
    public ResponseObject importCloseCardExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Close_Card.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }
    @RequestMapping(value = "import-reissue-card", method = RequestMethod.POST)
    public ResponseObject importReissueCardExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Reissue_Card.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }
    @RequestMapping(value = "import-instant-reissue-card", method = RequestMethod.POST)
    public ResponseObject importInstantReissueCardExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Instant_Reissue_Card.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }

    @RequestMapping(value = "import-renew-card", method = RequestMethod.POST)
    public ResponseObject importRenewCardExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Renew_Card.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }

    @RequestMapping(value = "import-instant-renew-card", method = RequestMethod.POST)
    public ResponseObject importInstantRenewCardExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Instant_Renew_Card.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) : 
                ResponseObject.ADDING_FAILED(null, null);
    }

    @RequestMapping(value = "import-change-language", method = RequestMethod.POST)
    public ResponseObject importChangeLanguageExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Change_Language.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }
    @RequestMapping(value = "import-change-product", method = RequestMethod.POST)
    public ResponseObject importChangeProductExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response, @RequestParam(value= "productID")Long productId ) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveChangeProductRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Change_Product.getType(),productId); 
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }


    @RequestMapping(value = "import-status-changes", method = RequestMethod.POST)
    public ResponseObject importChangeCardStatusExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Change_Status.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }

    @RequestMapping(value = "import-validate-card", method = RequestMethod.POST)
    public ResponseObject importValidateCardExcel(@RequestParam(value = "content") MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        List<ActionRequest> actionRequestList = actionRequestService.saveRequestsExcelToDatabase(tempContentModel,
                BulkTemplateType.Validate_Card.getType());
        return (actionRequestList != null && !actionRequestList.isEmpty()) ?
                ResponseObject.ADDED_SUCCESS(actionRequestList, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }

}
