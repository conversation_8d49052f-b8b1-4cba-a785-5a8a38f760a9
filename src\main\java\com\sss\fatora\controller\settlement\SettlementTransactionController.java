package com.sss.fatora.controller.settlement;

import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.service.settlement.SettlementTransactionService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Optional;

@RestController
@RequestMapping("/settlement-transaction")
public class SettlementTransactionController {
    private final SettlementTransactionService settlementTransactionService;

    public SettlementTransactionController(SettlementTransactionService settlementTransactionService) {
        this.settlementTransactionService = settlementTransactionService;
    }

    @PreAuthorize("hasAnyAuthority('Admin','THEM-on-US')")
    @PostMapping(value = "/search")
    public ResponseObject getTransactions(@ParameterName(value = "filter", required = false) SettlementTransaction transaction,
                                          @ParameterName(value = "fromDate", required = false) Long fromDate,
                                          @ParameterName(value = "toDate", required = false) Long toDate,
                                          @ParameterName(value = "showReconciled", required = false) Boolean showReconciled,
                                          @ParameterName(value = "showSettled", required = false) Boolean showSettled,
                                          @ParameterName(value = "pagination", required = false) Pagination pagination,
                                          @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                          @ParameterName(value = "withBins", required = false) Boolean withBins) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        if (transaction == null)
            transaction = new SettlementTransaction();
        Page<SettlementTransaction> transactions = settlementTransactionService.mainSearch(transaction, fromDate,
                toDate, showReconciled, showSettled,pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),withBins);
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", transactions != null ? transactions.getTotalElements() : 0);
        return ResponseObject.FETCHED_SUCCESS(transactions!=null?transactions.getContent():null, extra);
    }

    @RequestMapping(value = "/get-with-details", method = RequestMethod.POST)
    public ResponseObject getWithDetails(@RequestParam("recordId") Integer recordId,
                                         @RequestParam(value = "withBins", required = false) Boolean withBins)
            throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {
        SettlementTransaction filter = new SettlementTransaction();
        SettlementTransaction transaction = settlementTransactionService.getWithDetails(filter, recordId,withBins);
        return transaction != null ?
                ResponseObject.FETCHED_SUCCESS(transaction, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
