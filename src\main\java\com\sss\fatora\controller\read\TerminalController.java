package com.sss.fatora.controller.read;

import com.sss.fatora.domain.read.TerminalVW;
import com.sss.fatora.domain.read.TransactionDataVW;
import com.sss.fatora.service.read.TerminalService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Optional;

@RestController
@RequestMapping("/terminal")
public class TerminalController {

    @Autowired
    TerminalService terminalService;


   /* @PostMapping(value = "/search")
    public ResponseObject getTerminals(@ParameterName(value = "filter", required = false) TerminalVW terminal,
                                          @ParameterName(value = "fromDate", required = false) Long fromUDate,
                                          @ParameterName(value = "toDate", required = false) Long toUDate,
                                          @ParameterName(value = "pagination", required = false) Pagination pagination,
                                          @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator) {
        if (terminal == null)
            terminal = new TerminalVW();
        Page<TerminalVW> terminals = terminalService.mainSearch(terminal, fromUDate, toUDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", terminals != null ? terminals.getTotalElements() : 0);
        return ResponseObject.FETCHED_SUCCESS(terminals.getContent(), extra);
    }*/
}
