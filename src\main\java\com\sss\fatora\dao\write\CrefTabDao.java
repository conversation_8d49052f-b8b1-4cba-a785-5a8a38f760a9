package com.sss.fatora.dao.write;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.write.CrefTab;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CrefTabDao extends GenericDao<CrefTab, Long> {

    @Modifying
    @Query("UPDATE CrefTab SET pinCnt = 0 WHERE crefNo =:cardNo " +
            "AND expDate =:expDt")
    Integer resetPin(@Param("cardNo") String cardNo, @Param("expDt") Long expDate);

    @Modifying
    @Query("UPDATE CrefTab CT SET CT.cdCapt =:status WHERE CT.crefNo =:cardNo " +
            "AND CT.expDate =:expDt")
    Integer changeStatus(@Param("status") Integer status,
                         @Param("cardNo") String cardNo,
                         @Param("expDt") Long expDate);


    @Query("SELECT CT FROM CrefTab CT WHERE CT.crefNo =:cardNo " +
            "AND CT.expDate =:expDt")
    CrefTab getCrefTabByCrefNo(@Param("cardNo") String cardNo, @Param("expDt") Long expDate);


}
