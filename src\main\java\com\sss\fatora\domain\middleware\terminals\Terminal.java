package com.sss.fatora.domain.middleware.terminals;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class Terminal {

    private String bank;
    private String terminalNumber;
    private String merchantName;
    private String mcc;
    private String mccDesc;
    private String agentNumber;
    private String agentShortDesc;
    private String terminalGroup;
    private String serialNumber;
    private String merchantNumber;
    private String lastOnline;
    private String lastReconciliation;
    private String accountNumber;
    private String feePercentage;
    private String country;
    private String region;
    private String street;
    private String house;
    private String remoteAddress;
    private String localPort;
    private Integer luno;
    private Integer pid;
    private Integer cassette1;
    private Integer cassette2;
    private Integer cassette3;
    private Integer cassette4;
    private String latitude;
    private String longitude;
    private String customerNumber;
    private String accountType;
    private String terminalType;
}
