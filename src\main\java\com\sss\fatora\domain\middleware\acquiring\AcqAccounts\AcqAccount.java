package com.sss.fatora.domain.middleware.acquiring.AcqAccounts;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AcqAccount {
    private String customerNumber;
    private String accountNumber;
    private String accountType;
    private String accountTypeDesc;
    private String currency;
    private String status;
}
