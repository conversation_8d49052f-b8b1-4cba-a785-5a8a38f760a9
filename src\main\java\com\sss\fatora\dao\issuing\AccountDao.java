package com.sss.fatora.dao.issuing;

import com.sss.fatora.domain.issuing.Account;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AccountDao extends JpaRepository<Account,Long> {

    @Query("SELECT A FROM Account A " +
            "WHERE A.customerId =:customerId")
    List<Account> getAccountByCustomerId(@Param("customerId")Long customerId);

    @Query("SELECT A FROM Account A " +
            "WHERE A.customerId =:customerId and A.status =:status")
    List<Account> getActiveAccountByCustomerId(@Param("customerId")Long customerId,@Param("status")String status);

}
