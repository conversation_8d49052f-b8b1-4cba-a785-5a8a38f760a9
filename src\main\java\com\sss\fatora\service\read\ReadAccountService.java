package com.sss.fatora.service.read;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.sss.fatora.dao.read.AccountVWDao;
import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.AccountObject;
import com.sss.fatora.domain.converter.Application;
import com.sss.fatora.domain.issuing.XmlIssueError;
import com.sss.fatora.domain.local.ActionRequest;
import com.sss.fatora.domain.local.SoapResponse;
import com.sss.fatora.domain.read.AccountBo;
import com.sss.fatora.domain.read.AccountVW;
import com.sss.fatora.domain.read.CardsBo;
import com.sss.fatora.domain.read.DTO.AccountDTO;
import com.sss.fatora.domain.read.DTO.ActionResponse;
import com.sss.fatora.domain.read.DTO.Payment.PaymentRequest;
import com.sss.fatora.domain.read.DTO.Payment.PaymentResponse;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.converter.ApplicationService;
import com.sss.fatora.service.generic.GenericReadService;
import com.sss.fatora.service.local.ActionRequestService;
import com.sss.fatora.service.local.LocalApplicationService;
import com.sss.fatora.utils.constants.ActionStatus;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.log.*;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Transactional("readingDBTransactionManager")
public class ReadAccountService extends GenericReadService<AccountVWDao, AccountVW, Long> {
    final ApplicationService applicationConverterService;
    final ReadCardService readCardService;
    final CardsBoService cardsBoService;
    final AccountBoService accountBoService;
    final LocalApplicationService localApplicationService;
    final MessageSource messageSource;
    final ProductsService productsService;


    public ReadAccountService(ApplicationService applicationConverterService, ReadCardService readCardService,
                              LocalApplicationService localApplicationService,
                              CardsBoService cardsBoService,
                              AccountBoService accountBoService,
                              MessageSource messageSource,
                              ProductsService productsService, Environment environment, RestTemplate restTemplate, ActionRequestService actionRequestService) {
        this.applicationConverterService = applicationConverterService;
        this.readCardService = readCardService;
        this.localApplicationService = localApplicationService;
        this.cardsBoService = cardsBoService;
        this.accountBoService = accountBoService;
        this.messageSource = messageSource;
        this.productsService = productsService;
        this.environment = environment;
        this.restTemplate = restTemplate;
        this.actionRequestService = actionRequestService;
    }

    /**
     * @param cardNo This Parameter Represent The Number Of The Card
     *               <p>
     *               This Function Get All Accounts By Card Number
     */

    public List<AccountVW> getAccountsByCardNumber(String cardNo) {
        try {
            List<AccountVW> accountList = dao.getAccountsByCardNumber(cardNo);
            if (accountList == null)
                return new ArrayList<>();
            return accountList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @param accounts This Parameter Are A List Of Accounts To Be Modified
     *                 <p>
     *                 This Function Add 14 And Zeros To Every Account
     */

    private List<Account> getSGBAccounts(List<Account> accounts) {
        accounts.forEach(account -> {
            StringBuilder sgbAccount = new StringBuilder();
            sgbAccount.append("14");
            for (int i = 0; i < 24 - account.getAccount_number().length(); ++i) {
                sgbAccount.append("0");
            }
            sgbAccount.append(account.getAccount_number());
            account.setAccount_number(sgbAccount.toString());
        });
        return accounts;
    }

    private boolean checkSgbAccount(List<Account> accounts) {
        for (Account account : accounts) {
            if (account.getAccount_number().length() > 21) {
                return true;
            }
        }
        return false;
    }

    final Environment environment;
    final RestTemplate restTemplate;
    final ActionRequestService actionRequestService;

    @Log(actionType = ActionType.MANAGE_ACCOUNTS)
    public ResponseEntity<ActionResponse> manageAccounts(@LogCardNumber String cardNumber, AccountDTO accountDTO,
                                                         Long actionRequestId) {
        try {

            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("manageAccountsApi");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<AccountDTO> request = new HttpEntity<>(accountDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class, new Object());
            System.out.println("request : "+ request);
            System.out.println("*****************************************************************");
            System.out.println("response : "+ response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    XmlIssueError result = xmlMapper.readValue(response.getBody().getError(), XmlIssueError.class);
                    response.getBody().setError(result.getFaultString());
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId,ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }


    private final Integer RESET_PIN_COUNT_TransactionType = 748;
//    private final Integer CHANGE_CARD_STATUS_TransactionType = 782;
    private final Integer CHANGE_CARD_STATUS_TransactionType = 672;
    private final Integer VALIDATE_CARD_TransactionType = 782;
//    private final String TERMINAL_ID_FIXED_VALUE = "14740001";

    @Log(actionType = ActionType.CARD_RESET)
    public ResponseEntity<ActionResponse> resetPINCount(Long actionRequestId, @LogCardNumber String cardNumber,
                                                        @LogOldValue String oldStatus, @LogStatus String newStatus) {
        String transIdDate = String.valueOf(new Date().getTime());
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setCardNumber(cardNumber);
        paymentRequest.setDate(getFormattedDate());
        paymentRequest.setTerminalId(environment.getProperty("TERMINAL_ID_FIXED_VALUE"));
        paymentRequest.setTransactionId(transIdDate.substring(transIdDate.length()-6, transIdDate.length()));
        paymentRequest.setTransactionType(RESET_PIN_COUNT_TransactionType);
        return requestPaymentApi(actionRequestId, paymentRequest);
    }

    @Log(actionType = ActionType.CHANGE_STATUS)
    public ResponseEntity<ActionResponse> changeStatus(Long actionRequestId, @LogCardNumber String cardNumber, Integer
            status,@LogOldValue String oldStatus, @LogStatus String newStatus) {
        String transIdDate = String.valueOf(new Date().getTime());
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setCardStatus(status);
        paymentRequest.setCardNumber(cardNumber);
        paymentRequest.setDate(getFormattedDate());
        paymentRequest.setTerminalId(environment.getProperty("TERMINAL_ID_FIXED_VALUE"));
        paymentRequest.setTransactionId(transIdDate.substring(transIdDate.length()-6, transIdDate.length()));
        paymentRequest.setTransactionType(CHANGE_CARD_STATUS_TransactionType);
        return requestPaymentApi(actionRequestId, paymentRequest);
    }

    @Log(actionType = ActionType.VALIDATE_CARD)
    public ResponseEntity<ActionResponse> validateCard(Long actionRequestId, @LogCardNumber String cardNumber,@LogOldValue String oldStatus, @LogStatus String newStatus) {
        String transIdDate = String.valueOf(new Date().getTime());
        PaymentRequest paymentRequest = new PaymentRequest();
//        paymentRequest.setCardStatus(status);
        paymentRequest.setCardNumber(cardNumber);
        paymentRequest.setDate(getFormattedDate());
        paymentRequest.setTerminalId(environment.getProperty("TERMINAL_ID_FIXED_VALUE"));
        paymentRequest.setTransactionId(transIdDate.substring(transIdDate.length()-6, transIdDate.length()));
        paymentRequest.setTransactionType(VALIDATE_CARD_TransactionType);
        return requestPaymentApi(actionRequestId, paymentRequest);
    }

    private ResponseEntity<ActionResponse> requestPaymentApi(Long actionRequestId, PaymentRequest paymentRequest) {

        try {
            String url = environment.getProperty("FatoraPaymentServiceUrl") + environment.getProperty("payment");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<PaymentRequest> request = new HttpEntity<>(paymentRequest, headers);
            ResponseEntity<PaymentResponse> paymentResponse = restTemplate.postForEntity(url, request, PaymentResponse.class, new Object());
            ResponseEntity<ActionResponse> response = null;
            if (paymentResponse != null) {
                ActionResponse actionResponse = new ActionResponse();
                if (paymentResponse.getBody().getResponseCode().equalsIgnoreCase("00")) {
                    actionResponse.setSuccess(1);
                    actionResponse.setError(paymentResponse.getBody().getResponseDescription());
                    response = ResponseEntity.status(200).body(actionResponse);
                } else {
                    actionResponse.setSuccess(0);
                    actionResponse.setError(paymentResponse.getBody().getResponseDescription());
                    response = ResponseEntity.status(200).body(actionResponse);
                    actionRequestService.saveErrorMessage(actionRequestId, actionResponse.getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());


            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId,ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    private String getFormattedDate() {
        LocalDateTime parse = LocalDateTime.parse(new Date().toString(), DateTimeFormatter.ofPattern("EEE MMM dd " +
                "HH:mm:ss zzz " +
                "yyyy"));
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String format = parse.format(dateTimeFormatter);
        return format;
    }


}
