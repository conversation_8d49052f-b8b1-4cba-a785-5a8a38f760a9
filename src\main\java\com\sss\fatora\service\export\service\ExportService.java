package com.sss.fatora.service.export.service;

import com.sss.fatora.dao.specification.SpecificationsBuilder;
import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.domain.generic.GenericDomain;
import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.domain.local.Log;
import com.sss.fatora.domain.local.application.IssueApplication;
import com.sss.fatora.service.export.BatchConfiguration;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.log.Loggable;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.PaginationService;
import com.sss.fatora.utils.service.RefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.Transient;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

@Service
//@Transactional("readingDBTransactionManager")
public class ExportService {
    @Autowired
    PaginationService paginationService;
    @Autowired
    private RefService refService;
    @Autowired
    @Qualifier("readingDBEntityManagerFactory")
    private EntityManager readingEntityManager;

    @Autowired
    @Qualifier("localDBEntityManagerFactory")
    private EntityManager localEntityManager;

    @Autowired
    @Qualifier("settlementDBEntityManagerFactory")
    private EntityManager settlementEntityManager;

    @Autowired
    @Qualifier("issuingDBEntityManagerFactory")
    private EntityManager issuingEntityManager;

    @Autowired
    BatchConfiguration batchConfiguration;

    /**
     * This Function Add Joins To The Query If Needed And Add Constrains
     * */
    private SpecificationsBuilder rec(SpecificationsBuilder builder, Object domain, String parent, Map<String, Operator> filterOperator) throws IntrospectionException, InvocationTargetException, IllegalAccessException, NoSuchFieldException {
        for (PropertyDescriptor pd : Introspector.getBeanInfo(domain.getClass()).getPropertyDescriptors()) {
            Class<?> classType = pd.getPropertyType();
            Object propertyValue = pd.getReadMethod().invoke(domain);
            Boolean isTransient = false;
            if (!classType.equals(Class.class))
                isTransient = pd.getReadMethod().isAnnotationPresent(Transient.class);

            if (propertyValue != null && !classType.equals(Class.class) && !isTransient) {
                if (propertyValue instanceof GenericDomain) {
                    builder.join(" LEFT JOIN " + parent + "." + pd.getName() + " AS " + pd.getName() + " ");
                    rec(builder, propertyValue, pd.getName(), filterOperator);
                } else if (propertyValue instanceof Set) {
                    builder.join(" LEFT JOIN Fetch " + parent + "." + pd.getName() + " AS " + pd.getName() + " ");
                    if (!((Set) propertyValue).isEmpty())
                        rec(builder, ((Set) propertyValue).iterator().next(), pd.getName(), filterOperator);
                } else {
                    builder.add(parent + "." + pd.getName(), propertyValue, filterOperator.get(pd.getName()));
                }
            }
        }
        return builder;
    }

    /**
     * @param filter This Parameter Have Filters To Be Used In The Where Clause Of The Query
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection This Is A List Of Columns For Select Statement In Search Query
     *
     * This Function Build The Final Query As A String And Select The Correct Db ( EntityManager )
     * Where The Query Will Be Executed And Then Return The Result
     * */
    public <Domain> Page<Domain> dynamicSearch(Domain filter, Pagination pagination, String additionalConstraints, Map<String, Operator> filterOperator, List<String> projection, String filePath, List<String> headers, List<String> columns, Function... filterFunctions) throws Exception {
        SpecificationsBuilder builder = new SpecificationsBuilder(filter.getClass());
        SpecificationsBuilder specifications = rec(builder, filter, filter.getClass().getSimpleName(), filterOperator);
        ifDomainType(filter, specifications);
        builder.addConstraint(additionalConstraints);
        String buildQuery ;
        Query query;
        Long count;
        Boolean encodeCardNo=true;
        Boolean withPrivileges;
        if(filter instanceof Log || filter instanceof LocalApplication || filter instanceof IssueApplication) {
            buildQuery = specifications.build(pagination, projection);
            withPrivileges = false;
            query= localEntityManager.createQuery(buildQuery);
            count= getCountByQuery(specifications.buildCount(),localEntityManager);
            encodeCardNo=false;
        }
        else if(filter instanceof SettlementTransaction){
            buildQuery = specifications.buildWithoutDistinct(pagination, projection);
            withPrivileges = true;
            query = settlementEntityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCountWithoutDistinct(), settlementEntityManager);
        } else if (filter instanceof Customer) {
            buildQuery = specifications.buildWithoutDistinct(pagination, projection);
            withPrivileges = true;
            query = issuingEntityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCountWithoutDistinct(), issuingEntityManager);
        } else {
            buildQuery = specifications.buildWithoutDistinct(pagination, projection);
            withPrivileges = true;
            query = readingEntityManager.createQuery(buildQuery);
            count= getCountByQuery(specifications.buildCountWithoutDistinct(),readingEntityManager);
        }

        Pagination usedPagination = this.getPagination(pagination, count);
        if (pagination != null) {
            query.setMaxResults(usedPagination.getSize())
                    .setFirstResult(usedPagination.getStart() * usedPagination.getSize());
            if (usedPagination.getSize()==0)
                pagination.setSize(1);
        }
        // to put search query in the filter for Log Aspect
        if (filter instanceof Loggable) {
            ((Loggable) filter).setQuery(buildQuery);
        }
        /*List<Domain> res = query.getResultList();
        Page<Domain> result = new PageImpl<Domain>(res, PageRequest.of(usedPagination.getStart(),
                usedPagination.getSize()), count);*/
        batchConfiguration.run(buildQuery, filter.getClass(), pagination, filePath, headers, columns, withPrivileges,encodeCardNo);
        return null;
    }

    /**
     * This Function Add Join Statements To The Query By Type Of Domain
     * */
    private <Domain> void ifDomainType(Domain filter, SpecificationsBuilder specifications) {
        if (filter.getClass().getSimpleName().equalsIgnoreCase("SettlementTransaction")) {
            specifications.join(" INNER JOIN SettlementBank AS Bank2 ON SettlementTransaction.issuer = Bank2.code " +
                    "INNER JOIN SettlementBank AS Bank1 ON SettlementTransaction.acquirer = Bank1.code " +
                    "INNER JOIN SettlementChannel AS Channel ON SettlementTransaction.channel = Channel.channel ");
        }

        if (filter.getClass().getSimpleName().equalsIgnoreCase("CardDataVW")) {
            specifications.join(" INNER JOIN CardStatusVW AS CardStatusVW ON CardDataVW.status = CardStatusVW.cdStat " +
                    "INNER JOIN Agents AS Agents ON CardDataVW.agentCode = Agents.agentNumber "
            );
        }

        if (filter.getClass().getSimpleName().equalsIgnoreCase("Customer")) {
                specifications.join(" LEFT JOIN IdType AS IdType ON Customer.customerIdType = IdType.code " +
                        " LEFT JOIN CountryCodes AS CountryCodes ON Customer.customerCountry = CountryCodes.code " +
                        " LEFT JOIN CountryCodes AS NationalityCodes ON Customer.customerNationality = NationalityCodes.code ");
            }


    }


    /**
     * @param queryStr This Parameter Contains The Query To Be Executed
     *
     * This Function Create The Query In The Specified Entity Manager And Then Get A Count
     * Of The Result Of The Query
     * */
    private Long getCountByQuery(String queryStr, EntityManager entityManager) {
        Query query = entityManager.createQuery(queryStr);
        return (Long) query.getSingleResult();
    }

    /**
     * This Generic Function Add Pagination If Needed To The Dynamic Search
     */
    private Pagination getPagination(Pagination pagination, Long count) {
        if (pagination == null) {
            pagination = new Pagination();
        }
        if (pagination.getSize() == null) pagination.setSize(Math.toIntExact(count));
        if (pagination.getStart() == null) pagination.setStart(0);
        return pagination;
    }
}
