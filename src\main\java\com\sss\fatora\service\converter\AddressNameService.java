package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.AccountObject;
import com.sss.fatora.domain.converter.Address;
import com.sss.fatora.domain.converter.AddressName;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class AddressNameService extends GenericConverterService<AddressName> {

    public AddressName prepareAddressNameObject(AddressName addressName, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(addressName,excelIndexes,row);
       return addressName;
    }

    public AddressName prepareAddressNameObjectFromPanel(AddressName addressName, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(addressName,fixedValues);
        return addressName;
    }
}
