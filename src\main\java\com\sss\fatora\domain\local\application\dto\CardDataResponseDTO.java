package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CardDataResponseDTO {

    @JsonProperty(value = "Application_Number")
    private String applicationNumber;

    @JsonProperty(value = "Application_Id")
    private String applicationId;

    @JsonProperty(value = "Application_Status")
    private String applicationStatus;

    @JsonProperty(value = "Contract_Number")
    private String contractNumber;

    @JsonProperty(value = "Card_Number")
    private String cardNumber;

    @JsonProperty(value = "Expiry_Date")
    private String expiryDate;

    @JsonProperty(value = "Cardholder_Number")
    private String cardholderNumber;

    @JsonProperty(value = "Cardholder_Name")
    private String cardholderName;

    @JsonProperty(value = "Mobile_Number")
    private String mobilNumber;
}
