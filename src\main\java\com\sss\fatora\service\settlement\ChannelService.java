package com.sss.fatora.service.settlement;

import com.sss.fatora.dao.settlement.ChannelDao;
import com.sss.fatora.domain.settlement.SettlementChannel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("settlementDBTransactionManager")
public class ChannelService {
    final ChannelDao channelDao;
    public ChannelService(ChannelDao channelDao){
        this.channelDao = channelDao;
    }

    /**
     * This Function Get All Channels So They Can Be Sent To Front End By Global Data Api
     * */

    public List<SettlementChannel> getAllChannels() {
        try {
            return channelDao.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }
}
