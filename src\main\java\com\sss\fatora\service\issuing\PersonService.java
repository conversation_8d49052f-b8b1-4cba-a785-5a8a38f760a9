package com.sss.fatora.service.issuing;


import com.sss.fatora.dao.issuing.PersonDao;
import com.sss.fatora.domain.issuing.Person;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class PersonService {

        final PersonDao personDao;

    public PersonService(PersonDao personDao){
        this.personDao = personDao;
    }

    /**
     * @param idType This Parameter Represent The id Of The Passport Or NationalId That Is Founded In The Table
     * @param idNumber This Parameter Represent The Number Of The Password Or The NationalId
     * @param bankPrefix Is The Prefix Of A Specific Bank Like Syrian Gulf Bank Is SGB
     *
     * This Function Get A Person By IdType And IdNumber With Prefix If Not Found It Search Without Prefix
     * */
    public Map<String, Object> getPersonByTypeAndNumber(String idType, String idNumber, String bankPrefix) {
        Map<String, Object> map = new HashMap<>();
        Person requestedPerson = personDao.getPersonByTypeAndNumber(idType,bankPrefix + idNumber);
        if (requestedPerson == null){
            requestedPerson = personDao.getPersonByTypeAndNumber(idType,idNumber);
        }
        map.put("Person", requestedPerson);
        return map;
    }

    /**
     * @param personId This Parameter Is The Person Id
     *
     * This Function Get A Person By It's Id
     * */

    public Person getPersonById(Long personId) {
        Person requestedPerson = personDao.getByPersonId(personId);
        return requestedPerson;
    }
}
