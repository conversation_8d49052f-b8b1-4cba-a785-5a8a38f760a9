package com.sss.fatora.domain.local.application;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalPerson {

    Long id;
    String idType;
    String idNumber;
    String firstName;
    String lastName;
    String fatherName;
    String motherName;
    String gender;
    Date dateOfBirth;
    String placeOfBirth;

}
