package com.sss.fatora.service.export.service.middleware;

import com.sss.fatora.domain.middleware.Merchants.Merchant;
import com.sss.fatora.domain.middleware.device.Device;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.read.FatoraBankService;
import com.sss.fatora.utils.service.ContentUtilService;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ExportMerchantService {
    private final ConfigService configService;
    final ContentUtilService contentUtilService;
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private final FatoraBankService fatoraBankService;

    public ExportMerchantService(ConfigService configService, ContentUtilService contentUtilService, FatoraBankService fatoraBankService) {
        this.configService = configService;
        this.contentUtilService = contentUtilService;
        this.fatoraBankService = fatoraBankService;
    }

    void initWorkbook() {
        workbook = new SXSSFWorkbook();
        this.sheet = workbook.createSheet("Merchants");

    }
   private List<String> projection;

    private void writeHeaderLine(List<String> headers) {
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setBold(true);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);
        style.setBorderBottom(BorderStyle.THICK);
        style.setBorderTop(BorderStyle.THICK);
        int i = 0;
        projection = new ArrayList<>(headers);
        for (String iterator : projection) {
            createCell(row, i++, iterator, style);
        }
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        sheet.setColumnWidth(columnCount, 8000);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue((String) value);
        }
        cell.setCellStyle(style);
    }

    private void writeDataLines(List<Merchant> merchantList, List<String> fields) {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);
        style.setFont(font);
        for (Merchant merchant : merchantList) {
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            String bankName = fatoraBankService.getBankNameByCode(merchant.getBank()) != null ?
                    fatoraBankService.getBankNameByCode(merchant.getBank()) : merchant.getBank();
            merchant.setBank(bankName);
            setupRowByType(style, row, columnCount, fields, merchant);
        }
        setColumnsWidth();
    }

    private void setColumnsWidth() {
        if (projection.contains("Account Number"))
            sheet.setColumnWidth(projection.indexOf("Account Number"), 256 * 47);
        if (projection.contains("Merchant"))
            sheet.setColumnWidth(projection.indexOf("Merchant"), 256 * 47);
        if (projection.contains("MCC description"))
            sheet.setColumnWidth(projection.indexOf("MCC description"), 256 * 41);
        if (projection.contains("Street"))
            sheet.setColumnWidth(projection.indexOf("Street"), 256 * 47);
        if (projection.contains("Region"))
            sheet.setColumnWidth(projection.indexOf("Region"), 256 * 47);
        if (projection.contains("House"))
            sheet.setColumnWidth(projection.indexOf("House"), 256 * 47);

    }
    private void setupRowByType(CellStyle style, Row row, Integer columnCount, List<String> fields, Merchant merchant) {
        for (String field : fields) {
            Object value = null;
            try {
                Field declaredField = merchant.getClass().getDeclaredField(field);
                declaredField.setAccessible(true);
                value = declaredField.get(merchant);

            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
            createCell(row, columnCount++, value, style);
        }
    }

    public void export(List<Merchant> merchantList, HttpServletResponse response, List<String> headers, List<String> fields) throws IOException {
        initWorkbook();
        writeHeaderLine(headers);
        writeDataLines(merchantList, fields);
        File file = contentUtilService.makeExcelFile();
        FileOutputStream fileOutputStream = new FileOutputStream(file.getPath());
        workbook.write(fileOutputStream);
        workbook.dispose();
        workbook = null;
        fileOutputStream.flush();
        fileOutputStream.close();
        byte[] cards = FileUtils.readFileToByteArray(file);
        response.setContentLength(cards.length);
        response.getOutputStream().write(cards);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

}
