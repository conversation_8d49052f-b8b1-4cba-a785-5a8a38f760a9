package com.sss.fatora.domain.middleware.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class Device {
    @JsonProperty
    private String bank;
    @JsonProperty
    private String custodian;
    @JsonProperty
    private String owner;
    @JsonProperty
    private String status;
    @JsonProperty
    private String serialNumber;
    @JsonProperty
    private String softwareVersion;
    @JsonProperty
    private String gsm;
    @JsonProperty
    private String lastOnline;
    @JsonProperty
    private String lastReconciliation;
    @JsonProperty
    private String merchantName;
    @JsonProperty
    private String terminalId;
    @JsonProperty
    private String model;
    @JsonProperty
    private String shipment;
    @JsonProperty
    private String imei;
    @JsonProperty
    private String meid;
    @JsonProperty
    private String unlocked;
    @JsonProperty
    //SimId
    private String id;
    @JsonProperty
    private String ssid;
    @JsonProperty

    //SIM Details
    private String gsmProvider;
    @JsonProperty
    private String apn;
    @JsonProperty
    private String ipAddress;
    @JsonProperty
    private String simId;
    @JsonProperty
    private String puk;
    @JsonProperty
    private String puk2;
    @JsonProperty
    private String pin;

}
