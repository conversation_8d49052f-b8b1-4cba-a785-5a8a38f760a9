package com.sss.fatora.service.read;


import com.sss.fatora.dao.read.CurrencyCodesDao;
import com.sss.fatora.domain.read.CurrencyCodes;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Transactional("readingDBTransactionManager")
public class CurrencyCodesService extends GenericReadService<CurrencyCodesDao, CurrencyCodes, Long> {

}
