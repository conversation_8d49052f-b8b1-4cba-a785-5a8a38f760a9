package com.sss.fatora.controller.middleware.devices;

import com.sss.fatora.domain.middleware.device.Sims.GetSimsRequest;
import com.sss.fatora.domain.middleware.device.Sims.Sim;
import com.sss.fatora.domain.middleware.response.SearchMiddlewareResponse;
import com.sss.fatora.service.middleware.devices.SimService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sims")
public class SimController {
    private final SimService simService;

    public SimController(SimService simService ) {
        this.simService = simService;
    }

    @PostMapping("/filter")
    public ResponseObject getSimsWithFilter(@RequestBody GetSimsRequest request){
        SearchMiddlewareResponse<Sim> sims = simService.search(request);
        return sims != null ?
                ResponseObject.FETCHED_SUCCESS(sims, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
