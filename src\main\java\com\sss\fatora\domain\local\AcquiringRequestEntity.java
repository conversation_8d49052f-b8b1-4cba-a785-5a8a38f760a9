// package com.sss.fatora.domain.local;

// import com.sss.fatora.dao.generic.GenericDao;
// import com.sss.fatora.domain.generic.GenericDomain;
// import com.sss.fatora.utils.constants.UserRequestStatus;
// import lombok.AllArgsConstructor;
// import lombok.Getter;
// import lombok.NoArgsConstructor;
// import lombok.Setter;

// import javax.persistence.*;
// import java.util.Date;

// @NoArgsConstructor
// @AllArgsConstructor
// @Setter
// @Getter
// @Entity
// @Table(name = "Acquiring_Requests")
// public class AcquiringRequestEntity extends GenericDomain {
//     @Id
//     @GeneratedValue(strategy = GenerationType.IDENTITY)
//     private Long id;

//     @Column(name = "RequestStatus")
//     private String requestStatus;

//     @Column(name = "ActionType")
//     private String action;

//     @Column(name = "EntityName")
//     private String entityName;

//     @Column(name = "EntityId")
//     private String entityId;

//     @Column(name = "CreatorName")
//     private String userName;

//     @Column(name = "ApprovalDate")
//     private Date approvalDate;

//     @Column(name = "Archived")
//     private Boolean archived;

//     @Column(name = "NewValue", length = 5000)
//     private String newValue;

//     @Column(name = "OldValue")
//     private String oldValue;

//     @Column(name = "Description")
//     private String description;


//     @Override
//     public void setDatePrePersist() {
//         this.setRequestStatus(UserRequestStatus.PENDING.getType());
//     }

//     @Override
//     protected <T extends GenericDao> Class<T> defineDao() {
//         return null;
//     }
// }
