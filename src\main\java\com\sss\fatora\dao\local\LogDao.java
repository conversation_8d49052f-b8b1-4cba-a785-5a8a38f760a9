package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.Log;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface LogDao extends GenericDao<Log,Long> {

    @Query("SELECT L FROM Log L " +
            "LEFT JOIN L.bank B " +
            "WHERE B.id=:bankId")
    Page<Log> getExternalAudit(@Param("bankId") Integer bankId, Pageable pagination);


    @Query("SELECT L FROM Log L " +
            "WHERE L.applicationUser.id=:userId")
    List<Log> getLogsByUserId(@Param("userId") Integer userId);
}
