package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.AccountBoDao;
import com.sss.fatora.domain.read.AccountBo;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional("readingDBTransactionManager")
public class AccountBoService extends GenericReadService<AccountBoDao, AccountBo, Long> {

    /**Author Comment: <PERSON>
     * account BO is more update in database */

    /**Author Comment: <PERSON>
     * get POS accounts by card number*/
    public AccountBo getPosAccountByNumber(String cardNo) {

        try {
            return dao.getPosAccountBoByNumber(cardNo);
        } catch (Exception exception) {
            throw exception;
        }
    }

    /**Author Comment: <PERSON>
     * get ATM accounts by card number*/
    public AccountBo getAtmAccountByNumber(String cardNo) {

        try {
            return dao.getAtmAccountBoByNumber(cardNo);
        } catch (Exception exception) {
            throw exception;
        }
    }
    /**Author Comment: <PERSON>
     * get account by account number*/
    public AccountBo getAccountBoByNumber(String accNo) {

        try {
            return dao.getAccountBoByNumber(accNo);
        } catch (Exception exception) {
            throw exception;
        }
    }

    /**Author Comment: Maria Suleiman
     * get account by account number and card number*/
    public AccountBo getAccountBoByNumberAndCard(String accNo, String cardNo) {

        try {
            return dao.getAccountBoByNumberAndCard(accNo, cardNo);
        } catch (Exception exception) {
            throw exception;
        }
    }
}
