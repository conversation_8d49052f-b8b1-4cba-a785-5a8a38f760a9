package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.TerminalDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.read.TerminalVW;
import com.sss.fatora.domain.read.TransactionDataVW;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Transactional("readingDBTransactionManager")
public class TerminalService extends GenericReadService<TerminalDao, TerminalVW, Long> {

   /* public Page<TerminalVW> search(TerminalVW terminal, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page<TerminalVW> terminals;
        String additionalConstraint = "";
        String oracleFormat = "yyyy-MM-dd HH24:MI:ss";
        String javaFormat = "yyyy-MM-dd HH:mm:ss";
        DateFormat f = new SimpleDateFormat(javaFormat);
        String bins = "";
        String date1 = "";
        String date2 = "";
        String searchLimitConstraint = "";


        if (terminal == null)
            terminal = new TerminalVW();
        if (fromDate == null)
            fromDate = new Date(*********).getTime();
        if (toDate == null)
            toDate = new Date(System.currentTimeMillis() * 100).getTime();

        if (applicationUser.getBank() != null)
            bins = applicationUser.getBank().getBinCodes().stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));

        if (applicationUser.getMonthlySearchLimit() != null) {
            Date dateLimit = DateUtils.getDateAfterMonthsAdded(new Date(), -1 * applicationUser.getMonthlySearchLimit());
            searchLimitConstraint = " And TransactionDataVW.udate >=" + "to_timestamp('" + f.format(dateLimit) + "','" + oracleFormat + "')";
        }

        date1 = "to_timestamp('" + f.format(fromDate) + "','" + oracleFormat + "')";
        date2 = "to_timestamp('" + f.format(toDate) + "','" + oracleFormat + "')";
        additionalConstraint = "TerminalVW.udate BETWEEN " + date1 + " AND " + date2 + searchLimitConstraint;
        *//*if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            additionalConstraint = "(substring(TransactionDataVW.hpan,0,6) IN (" + bins + ") OR " +
                    "substring(TransactionDataVW.atmId,0,2) = " + bankCode + " ) " +
                    "AND TransactionDataVW.udate BETWEEN " + date1 + " AND " + date2 + searchLimitConstraint;
        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType())) {
            additionalConstraint = "TransactionDataVW.udate BETWEEN " + date1 + " AND " + date2 + searchLimitConstraint;
        }*//*

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("TransactionDataVW.udate");
            pagination.setOrderType("DESC");
        }

        terminals = this.dynamicSearch(terminal
                , pagination
                , additionalConstraint
                , filterOperator
                , projection);

        return terminals;

    }
    @Log(actionType = ActionType.TRANSACTION_SEARCH)
    public Page<TerminalVW> mainSearch(TerminalVW terminal, Long fromUDate, Long toUDate, Pagination pagination, Map<String, Operator> filterOperator) throws IntrospectionException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {
        *//*List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
*//*
        List<String> projection = new ArrayList<>();
        projection= Stream.of("terminalId","type","merchantId","merchant","mainTerminal","bankCode","bank")
                .collect(Collectors.toList());
        Page<TerminalVW> terminals = search(terminal, fromUDate, toUDate, pagination, filterOperator, projection);
        return terminals;
    }*/
}
