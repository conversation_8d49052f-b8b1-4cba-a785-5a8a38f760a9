package com.sss.fatora.domain.middleware.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sss.fatora.domain.middleware.terminals.ResponseError;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class MiddlewareResponse {

    @JsonProperty("targetUrl")
    private String targetUrl;
    @JsonProperty("success")
    private Boolean success;
    @JsonProperty("error")
    private ResponseError error;
    @JsonProperty("unAuthorizedRequest")
    private Boolean unAuthorizedRequest;
    @JsonProperty("__abp")
    private Boolean __abp;
}
