package com.sss.fatora.utils.constants;

public enum PaymentMiddlewareRequestType {
    SEARCH("SEARCH"),
    SEARCH_ATM_Main("SEARCH_ATM_Main"),
    SEARCH_WITH_DETAILS("SEARCH_WITH_DETAILS"),
    GET_HOSTS("GET_HOSTS"),
    GET_SIM_DETAILS("GET_SIM_DETAILS"),
    EXPORT_WITH_DETAILS("EXPORT_WITH_DETAILS"),
    EXPORT_WITHOUT_DETAILS("EXPORT_WITHOUT_DETAILS"),
    EXPORT_POS_WITH_DETAILS("EXPORT_POS_WITH_DETAILS"),
    EXPORT_EPOS_WITH_DETAILS("EXPORT_EPOS_WITH_DETAILS"),
    EXPORT_ATM_WITH_DETAILS("EXPORT_ATM_WITH_DETAILS"),
    GET_TERMINALS_BY_MERCHANT_NUMBER("GET_TERMINALS_BY_MERCHANT_NUMBER");

    private String type;

    PaymentMiddlewareRequestType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
