package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class IssueApplicationDTO {

    @JsonProperty(value = "Application_Number")
    String applicationNumber;

    @JsonProperty(value = "Instant_Card")
    Integer instant;

    @JsonProperty(value = "Card")
    LocalCardDTO card;

    @JsonProperty(value = "Customer")
    LocalCustomerDTO customer;

    @JsonProperty(value = "Accounts")
    List<LocalAccountDTO> accounts;

}
