package com.sss.fatora.service.generic;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.dao.specification.SpecificationsBuilder;
import com.sss.fatora.domain.converter.GenericConverter;
import com.sss.fatora.domain.generic.GenericDomain;
import com.sss.fatora.domain.local.ActionRequest;
import com.sss.fatora.domain.smpp.MessageLog;
import com.sss.fatora.exception.ResourceNotFoundException;
import com.sss.fatora.service.converter.ExcelFileService;
import com.sss.fatora.service.local.ActionRequestService;
import com.sss.fatora.utils.annotation.ExcelProperty;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.PaginationService;
import com.sss.fatora.utils.service.RefService;
import com.sss.fatora.utils.service.UpdateService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.Transient;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

//@Transactional("localDBTransactionManager")
public class GenericService<Dao extends GenericDao, Domain extends GenericDomain, IdClass extends Serializable> {

    @Autowired
    protected Dao dao;
    @Autowired
    PaginationService paginationService;
    @Autowired
    private RefService refService;
    @Autowired
    private ActionRequestService actionRequestService;
    @Autowired
    ExcelFileService excelFileService;
    @Autowired
    private UpdateService<Domain, ? extends Serializable> updateService;

    @Autowired
    @Qualifier("localDBEntityManagerFactory")
    private EntityManager entityManager;

    @Autowired
    @Qualifier("smppDBEntityManagerFactory")
    private EntityManager smppEntityManager;

    public Iterable<Domain> insert(List<Domain> domain) {
        refService.linkChildrenWithParents(domain);
        return ((JpaRepository<Domain, IdClass>) dao).saveAll(domain);
    }

    public Domain merge(Domain domain) throws Exception {
        // refService.linkChildrenWithParents(domain);
        Domain domainToSave = (Domain) refService.persistMethod(domain);
        return ((JpaRepository<Domain, IdClass>) dao).save(domainToSave);
    }

    public Domain mergeWithChildren(Domain domain) throws Exception {
        refService.linkChildrenWithParents(domain);
        // Domain domainToSave = (Domain) refService.persistMethod(domain);
        return ((JpaRepository<Domain, IdClass>) dao).save(domain);
    }

    public Domain getById(IdClass id) {
        Optional<Domain> result = ((JpaRepository<Domain, IdClass>) dao).findById(id);
        return result != null && result.isPresent() ? result.get() : null;
    }

    public Page getAll(Pagination pagination, int... recordStatus) {

        return recordStatus != null
                ? dao.findAllByStatusWithPagination(paginationService.getPagination(pagination), recordStatus)
                : dao.findAll(paginationService.getPagination(pagination));
    }

    public Boolean delete(IdClass id) {
        try {
            dao.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public Domain update(IdClass id, Domain domain) throws Exception {
        Domain domainFromDB = this.getById(id);
        if (domainFromDB == null) {
            throw new ResourceNotFoundException();
        }
        Domain updatedDomain = updateService.update(domain, domainFromDB);
        return ((JpaRepository<Domain, IdClass>) dao).save(updatedDomain);
    }

    /**
     * This Generic Function Add Joins To The Query If Needed And Add Constrains
     */

    private SpecificationsBuilder rec(SpecificationsBuilder builder, Object domain, String parent,
            Map<String, Operator> filterOperator)
            throws IntrospectionException, InvocationTargetException, IllegalAccessException, NoSuchFieldException {
        for (PropertyDescriptor pd : Introspector.getBeanInfo(domain.getClass()).getPropertyDescriptors()) {
            Class<?> classType = pd.getPropertyType();
            Object propertyValue = pd.getReadMethod().invoke(domain);
            Boolean isTransient = false;

            if (!classType.equals(Class.class))
                isTransient = pd.getReadMethod().isAnnotationPresent(Transient.class);

            if (propertyValue != null && !classType.equals(Class.class) && !isTransient) {
                if (propertyValue instanceof GenericDomain) {
                    builder.join(" LEFT JOIN Fetch " + parent + "." + pd.getName() + " AS " + pd.getName() + " ");
                    rec(builder, propertyValue, pd.getName(), filterOperator);
                } else if (propertyValue instanceof Set) {
                    builder.join(" LEFT JOIN Fetch " + parent + "." + pd.getName() + " AS " + pd.getName() + " ");
                    if (!((Set) propertyValue).isEmpty())
                        rec(builder, ((Set) propertyValue).iterator().next(), pd.getName(), filterOperator);
                } else {
                    builder.add(parent + "." + pd.getName(), propertyValue, filterOperator.get(pd.getName()));
                }
            }

        }
        return builder;
    }

    /**
     * @param filter         This Parameter Have Filters To Be Used In The Where
     *                       Clause Of The Query
     * @param filterOperator There Is An Specific Operator For Every Filter ex: = ,
     *                       >
     * @param projection     This Is A List Of Columns For Select Statement In
     *                       Search Query
     *                       <p>
     *                       This Function Build The Final Query As A String And
     *                       Select The Correct Db ( EntityManager )
     *                       Where The Query Will Be Executed And Then Return The
     *                       Result
     */

    public Page<Domain> dynamicSearch(Domain filter, Pagination pagination, String additionalConstraints,
            Map<String, Operator> filterOperator, List<String> projection, Boolean isAcquiring)
            throws IllegalAccessException, IntrospectionException, InvocationTargetException, NoSuchFieldException {
        SpecificationsBuilder builder = new SpecificationsBuilder(filter.getClass());
        SpecificationsBuilder specifications = rec(builder, filter, filter.getClass().getSimpleName(), filterOperator);
        builder.addConstraint(additionalConstraints);
        conditionsForJoin(filter, specifications, isAcquiring);
        String buildQuery = specifications.build(pagination, projection);
        Query query;
        Long count;
        if (filter instanceof MessageLog) {
            query = smppEntityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCount(), smppEntityManager);
        } else {
            query = entityManager.createQuery(buildQuery);
            count = getCountByQuery(specifications.buildCount(), entityManager);
        }

        Pagination usedPagination = this.getPagination(pagination, count);
        if (pagination != null) {
            query.setMaxResults(usedPagination.getSize())
                    .setFirstResult(usedPagination.getStart() * usedPagination.getSize());
        }
        List<Domain> res = query.getResultList();
        Page<Domain> result = new PageImpl<Domain>(res, PageRequest.of(usedPagination.getStart(),
                usedPagination.getSize()), count);
        return result;
    }

    /**
     * This Function Adds Joins To The Query When The Filter Is From A Certain Type
     */

    private <Domain> void conditionsForJoin(Domain filter, SpecificationsBuilder specifications, Boolean isAcquiring) {
        if (isAcquiring) {
            if (filter.getClass().getSimpleName().equalsIgnoreCase("AcquiringActionRequest")) {
                specifications.join(actionRequestService.addAcquiringJoinToQuery());
            }
        } else {
            if (filter.getClass().getSimpleName().equalsIgnoreCase("ActionRequest")) {
                specifications.join(actionRequestService.addJoinToQuery());
            }
        }
    }


    /**
     * @param queryStr This Parameter Contains The Query To Be Executed
     *                 <p>
     *                 This Function Create The Query In The Specified Entity
     *                 Manager And Then Get A Count
     *                 Of The Result Of The Query
     */

    private Long getCountByQuery(String queryStr, EntityManager entityManager) {
        Query query = entityManager.createQuery(queryStr);
        return (Long) query.getSingleResult();
    }

    /**
     * This Function Add Pagination If Needed To The Dynamic Search
     */

    private Pagination getPagination(Pagination pagination, Long count) {
        if (pagination == null) {
            pagination = new Pagination();
        }
        if (pagination.getSize() == null)
            pagination.setSize(Math.toIntExact(count));
        if (pagination.getStart() == null)
            pagination.setStart(0);
        return pagination;
    }

    public Domain getPropertyValueByExcel(Domain genericConverter, Map<String, Integer> excelIndexes, Row row)
            throws IllegalAccessException {
        Boolean isEmpty = true;
        for (Field field : genericConverter.getClass().getDeclaredFields()) {
            // if the field is a list
            if (List.class.getSimpleName().equals(field.getType().getSimpleName())) {
                continue;
            }
            if (GenericConverter.class.getSimpleName().equals(field.getType().getSuperclass().getSimpleName())) {
                continue;
            } else {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (excelProperty != null) {
                    field.setAccessible(true);
                    // Cell targetCell =
                    // row.getCell(excelIndexes.get(excelProperty.name()),Row.RETURN_BLANK_AS_NULL);
                    Cell targetCell = row.getCell(excelIndexes.get(excelProperty.name()));

                    if (targetCell != null) {
                        Object targetCellValue = excelFileService.getCellValue(targetCell);
                        if (targetCellValue != null) {
                            field.set(genericConverter, targetCellValue.toString());
                            isEmpty = false;
                        } else {
                            field.set(genericConverter, null);
                        }
                    } else
                        continue;
                } else
                    continue;
            }
        }
        if (isEmpty) {
            return null;
        } else
            return genericConverter;
    }
}
