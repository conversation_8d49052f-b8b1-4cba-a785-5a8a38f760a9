package com.sss.fatora.domain.issuing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.domain.generic.GenericDomain;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_ACCOUNTS")
public class Account {
    @Id
    @Column(name = "ID")
    Long id;

    @Column(name = "ACCOUNT_TYPE")
    String accountType;

    @Column(name = "ACCOUNT_NUMBER")
    String accountNumber;

    @Column(name = "CURRENCY")
    String currency;

    @Column(name = "CUSTOMER_ID")
    Long customerId;

    @Column(name = "STATUS")
    String status;

    //In PROD  
    @Column(name = "STATUS_DESC")
    // @Column(name = "NAME")
    String statusName;
}
