package com.sss.fatora.service.read;

import com.sss.fatora.service.settlement.ChannelService;
import com.sss.fatora.service.local.ApplicationUserService;
import com.sss.fatora.service.local.ConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;


@Service
@Transactional("readingDBTransactionManager")
public class GlobalService {
    final CardStatusService cardStatusService;
    final ApplicationUserService applicationUserService;
    final ResponseCodeService responseCodeService;
    final FatoraBankService fatoraBankService;
    final TransTypeService transTypeService;
    final ProductsService productsService;
    final ConfigService configService;
    final ChannelService channelService;

    public GlobalService(CardStatusService cardStatusService, ApplicationUserService applicationUserService, ResponseCodeService responseCodeService, FatoraBankService fatoraBankService, TransTypeService transTypeService, ProductsService productsService, ConfigService configService, ChannelService channelService) {
        this.cardStatusService = cardStatusService;
        this.applicationUserService = applicationUserService;
        this.responseCodeService = responseCodeService;
        this.fatoraBankService = fatoraBankService;
        this.transTypeService = transTypeService;
        this.productsService = productsService;
        this.configService = configService;
        this.channelService =channelService;
    }


    /**
     * This Function Get Some Data From Multiple Places And Return It To The Front-End
     * */
    public Map getGlobalData() {
        Map<String, Object> map = new HashMap<>();
        map.put("cardStatus", cardStatusService.getCardsStatus());
        map.put("users", applicationUserService.getAllUsers());
        map.put("channels", channelService.getAllChannels());
        map.put("responseCodes", responseCodeService.getAllResponseCodes());
        map.put("banks", fatoraBankService.getUserAllowableBanks());
        map.put("transTypes", transTypeService.getAllTransTypes());
        map.put("products", productsService.getAllProductsByBankCode());
        map.put("actionType", configService.getActionType());
        map.put("actionTypeForRequest", configService.getActionTypeForRequests());
        map.put("cardHolderAuth", configService.getCardHolderAuth());
        map.put("cardInputMode", configService.getCardInputMode());
        map.put("acquiringActionTypeForRequest", configService.getAcquiringActionType());
        return map;
    }


}
