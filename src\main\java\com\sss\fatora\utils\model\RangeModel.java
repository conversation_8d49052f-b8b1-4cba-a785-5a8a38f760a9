package com.sss.fatora.utils.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RangeModel implements Serializable {

    private Object fromValue;
    private Object toValue;
    private String propertyName;

    public RangeModel() {
    }

    public RangeModel(Object fromValue, Object toValue) {
        this.fromValue = fromValue;
        this.toValue = toValue;
    }

    public Object getFromValue() {
        return fromValue;
    }

    public void setFromValue(Object fromValue) {
        this.fromValue = fromValue;
    }

    public Object getToValue() {
        return toValue;
    }

    public void setToValue(Object toValue) {
        this.toValue = toValue;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }
}
