package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "commun_method","commun_address"})
public class ContactData extends GenericConverter{

    public ContactData(String commun_address) {
        this.commun_address = commun_address;
    }

    public ContactData() {

    }

    @ExcelProperty(name = "ns1:commun_method")
    private String commun_method;
    @ExcelProperty(name = "ns1:commun_address")
    private String commun_address;
}
