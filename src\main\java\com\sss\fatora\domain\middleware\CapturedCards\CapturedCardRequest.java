package com.sss.fatora.domain.middleware.CapturedCards;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CapturedCardRequest {
    private String bank;
    private String terminalId;
    private String cardNumber;
    private String fromCaptDate;
    private String toCaptDate;
    private String captReason;

    private List<String> fields;

    private String sorting;
    private Integer maxResultCount;
    private Integer skipCount;
}
