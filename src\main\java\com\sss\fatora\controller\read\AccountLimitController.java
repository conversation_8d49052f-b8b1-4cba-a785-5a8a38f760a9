package com.sss.fatora.controller.read;

import com.sss.fatora.domain.read.AccountLimit;
import com.sss.fatora.service.read.AccountLimitService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/account-limits")
public class AccountLimitController {

    final private AccountLimitService accountLimitService;

    public AccountLimitController(AccountLimitService accountLimitService) {
        this.accountLimitService = accountLimitService;
    }

    @RequestMapping(value = "/by-accountNo", method = RequestMethod.GET)
    public ResponseObject getByAccountNumber(@RequestParam("accountNumber") String accountNumber){
        List <AccountLimit> accountLimitList = accountLimitService.getByAccountNumber(accountNumber);
        return accountLimitList != null ?
                ResponseObject.FETCHED_SUCCESS(accountLimitList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
