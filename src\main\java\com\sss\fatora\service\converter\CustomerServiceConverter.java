package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.*;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CustomerServiceConverter extends GenericConverterService<Customer> {

    @Autowired
    ContractService contractService;
    @Autowired
    PersonServiceConverter personService;
    @Autowired
    AddressService addressService;
    @Autowired
    CustomerContactService customerContactService;

    public Customer prepareCustomerObject(Customer customer, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(customer, excelIndexes, row);
        customer.setContract(contractService.prepareContractObject(new Contract(), excelIndexes, row));
        customer.setPerson(personService.preparePersonObject(new Person(), excelIndexes, row));
        customer.setContact(customerContactService.prepareCustomerContactObject(new CustomerContact(), excelIndexes, row));
        customer.setAddress(addressService.prepareAddressObject(new Address(), excelIndexes, row));
        return customer;
    }

    public Customer prepareCustomerObjectFromPanel(Customer customer, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(customer, fixedValues);
        customer.setContract(contractService.prepareContractObjectFromPanel(customer.getContract(), fixedValues));
        customer.setPerson(personService.preparePersonObjectFromPanel(customer.getPerson(), fixedValues));
        customer.setContact(customerContactService.prepareCustomerContactObjectFromPanel(customer.getContact(), fixedValues));
        customer.setAddress(addressService.prepareAddressObjectFromPanel(customer.getAddress(), fixedValues));
        return customer;
    }

    public Customer prepareCustomerObjectForChangePrimaryAccount(Customer customer, Map<String, String> fixedValues) throws IllegalAccessException {
        customer.setCommand("CMMDEXPR");
        getPropertyValueByObject(customer, fixedValues);
        customer.setContract(contractService.prepareContractObjectForChangePrimaryAccount(new Contract(), fixedValues));
        return customer;
    }

    public Customer prepareCustomerObjectForAccount(Customer customer, Map<String, String> fixedValues, List<Account> accountList) throws IllegalAccessException {
        customer.setCommand("CMMDEXPR");
        getPropertyValueByObject(customer, fixedValues);
        customer.setContract(contractService.prepareContractObjectForAccount(new Contract(), fixedValues, accountList));
        return customer;
    }

    public Customer prepareCustomerObjectForChangeMobileNumber(Customer customer, Map<String, String> fixedValues, List<Contact> contactList ) throws IllegalAccessException {
        customer.setCommand("CMMDEXPR");
        getPropertyValueByObject(customer, fixedValues);
        customer.setContract(contractService.prepareContractObjectForChangeMobileNumber(new Contract(), fixedValues, contactList));
        return customer;
    }

    public Customer prepareCustomerObjectForChangeProduct(Customer customer, Map<String, String> fixedValues ) throws IllegalAccessException {
        customer.setCommand("CMMDEXPR");
        getPropertyValueByObject(customer, fixedValues);
        customer.setContract(contractService.prepareContractObjectForChangeProduct(new Contract(), fixedValues));
        return customer;
    }
}
