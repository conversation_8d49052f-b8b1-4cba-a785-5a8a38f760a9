package com.sss.fatora.domain.settlement;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "Banks")
public class SettlementBank {


    @Id
    @Column(name = "Id")
    Integer id;

    @Column(name = "Code")
    Integer code;

    @Column(name = "Name")
    String name;

    @Column(name = "Short_Name")
    String shortName;

    @Column(name = "Institution_Id")
    String institutionId;
}
