package com.sss.fatora.security.model;


import com.sss.fatora.domain.local.ApplicationUser;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

public class CustomUserDetails extends User {

    private String email;
    private String password;
    private String moduleName;
    private Integer systemModuleId;
    private ApplicationUser applicationUser;
    private Boolean mobile;
    private List<String> errorsList;


    public CustomUserDetails(ApplicationUser applicationUser, String password, String moduleName) {
        super(applicationUser.getDomainName(), password,
                true, true, true,
                true, new ArrayList<GrantedAuthority>());
        this.applicationUser = applicationUser;
        this.moduleName = moduleName;

    }

    public CustomUserDetails(String username, String password, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
        this.email = username;
        this.password = password;
    }

    public CustomUserDetails(String email, String password, boolean enabled
            , boolean accountNonExpired, boolean credentialsNonExpired
            , boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities) {
        super(email, password, true, true, true, true, authorities);
        this.email = email;
        this.password = password;
        this.errorsList=new ArrayList<>();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public ApplicationUser getApplicationUser() {
        return applicationUser;
    }

    public void setApplicationUser(ApplicationUser applicationUser) {
        this.applicationUser = applicationUser;
    }

    public Integer getSystemModuleId() {
        return systemModuleId;
    }

    public void setSystemModuleId(Integer systemModuleId) {
        this.systemModuleId = systemModuleId;
    }

    public static CustomUserDetails getCurrentInstance() {
        if (SecurityContextHolder.getContext() == null || SecurityContextHolder.getContext().getAuthentication() == null)
            return null;
        return (CustomUserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    public static ApplicationUser getCurrentUser() {
        return Objects.requireNonNull(getCurrentInstance()).getApplicationUser();
    }

    public Boolean getMobile() {
        return mobile;
    }

    public void setMobile(Boolean mobile) {
        this.mobile = mobile;
    }

    public List<String> getErrorsList() {
        return errorsList;
    }

    public void setErrorsList(List<String> errorsList) {
        this.errorsList = errorsList;
    }

}
