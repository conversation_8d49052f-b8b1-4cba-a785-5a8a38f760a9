package com.sss.fatora.domain.middleware.terminals.dto.EPOS;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class ChangeEGateDataEPOSRequest {
    private String oldProjectManagerName;
    private String oldProjectManagerEmail;
    private String oldProjectManagerMobile;
    private String oldTechnicalManagerName;
    private String oldTechnicalManagerEmail;
    private String oldTechnicalManagerMobile;
    private String oldIPAddress;
    private String oldLogo;
    private String newProjectManagerName;
    private String newProjectManagerEmail;
    private String newProjectManagerMobile;
    private String newTechnicalManagerName;
    private String newTechnicalManagerEmail;
    private String newTechnicalManagerMobile;
    private String newIPAddress;
}
