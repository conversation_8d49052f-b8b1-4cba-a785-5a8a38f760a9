package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.Card;
import com.sss.fatora.domain.converter.Contact;
import com.sss.fatora.domain.converter.Contract;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ContractService extends GenericConverterService<Contract> {
    @Autowired
    CardService cardService;
    @Autowired
    AccountServiceConverter accountService;
    @Autowired
    ServiceService serviceService;

    public Contract prepareContractObject(Contract contract, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(contract, excelIndexes, row);
        Card card = cardService.prepareCardObject(new Card(), excelIndexes, row);
        contract.setCard(card);
        List<Account> accountList = accountService.prepareAccountList(excelIndexes, row);
        contract.setAccount(accountList);
        contract.setService(serviceService.prepareServiceObject(accountList, card));
        return contract;
    }

    public Contract prepareContractObjectFromPanel(Contract contract, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(contract, fixedValues);
        Card card = cardService.prepareCardObjectFromPanel(contract.getCard(), fixedValues);
        contract.setCard(card);
        List<Account> accountList = accountService.prepareAccountListFromPanel(contract.getAccount(), fixedValues);
        contract.setAccount(accountList);
        contract.setService(serviceService.prepareServiceFromPanel(accountList, card));
        return contract;
    }


    public Contract prepareContractObjectForChangePrimaryAccount(Contract contract, Map<String, String> fixedValues) throws IllegalAccessException {
        contract.setCommand("CMMDEXPR");
        getPropertyValueByObject(contract, fixedValues);
        Card card = cardService.prepareCardObjectForChangePrimaryAccount(new Card(), fixedValues);
        contract.setCard(card);
        //  List<Account> accountList = accountService.prepareAccountListForChangePrimaryAccount(contract.getAccount(),fixedValues);
        //  contract.setAccount(accountList);
        //  contract.setService(serviceService.prepareServiceObject(accountList,card));
        return contract;
    }


    public Contract prepareContractObjectForAccount(Contract contract, Map<String, String> fixedValues, List<Account> accountList) throws IllegalAccessException {
        contract.setCommand("CMMDEXPR");
        getPropertyValueByObject(contract, fixedValues);
        Card card = cardService.prepareCardObjectForAccount(new Card(), fixedValues);
        contract.setCard(card);
        List<Account> preparedAccountList = accountService.prepareAccountListForAccount(accountList, fixedValues);
        contract.setAccount(preparedAccountList);
        contract.setService(serviceService.prepareServiceForAccount(preparedAccountList, card));
        return contract;
    }

    public Contract prepareContractObjectForChangeMobileNumber(Contract contract, Map<String, String> fixedValues, List<Contact> contactList) throws IllegalAccessException {
        contract.setCommand("CMMDEXPR");
        getPropertyValueByObject(contract, fixedValues);
        Card card = cardService.prepareCardObjectForChangeMobileNumber(new Card(), fixedValues, contactList);
        contract.setCard(card);

        return contract;
    }

    public Contract prepareContractObjectForChangeProduct(Contract contract, Map<String, String> fixedValues) throws IllegalAccessException {
        contract.setCommand("CMMDEXUP");
        getPropertyValueByObject(contract, fixedValues);
        return contract;
    }
}
