package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.Products;
import com.sss.fatora.domain.read.TransType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ProductsDao extends GenericReadDao<Products, Long> {
    @Query("SELECT P FROM Products P " +
            "WHERE P.bankCode=:bankCode")
    List<Products> getAllProductsByBankCode(@Param("bankCode") String bankCode);

    @Query("SELECT P FROM Products P " +
            "WHERE P.id=:productId")
    Products getProductById(@Param("productId") Long productId);

    @Query("SELECT P.bankCode FROM Products P " +
            "WHERE P.productNumber=:productNumber")
    String getBankCodeByProductNumber(@Param("productNumber") Long productNumber);

    @Query("SELECT P.productNumber FROM Products P " +
            "WHERE P.label=:productOldName")
    Long getProductNumberByLabel(@Param("productOldName") String productOldName);
}
