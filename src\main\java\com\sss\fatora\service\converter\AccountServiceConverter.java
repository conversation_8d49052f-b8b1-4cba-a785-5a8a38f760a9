package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.*;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class AccountServiceConverter extends GenericConverterService<Account> {

    @Autowired
    AccountObjectService accountObjectService;


    public List<Account> prepareAccountList(Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        List<Account> accountList = new ArrayList<>();
        Account1 account1 = new Account1();
        Account2 account2 = new Account2();
        Account3 account3 = new Account3();
        accountList.add(getPropertyValueByExcel(account1, excelIndexes, row));
        Account preparedAccount2 = getPropertyValueByExcel(account2, excelIndexes, row);
        Account preparedAccount3 = getPropertyValueByExcel(account3, excelIndexes, row);
        AccountObject accountObject = accountObjectService.prepareAccountObjectObject(new AccountObject(), excelIndexes, row);
        AccountObject accountObject2 = new AccountObject();
        account1.setAccount_object(accountObject);
        if (preparedAccount2 != null) {
            accountList.add(preparedAccount2);
            accountObject2.setRef_id(accountObject.getRef_id());
            accountObject2.setAccount_link_flag(accountObject.getAccount_link_flag());
            account2.setAccount_object(accountObject2);
        }
        if (preparedAccount3 != null) {
            accountList.add(preparedAccount3);
            account3.setAccount_object(accountObject2);
        }

        return accountList;

    }


    public List<Account> prepareAccountListFromPanel(List<Account> accountList, Map<String, String> fixedValues) throws IllegalAccessException {
        Integer counter = 1;
        for (Account account : accountList) {
            account.setId("account_" + counter.toString());
            getPropertyValueByObject(account, fixedValues);
            accountObjectService.prepareAccountObjectObjectFromPanel(account.getAccount_object(), fixedValues);
            counter = counter + 1;
        }
        return accountList;

    }


    public List<Account> prepareAccountListForChangePrimaryAccount(List<Account> accountList, Map<String, String> fixedValues) throws IllegalAccessException {

        for (Account account : accountList) {
            getPropertyValueByObject(account, fixedValues);
            accountObjectService.prepareAccountObjectObjectForChangePrimaryAccount(account.getAccount_object(), fixedValues);
        }
        return accountList;

    }

    public List<Account> prepareAccountListForAccount(List<Account> accountList, Map<String, String> fixedValues) throws IllegalAccessException {

        for (Account account : accountList) {
            account.setCommand("CMMDCRUP");
            getPropertyValueByObject(account, fixedValues);
            accountObjectService.prepareAccountObjectForAccount(account.getAccount_object(), fixedValues);
        }
        return accountList;

    }
}
