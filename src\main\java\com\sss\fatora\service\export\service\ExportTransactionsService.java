package com.sss.fatora.service.export.service;

import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.read.TransactionDataVW;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import com.sss.fatora.utils.service.ObjectConverter;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class ExportTransactionsService {

    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;
    final ExportService exportService;
    final ContentUtilService contentUtilService;

    @Autowired
    @Qualifier("readingDBEntityManagerFactory")
    private EntityManager entityManager;

    public ExportTransactionsService(PaginationService paginationService, PrivilegeService privilegeService,
            ConfigService configService, ExportService exportService, ContentUtilService contentUtilService) {
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.exportService = exportService;
        this.contentUtilService = contentUtilService;
    }

    /**
     * @param transaction       This Parameter Is For The Value Of Filters Send From
     *                          The Front-End
     * @param fromUDate,toUDate This Two Parameters Represent The Period We Want To
     *                          Search In
     * @param filterOperator    There Is An Specific Operator For Every Filter
     *                          {@link com.sss.fatora.utils.constants.Operator}
     * @param projection        This Is A List Of Columns For Select Statement In
     *                          Search Query
     *                          <p>
     *                          This Function Add Certain Conditions To Where Clause
     *                          That Can't Be Added To The Filter
     *                          (TransactionDataVW)
     *                          Directly And Then Request The Dynamic Search
     *                          Function ( dynamicSearch() )
     */
    public byte[] search(TransactionDataVW transaction, Long fromUDate, Long toUDate, Pagination pagination,
            Map<String, Operator> filterOperator, List<String> projection, Boolean details, Boolean withBins)
            throws Exception {

        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page transactions;
        String additionalConstraint = "";
        long date1 = 99;
        long date2 = 99;
        long time1 = 99;
        long time2 = 99;
        long dateLimitLong = 99;

        String searchLimitConstraint = "";
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        DateFormat timeFormat = new SimpleDateFormat("HHmmss");

        if (transaction == null)
            transaction = new TransactionDataVW();

        if (fromUDate == null && transaction.getId() == null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.MONTH, -1);
            Date lastMonthDate = cal.getTime();
            fromUDate = lastMonthDate.getTime();
        }
        if (toUDate == null && transaction.getId() == null)
            toUDate = new Date().getTime();

        if (applicationUser.getMonthlySearchLimit() != null) {
            Date dateLimit = DateUtils.getDateAfterMonthsAdded(new Date(),
                    -1 * applicationUser.getMonthlySearchLimit());
            dateLimitLong = Long.parseLong(dateFormat.format(new Date(dateLimit.getTime())));
            searchLimitConstraint = " And TransactionDataVW.udate >=" + dateLimitLong;

        }
        if (fromUDate != null) {
            date1 = Long.parseLong(dateFormat.format(new Date(fromUDate)));
            time1 = Long.parseLong(timeFormat.format(new Date(fromUDate)));
            if (date1 < dateLimitLong && dateLimitLong != 99) {
                date1 = dateLimitLong;
            }

        }
        if (toUDate != null) {
            date2 = Long.parseLong(dateFormat.format(new Date(toUDate)));
            time2 = Long.parseLong(timeFormat.format(new Date(toUDate)));
        }
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            if (privilegeService.privilegeFoundByUserIdAndPrivilegeName(applicationUser.getId(), "hpan")) {
                projection.add("Case When TransactionDataVW.issuerBank =" + bankCode +
                        " Then TransactionDataVW.hpan When TransactionDataVW.issuerBank <>" + bankCode +
                        " Then TransactionDataVW.maskedHpan End As hpan ");
                projection.add("Case When TransactionDataVW.issuerBank =" + bankCode +
                        " Then TransactionDataVW.acct1 When TransactionDataVW.issuerBank <>" + bankCode +
                        " Then 'Unavailable' End As acct1 ");
                projection.removeIf(s -> s.equals("TransactionDataVW.hpan AS hpan"));
                projection.removeIf(s -> s.equals("TransactionDataVW.acct1 AS acct1"));
            }
      

            if (withBins != null && withBins) {
                additionalConstraint += "( " + getTransactionsByUserBankBins(applicationUser) + ") AND ";
                if (date1 == date2) {
                    additionalConstraint += "(TransactionDataVW.udate = " + date1 +
                            " AND TransactionDataVW.onlineTime BETWEEN " + time1 + " AND " + time2 + ")";
                } else {
                    additionalConstraint += "(" +
                            "(TransactionDataVW.udate = " + date1 + " AND TransactionDataVW.onlineTime >= " + time1
                            + ") OR " +
                            "(TransactionDataVW.udate > " + date1 + " AND TransactionDataVW.udate < " + date2 + ") OR "
                            +
                            "(TransactionDataVW.udate = " + date2 + " AND TransactionDataVW.onlineTime <= " + time2
                            + ")" +
                            ")";
                }
            } else {
                additionalConstraint += "(TransactionDataVW.issuerBank = " + bankCode + " OR " +
                        " TransactionDataVW.acquirerBank = " + bankCode + ") AND ";
                if (date1 == date2) {
                    additionalConstraint += "(TransactionDataVW.udate = " + date1 +
                            " AND TransactionDataVW.onlineTime BETWEEN " + time1 + " AND " + time2 + ")";
                } else {
                    additionalConstraint += "(" +
                            "(TransactionDataVW.udate = " + date1 + " AND TransactionDataVW.onlineTime >= " + time1
                            + ") OR " +
                            "(TransactionDataVW.udate > " + date1 + " AND TransactionDataVW.udate < " + date2 + ") OR "
                            +
                            "(TransactionDataVW.udate = " + date2 + " AND TransactionDataVW.onlineTime <= " + time2
                            + ")" +
                            ")";
                }

            }
        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()) && transaction.getId() == null) {
            if (date1 == date2) {
                additionalConstraint += "(TransactionDataVW.udate = " + date1 +
                        " AND TransactionDataVW.onlineTime BETWEEN " + time1 + " AND " + time2 + ")";
            } else {
                additionalConstraint += "(" +
                        "(TransactionDataVW.udate = " + date1 + " AND TransactionDataVW.onlineTime >= " + time1
                        + ") OR " +
                        "(TransactionDataVW.udate > " + date1 + " AND TransactionDataVW.udate < " + date2 + ") OR " +
                        "(TransactionDataVW.udate = " + date2 + " AND TransactionDataVW.onlineTime <= " + time2 + ")" +
                        ")";
            }
        }
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("TransactionDataVW.utrnno");
            pagination.setOrderType("DESC");
        }

        File file = contentUtilService.makeExcelFile();
        TempContentModel tempContentModel = contentUtilService.makeTempModel(file);
        if (details == false) {
            transactions = exportService.dynamicSearch(transaction, pagination, additionalConstraint, filterOperator,
                    projection, file.getPath(), configService.getTransactionHeaders(),
                    configService.getTransactionColumns());

            return contentUtilService.fetchAsBytes(tempContentModel);
        } else {
            transactions = exportService.dynamicSearch(transaction, pagination, additionalConstraint, filterOperator,
                    projection, file.getPath(), configService.getTransactionHeadersWithDetails(),
                    configService.getTransactionColumnsWithDetails());

            return contentUtilService.fetchAsBytes(tempContentModel);
        }
    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List
     *                   Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two
     *                   List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As
     *                   Column) To Be Used In The
     *                   Select Section Of The Query
     */
    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> "TransactionDataVW." + s + " AS " + s)
                .collect(Collectors.toList());
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This
     * Specific Domain (Online Transaction)
     */
    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService
                .getPrivilegesById(PrivilegeType.TRANSACTION_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    /**
     * @param transaction       This Parameter Is For The Value Of Filters Send From
     *                          The Front-End
     * @param fromUDate,toUDate This Two Parameters Represent The Period We Want To
     *                          Search In
     * @param filterOperator    There Is An Specific Operator For Every Filter
     *                          {@link com.sss.fatora.utils.constants.Operator}
     *                          <p>
     *                          This Function Get UserPrivileges And Domain Columns
     *                          And Then Request The Intersection Function
     *                          ( filterProjectionList() ) On Them, Then Add Some
     *                          Clauses To Select Statement (
     *                          addIssuerAndAcquirerNamesToQuery() )
     *                          , Set The Desired Filter Values To The Filter Object
     *                          When Requesting ( getRouting() ) And Then
     *                          Request Search Function ( search() )
     */
    @Log(actionType = ActionType.TRANSACTION_EXPORT)
    public byte[] export(TransactionDataVW transaction, Long fromUDate, Long toUDate, Pagination pagination,
            Map<String, Operator> filterOperator, Boolean details, Boolean withBins) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getTransactionMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        if (details)
            projection.addAll(configService.getTransactionDetailsColumns());
        projection = filterProjectionList(projection, privileges);
        projection = addIssuerAndAcquirerNamesToQuery(projection);

        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        // getRouting(transaction);
        return search(transaction, fromUDate, toUDate, pagination, filterOperator, projection, details, withBins);
    }

    /**
     * @param projection This Is A List Of Names Used In The Select Statement
     *                   <p>
     *                   This Function Add Some Clauses To Select Statement
     */
    private List<String> addIssuerAndAcquirerNamesToQuery(List<String> projection) {
        projection.remove("TransactionDataVW.issuerBank AS issuerBank");
        projection.remove("TransactionDataVW.acquirerBank AS acquirerBank");
        projection.add(
                "(select b.shortName from FatoraBank as b where b.code = TransactionDataVW.acquirerBank) AS acquirerBank ");
        projection.add(
                "(select b.shortName from FatoraBank as b where b.code = TransactionDataVW.issuerBank) AS issuerBank ");
        return projection;
    }

    /**
     * @param filter This Parameter Is For The Value Of Filters Send From The
     *               Front-End
     *               This Function Check If The User Is A Bank User, It Adds A New
     *               Value
     *               In The Filter
     */
    public void getRouting(TransactionDataVW filter) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (filter.getIssuerBank() != null && applicationUser.getBank() != null) {
            String bankCode = applicationUser.getBank().getCode();
            filter.setIssuerBank(bankCode);
        }
    }

    private String getTransactionsByUserBankBins(ApplicationUser applicationUser) {
        String additionalConstraint = "";
        String BinString = applicationUser.getBank().getBin();
        String[] Bins = BinString.split(",");
        for (int BinCount = 0; BinCount < Bins.length; BinCount++) {
            String Bin = Bins[BinCount];
            if (BinCount < Bins.length - 1)
                additionalConstraint += " TransactionDataVW.hpan like  '" + Bin + "%' or ";
            else
                additionalConstraint += " TransactionDataVW.hpan like  '" + Bin + "%' ";
        }
        return additionalConstraint;
    }
}
