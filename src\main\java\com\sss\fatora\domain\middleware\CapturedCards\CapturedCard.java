package com.sss.fatora.domain.middleware.CapturedCards;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CapturedCard {
    private String bank;
    private String terminalId;
    private String cardNumber;
    private String captDate;
    private String captReason;

    private String cardHolderName;
}
