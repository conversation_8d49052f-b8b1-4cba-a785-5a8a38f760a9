package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_PRODUCTS")
public class Products {
    @Id
    @Column(name = "ID")
    Long id;
    @Column(name = "PRODUCT_NUMBER")
    Long productNumber;
    @Column(name = "PARENT_ID")
    Long parentId;
    @Column(name = "PARENT_PRODUCT_NUMBER")
    Long parentProductNumber;
    @Column(name = "LABEL")
    String label;
    @Column(name = "BANK_CODE")
    String bankCode;
    @Column(name = "CARD_TYPE_ID")
    Long cardTypeId;
    @Column(name = "CONTRACT_TYPE")
    String contractType;
}
