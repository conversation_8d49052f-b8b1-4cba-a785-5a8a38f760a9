package com.sss.fatora.service.export.service;

import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExportSettlementTransactionService {
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;
    final ExportService exportService;
    final ContentUtilService contentUtilService;

    @Autowired
    @Qualifier("settlementDBEntityManagerFactory")
    private EntityManager entityManager;

    public ExportSettlementTransactionService(PaginationService paginationService, PrivilegeService privilegeService, ConfigService configService, ExportService exportService, ContentUtilService contentUtilService) {
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.exportService = exportService;
        this.contentUtilService = contentUtilService;
    }

    /**
     * @param transaction      This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate  This Two Parameters Represent The Period We Want To Search In
     * @param showReconciled   This Parameter Is For Adding A Specific Condition To The Search Query ( This
     *                         condition Is Fatora logic )
     * @param showSettled      This Parameter Is For Adding A Specific Condition To The Search Query ( This
     *                         condition Is Fatora logic )
     * @param filterOperator   There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection       This Is A List Of Columns For Select Statement In Search Query
     *                         <p>
     *                         This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (SettlementTransaction)
     *                         Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     */
    public byte[] search(SettlementTransaction transaction, Long fromDate, Long toDate, Boolean showReconciled, Boolean showSettled,
                         Pagination pagination, Map<String, Operator> filterOperator, List<String> projection,
                         Boolean details, Boolean withBins) throws Exception {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page transactions;
        String additionalConstraint = "";
        String oracleFormat = "yyyy-MM-dd HH24:MI:ss";
        String javaFormat = "yyyy-MM-dd HH:mm:ss";
        DateFormat f = new SimpleDateFormat(javaFormat);
        String date1 = "";
        String date2 = "";
        String reconciliationDate = "";
        String settlementDate = "";
        String searchLimitConstraint = "";


        if (transaction == null)
            transaction = new SettlementTransaction();
        if (fromDate == null)
            fromDate = new Date(631152000).getTime();
        if (toDate == null)
            toDate = new Date(System.currentTimeMillis() * 100).getTime();

//
//        if (applicationUser.getMonthlySearchLimit() != null) {
//            Date dateLimit = DateUtils.getDateAfterMonthsAdded(new Date(), -1 * applicationUser.getMonthlySearchLimit());
//            searchLimitConstraint = " And SettlementTransaction.date >=" + "to_timestamp('" + f.format(dateLimit) + "','" + oracleFormat + "')";
//        }


        date1 = getDate(fromDate);
        date2 = getDate(toDate);
//        date1 = "to_timestamp('" + f.format(DateUtils.getStartDayOfDate(fromDate)) + "','" + oracleFormat + "')";
//        date2 = "to_timestamp('" + f.format(DateUtils.getEndDayOfDate(toDate)) + "','" + oracleFormat + "')";

        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            additionalConstraint =
//                    "(substring(SettlementTransaction.mainTerminal,0,2) = " + bankCode + " ) " +
                    " SettlementTransaction.date BETWEEN " + date1 + " AND " + date2 + searchLimitConstraint;
            if (withBins != null && withBins) {
                additionalConstraint += " AND ( " + getTransactionsByUserBankBins(applicationUser) + " ) ";
            }


        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType())) {
            additionalConstraint = "SettlementTransaction.date BETWEEN " + date1 + " AND " + date2 + searchLimitConstraint;
        }
        if (showReconciled != null) {
            if (showReconciled.equals(true))
                additionalConstraint += " AND SettlementTransaction.reconciliationDate is not null  ";
            if (showReconciled.equals(false))
                additionalConstraint += " AND SettlementTransaction.reconciliationDate is null  ";
        }
        if (showSettled != null) {
            if (showSettled.equals(true))
                additionalConstraint += " AND SettlementTransaction.settlmentDate is not null ";
            if (showSettled.equals(false))
                additionalConstraint += " AND SettlementTransaction.settlmentDate is null ";
        }
//        if (showUnreconciled != null && showUnreconciled.equals(true)){
////            additionalConstraint+=" AND (SettlementTransaction.matched like 0 AND SettlementTransaction.solved like 0 OR SettlementTransaction.closed like 0) ";
//            additionalConstraint+=" AND (SettlementTransaction.reconciliationDate is null or SettlementTransaction.settlmentDate is null) ";
//        }
        if (transaction.getReconciliationDate() != null) {
            reconciliationDate = f.format(DateUtils.getStartDayOfDate(transaction.getReconciliationDate().getTime()));
            additionalConstraint += " AND SettlementTransaction.reconciliationDate = '" + reconciliationDate + "'";
            transaction.setReconciliationDate(null);
        }
        if (transaction.getSettlmentDate() != null) {
            settlementDate = f.format(DateUtils.getStartDayOfDate(transaction.getSettlmentDate().getTime()));
            additionalConstraint += " AND SettlementTransaction.settlmentDate = '" + settlementDate + "'";
            transaction.setSettlmentDate(null);
        }

        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("SettlementTransaction.date");
            pagination.setOrderType("DESC");
        }
        File file = contentUtilService.makeExcelFile();
        TempContentModel tempContentModel = contentUtilService.makeTempModel(file);
        if (details == false) {
            transactions = exportService.dynamicSearch(transaction
                    , pagination
                    , additionalConstraint
                    , filterOperator
                    , projection
                    , file.getPath()
                    , configService.getSettlementTransactionHeaders()
                    , configService.getSettlementTransactionColumns()
            );
        } else {
            transactions = exportService.dynamicSearch(transaction
                    , pagination
                    , additionalConstraint
                    , filterOperator
                    , projection
                    , file.getPath()
                    , configService.getSettlementTransactionHeadersWithDetails()
                    , configService.getSettlementTransactionColumnsWithDetails()
            );
        }

        return contentUtilService.fetchAsBytes(tempContentModel);

    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As Column) To Be Used In The
     *                   Select Section Of The Query
     */
    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> {
                    if (s.equalsIgnoreCase("acquirer")) {
                        s = "Bank1.shortName AS " + s;
                    } else if (s.equalsIgnoreCase("issuer")) {
                        s = "Bank2.shortName AS " + s;
                    } else if (s.equalsIgnoreCase("channel")) {
                        s = "Channel.name AS " + s;
                    } else {
                        s = "SettlementTransaction." + s + " AS " + s;
                    }
                    return s;
                })
                .collect(Collectors.toList());

    }


    /**
     * @param expDate This Parameter Is A TimeStamp Date
     *                <p>
     *                This Function Change The Date From TimeStamp To Another Irregular Format
     *                That Is Like (********)
     */
    private String getDate(Long expDate) {
        String stringDate;
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        calendar.setTimeInMillis(expDate);
        Integer dateYear = calendar.get(Calendar.YEAR);
        Integer dateMonth = calendar.get(Calendar.MONTH) + 1;
        Integer dateDay = calendar.get(Calendar.DAY_OF_MONTH);
        /*
          This 'If' Is For EXAMPLE.
          year = 2021, month = 11, day = 12 So It Become -> 20211112 Do Not Need To Add Any Zero
         */
        if (dateMonth >= 10 && dateDay >= 10) {
            stringDate = dateYear.toString() + dateMonth.toString() + dateDay.toString();
        }
        /*
         * This 'If' Is For EXAMPLE.
         * year = 2021, month = 1, day = 12 So It Become -> 20210112 We Add A Zero After Year Before Month
         */
        else if (dateMonth < 10 && dateDay >= 10) {
            stringDate = dateYear.toString() + "0" + dateMonth.toString() + dateDay.toString();
        }
        /*
         * This 'If' Is For EXAMPLE.
         * year = 2021, month = 11, day = 2 So It Become -> 20211102 We Add A Zero After Month Before Day
         */
        else if (dateMonth >= 10 && dateDay < 10) {
            stringDate = dateYear.toString() + dateMonth.toString() + "0" + dateDay.toString();
        }
        /*
         * This 'If' Is For EXAMPLE.
         * year = 2021, month = 1, day = 2 So It Become -> 20210102 We Add Two Zeros One Before Month And The Other
         * Before Day
         */
        else {
            stringDate = dateYear.toString() + "0" + dateMonth.toString() + "0" + dateDay.toString();
        }
        return stringDate;
    }


    /**
     * This Function Get A List Of All Privileges Names That This User Has For This Specific Domain (SettlementTransaction)
     */
    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.SETTLEMENT_TRANSACTION_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    /**
     * @param transaction       This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromUDate,toUDate This Two Parameters Represent The Period We Want To Search In
     * @param showReconciled  This Parameter Is For Adding A Specific Condition To The Search Query (This
     *                          condition Is Fatora logic)
     *
     * @param showSettled  This Parameter Is For Adding A Specific Condition To The Search Query (This
     *                          condition Is Fatora logic)
     * @param filterOperator    There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *                          <p>
     *                          This Function Get UserPrivileges And Domain Columns And Then Request The Intersection Function
     *                          ( filterProjectionList() ) On Them, Set The Desired Filter Values To The Filter Object When
     *                          Requesting ( getRouting() ) And Then Request Search Function ( search() )
     */
    @Log(actionType = ActionType.TRANSACTION_EXPORT)
    public byte[] export(SettlementTransaction transaction, Long fromUDate, Long toUDate, Boolean showReconciled, Boolean showSettled,
                         Pagination pagination, Map<String, Operator> filterOperator, Boolean details, Boolean withBins) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getSettlementTransactionColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        //You can use this condition in the future if there is export details for SettlementTransaction
        if (details)
            projection.addAll(configService.getSettlementTransactionColumnsWithDetails());
        projection = filterProjectionList(projection, privileges);

        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        getRouting(transaction);
        return search(transaction, fromUDate, toUDate, showReconciled, showSettled, pagination, filterOperator, projection,
                details, withBins);
    }

    /**
     * @param filter This Parameter Is For The Value Of Filters Send From The Front-End
     *               This Function Check If The User Is A Bank User, It Adds A New Value
     *               In The Filter
     */
    public void getRouting(SettlementTransaction filter) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getBank() != null) {
            String bankCode = applicationUser.getBank().getCode();
            filter.setBankOfRecord(bankCode);
        }
    }

    private String getTransactionsByUserBankBins(ApplicationUser applicationUser) {
        String additionalConstraint = "";
        String BinString = applicationUser.getBank().getBin();
        String[] Bins = BinString.split(",");
        for (int BinCount = 0; BinCount < Bins.length; BinCount++) {
            String Bin = Bins[BinCount];
            if (BinCount < Bins.length - 1)
                additionalConstraint += " SettlementTransaction.cardNumber like  '" + Bin + "%' or ";
            else
                additionalConstraint += " SettlementTransaction.cardNumber like  '" + Bin + "%' ";
        }
        return additionalConstraint;
    }

}
