package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.ApplicationUserPrivilege;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;

public interface ApplicationUserPrivilegeDao extends GenericDao<ApplicationUserPrivilege,Integer> {

    @Query("SELECT DISTINCT AUP FROM ApplicationUserPrivilege AUP " +
            "LEFT JOIN FETCH AUP.privilege P " +
            "WHERE AUP.applicationUser.id=:userId and P.recordStatus = 1 order by P.orderItem")
    Set<ApplicationUserPrivilege> getAllPrivilegeForUser(@Param("userId") Integer userId);

    @Modifying
    @Query("DELETE FROM ApplicationUserPrivilege AUP " +
            "WHERE AUP.applicationUser.id =:userId " +
            "AND AUP.privilege.id IN :privilegeList")
    void deleteAll(@Param("userId") Integer userId, @Param("privilegeList") List<Integer> privilegeList);

    @Query("SELECT AUP FROM ApplicationUserPrivilege AUP " +
            "WHERE AUP.applicationUser.id=:userId")
    List<ApplicationUserPrivilege> getByUserId(@Param("userId") Integer userId);
}
