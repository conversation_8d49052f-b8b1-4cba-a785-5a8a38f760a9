package com.sss.fatora.controller.middleware.merchants;

import com.sss.fatora.domain.middleware.Merchants.MerchantRequest;
import com.sss.fatora.domain.middleware.Merchants.MerchantResponse;
import com.sss.fatora.domain.middleware.Merchants.dto.UpdateMerchantRequest;
import com.sss.fatora.service.middleware.merchants.merchantService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/merchants")
public class merchantController {
    private final merchantService merchantService;

    public merchantController(merchantService merchantService) {
        this.merchantService = merchantService;
    }

    @RequestMapping("/search")
    public ResponseObject search(@ParameterName("merchant") MerchantRequest merchantRequest,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination) {
        MerchantResponse posTerminals = merchantService.search(merchantRequest, pagination);
        return posTerminals != null ?
                ResponseObject.FETCHED_SUCCESS(posTerminals, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/details")
    public ResponseObject getWithDetails(@ParameterName("merchant") MerchantRequest merchantRequest) {
        MerchantResponse posTerminals = merchantService.getWithDetails(merchantRequest);
        return posTerminals != null ?
                ResponseObject.FETCHED_SUCCESS(posTerminals, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping("/export")
    public ResponseObject export(@ParameterName("merchant") MerchantRequest merchantRequest,
                                 @ParameterName(("withDetails")) Boolean withDetails,
                                 HttpServletResponse response) {
        MerchantResponse posTerminals = merchantService.export(merchantRequest, new Pagination(),withDetails,response);
        return posTerminals != null ?
                ResponseObject.FETCHED_SUCCESS(posTerminals, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @PostMapping("/update")
    public ResponseObject update(@RequestBody UpdateMerchantRequest merchantRequest){
        merchantService.update(merchantRequest);
        return ResponseObject.UPDATED_SUCCESS(null,null);
    }
}
