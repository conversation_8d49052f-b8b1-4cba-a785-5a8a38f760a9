package com.sss.fatora.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;


@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "writingDBEntityManagerFactory",
        transactionManagerRef = "writingDBTransactionManager",
        basePackages = "com.sss.fatora.dao.write"
)
public class WritingDBConfig {
    @Autowired
    Environment env;

    @Bean(name = "writingDBDataSourceProperties")
    @ConfigurationProperties("spring.datasource-writing-db")
    public DataSourceProperties writingDBDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "writingDBDataSource")
    public DataSource writingDBDataSource() {
       DataSourceProperties writingDBDataSourceProperties = writingDBDataSourceProperties();
        return DataSourceBuilder.create()
                .driverClassName(writingDBDataSourceProperties.getDriverClassName())
                .url(writingDBDataSourceProperties.getUrl())
                .username(writingDBDataSourceProperties.getUsername())
                .password(writingDBDataSourceProperties.getPassword())
                .build();
    }

    @Bean(name = "writingDBEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
          @Qualifier("readingDBEntityManagerFactoryBuilder")  EntityManagerFactoryBuilder writingDBEntityManagerFactoryBuilder) {

        Map<String, String> JpaProperties = new HashMap<>();

        JpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.properties.hibernate.format_sql"));
        JpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
        JpaProperties.put("hibernate.hbm2ddl.auto", "none");
        JpaProperties.put("hibernate.dialect", env.getProperty("spring.datasource-writing-db.hibernate.dialect"));
        if (env.getProperty("spring.datasource-reading-db.hibernate.schema") != null ||
                !"".equals(env.getProperty("spring.datasource-reading-db.hibernate.schema")))
            JpaProperties.put("hibernate.default_schema", env.getProperty("spring.datasource-reading-db.hibernate.schema"));

        return writingDBEntityManagerFactoryBuilder
                .dataSource(writingDBDataSource())
                .packages("com.sss.fatora.domain.write")
                //.persistenceUnit("secondaryDataSource")
                .properties(JpaProperties)
                .build();

    }

    @Bean(name = "writingDBTransactionManager")
    public PlatformTransactionManager writingDBTransactionManager(
            @Qualifier("writingDBEntityManagerFactory") EntityManagerFactory writingDBEntityManagerFactory) {

        return new JpaTransactionManager(writingDBEntityManagerFactory);
    }

}
