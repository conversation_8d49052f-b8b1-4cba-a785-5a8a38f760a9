package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.ContactData;
import com.sss.fatora.domain.converter.CustomerContactData;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class CustomerContactDataService extends GenericConverterService<CustomerContactData> {
    public CustomerContactData prepareCustomerContactDataObject(CustomerContactData customerContactData, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(customerContactData, excelIndexes, row);
        return customerContactData;

    }
    public CustomerContactData prepareCustomerContactDataObjectFromPanel(CustomerContactData customerContactData, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(customerContactData, fixedValues);
        return customerContactData;

    }
}
