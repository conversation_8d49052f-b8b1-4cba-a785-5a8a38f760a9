package com.sss.fatora.domain.converter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlType(propOrder = { "application_number","application_type","application_flow_id","application_status","operator_id","institution_id","agent_number","agent_id","customer_type","customer" ,"error"})
@Setter
@Getter
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@XmlRootElement(name = "application")
public class Application extends GenericConverter {


    @ExcelProperty(name = "ns1:application_type")
    protected String application_type;
    @ExcelProperty(name = "ns1:application_number")
    protected String application_number;
    @ExcelProperty(name = "ns1:application_flow_id")
    protected String application_flow_id;
    @ExcelProperty(name = "ns1:application_status")
    protected String application_status;
    @ExcelProperty(name = "ns1:operator_id")
    protected String operator_id;
    @ExcelProperty(name = "ns1:institution_id")
    protected String institution_id;
    @ExcelProperty(name = "ns1:agent_number")
    protected String agent_number;
    @ExcelProperty(name = "ns1:agent_id")
    protected String agent_id;
    @ExcelProperty(name = "ns1:customer_type")
    protected String customer_type;
    protected Customer customer;
    protected Error error;
}
