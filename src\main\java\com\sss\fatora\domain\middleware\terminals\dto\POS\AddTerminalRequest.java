package com.sss.fatora.domain.middleware.terminals.dto.POS;

import com.sss.fatora.domain.middleware.Address;
import com.sss.fatora.domain.middleware.terminals.dto.TerminalDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;


@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AddTerminalRequest extends TerminalDto {
    private String serialNumber;
    //Third Stage
    private List<Map<String,String>> pairs;//list of terminal ids and serial numbers of POS

    @Length(min = 8,max = 8)
    private String terminalId;
    private String latitude;
    private String longitude;
}
