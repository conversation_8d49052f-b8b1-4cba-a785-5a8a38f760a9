package com.sss.fatora.security.service;

import com.sss.fatora.security.model.CustomUserDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class TokenBuildService {

    public Map<String, Object> buildUserDetails(CustomUserDetails customUserDetails) {
        Map<String, Object> userDetails = new HashMap<String, Object>();

        userDetails.put("email", customUserDetails.getApplicationUser().getEmail());
        userDetails.put("userId", customUserDetails.getApplicationUser().getId());
        userDetails.put("mobile", customUserDetails.getMobile());
        return userDetails;
    }

}
