package com.sss.fatora.controller.read;

import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.local.SoapResponse;
import com.sss.fatora.domain.read.AccountVW;
import com.sss.fatora.domain.read.CardsBo;
import com.sss.fatora.service.read.CardsBoService;
import com.sss.fatora.service.read.ReadAccountService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/account")
public class ReadAccountController {
    @Autowired
    ReadAccountService readAccountService;
    @Autowired
    CardsBoService cardsBoService;

    @RequestMapping(value = "/by-cardNo", method = RequestMethod.POST)
    public ResponseObject getAccountsByCardNumber(@ParameterName("cardNo") String cardNo) {

        List<AccountVW> accounts = readAccountService.getAccountsByCardNumber(cardNo);
        CardsBo cardsBo = cardsBoService.getCardBoByNumber(cardNo);
        Map<String, Object> response = new HashMap<>();
        if (cardsBo != null)
            response.put("cardBranch", cardsBo.getAgentNumber());
        else
            response.put("cardBranch", null);
        response.put("accounts", accounts);
        return response != null ?
                ResponseObject.FETCHED_SUCCESS(accounts, null) :
                ResponseObject.FETCHED_SUCCESS(null, null);
    }

}
