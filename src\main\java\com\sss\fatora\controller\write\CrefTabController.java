package com.sss.fatora.controller.write;

import com.sss.fatora.domain.local.SoapResponse;
import com.sss.fatora.domain.read.CardStatusVW;
import com.sss.fatora.service.read.CardStatusService;
import com.sss.fatora.service.write.CrefTabService;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/cref")
public class CrefTabController {

    final CrefTabService crefTabService;
    final CardStatusService cardStatusService;

    public CrefTabController(CrefTabService crefTabService, CardStatusService cardStatusService) {
        this.crefTabService = crefTabService;
        this.cardStatusService = cardStatusService;
    }

    @RequestMapping(value = "reset-pin", method = RequestMethod.PUT)
    public ResponseObject resetPin(@RequestParam("cardNo") String cardNo, @RequestParam("expDate") Long expDate) {
        Boolean crefTab = crefTabService.resetPin(cardNo, expDate);
        return crefTab != null ?
                ResponseObject.UPDATED_SUCCESS(crefTab, null) :
                ResponseObject.UPDATING_FAILED(null, null);
    }

    @RequestMapping(value = "change-status", method = RequestMethod.PUT)
    public ResponseObject changeCardStatus(@RequestParam("cardNo") String cardNo, @RequestParam("expDate") Long expDate, @RequestParam("status") Integer status,@RequestParam("oldStatus") String oldStatus) {
        Boolean crefTab = crefTabService.changeStatus(cardNo, expDate, status, oldStatus, null);
        return crefTab != null ?
                ResponseObject.UPDATED_SUCCESS(crefTab, null) :
                ResponseObject.UPDATING_FAILED(null, null);
    }

    @RequestMapping(value = "get-card-status", method = RequestMethod.GET)
    public ResponseObject getCardStatus() {
        List<CardStatusVW> cardStatusVWList = cardStatusService.getActiveChangeCardsStatus();
        return cardStatusVWList != null ?
                ResponseObject.FETCHED_SUCCESS(cardStatusVWList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "get-logs-card-status", method = RequestMethod.GET)
    public ResponseObject getCardStatusWithValid() {
        List<CardStatusVW> cardStatusVWList = cardStatusService.getActiveChangeCardsStatusWithValid();
        return cardStatusVWList != null ?
                ResponseObject.FETCHED_SUCCESS(cardStatusVWList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


}
