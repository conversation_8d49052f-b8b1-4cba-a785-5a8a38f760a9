package com.sss.fatora.dao.issuing;

import com.sss.fatora.domain.issuing.Person;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface PersonDao extends JpaRepository<Person,Long> {
    @Query("SELECT P FROM Person P " +
            "WHERE P.idNumber LIKE :idNumber AND P.idType LIKE :idType")
    Person getPersonByTypeAndNumber(@Param("idType")String personIdType, @Param("idNumber")String personIdNumber);

    @Query("SELECT P FROM Person P " +
            "WHERE P.personId =:personId")
    Person getByPersonId(@Param("personId")Long personId);

    @Query("SELECT P FROM Person P " +
            "WHERE P.personId =:personId")
    List<Person> getFirstByPersonId(@Param("personId")Long personId);
}
