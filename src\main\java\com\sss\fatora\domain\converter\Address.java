package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "command","address_type","country","address_name","house","apartment"})
public class Address extends GenericConverter{
    @ExcelProperty(name = "ns1:command18")
    private String command;
    @ExcelProperty(name = "ns1:address_type")
    private String address_type;
    @ExcelProperty(name = "ns1:country")
    private String country;
    private AddressName address_name;
    @ExcelProperty(name = "ns1:house")
    private String house;
    @ExcelProperty(name = "ns1:apartment")
    private String apartment;
}
