package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.RequestsLog;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface RequestsLogDao extends GenericDao<RequestsLog,Long> {

    List<RequestsLog> getLogsByRequestId(@Param("requestId") String requestId);
}
