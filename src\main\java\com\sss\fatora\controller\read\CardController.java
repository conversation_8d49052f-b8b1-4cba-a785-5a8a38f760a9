package com.sss.fatora.controller.read;
import com.sss.fatora.domain.converter.Card;
import com.sss.fatora.domain.local.SoapResponse;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.CardsBo;
import com.sss.fatora.domain.read.DTO.ActionResponse;
import com.sss.fatora.service.read.ReadCardService;
import com.sss.fatora.service.read.TransactionService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/card")
public class CardController {

    @Autowired
    ReadCardService readCardService;
    @Autowired
    TransactionService transactionService;

    @GetMapping
    public ResponseObject getCards() {

        List<CardDataVW> cards = readCardService.getCards();
        return ResponseObject.FETCHED_SUCCESS(cards, null);
    }

    @PreAuthorize("hasAnyAuthority('Admin','Search Cards')")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResponseObject search(@ParameterName(value = "filter", required = false) CardDataVW card,
                                 @ParameterName(value = "fromIssueDate", required = false) Long fromIssueDate,
                                 @ParameterName(value = "toIssueDate", required = false) Long toIssueDate,
                                 @ParameterName(value = "fromExpDate", required = false) Long fromExpDate,
                                 @ParameterName(value = "toExpDate", required = false) Long toExpDate,
                                 @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @RequestParam(value = "accountNumber", required = false) String accountNumber) throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {
        if (card == null)
            card = new CardDataVW();
        Page<CardDataVW> cards = readCardService.mainSearch(card, fromIssueDate, toIssueDate, fromExpDate, toExpDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(), accountNumber);
        //Page<CardDataVW> cards = readCardService.search(card,fromIssueDate,toIssueDate,pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),null);
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", cards != null ? cards.getTotalElements() : 0);
        return cards != null ?
                ResponseObject.FETCHED_SUCCESS(cards.getContent(), extra) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


    @RequestMapping(value = "/get-with-details", method = RequestMethod.POST)
    public ResponseObject getWithDetails(@RequestParam("recordId") String recordId) throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {

        CardDataVW filter = new CardDataVW();
        CardDataVW card = readCardService.getWithDetails(filter, recordId);
        return card != null ?
                ResponseObject.FETCHED_SUCCESS(card, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }


    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public ResponseObject export(@ParameterName(value = "filter", required = false) CardDataVW card,
                                 @ParameterName(value = "fromIssueDate", required = false) Long fromIssueDate,
                                 @ParameterName(value = "toIssueDate", required = false) Long toIssueDate,
                                 @ParameterName(value = "fromExpDate", required = false) Long fromExpDate,
                                 @ParameterName(value = "toExpDate", required = false) Long toExpDate,
                                 @ParameterName(value = "details") Boolean details,
                                 @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                                 @ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @ParameterName(value = "accountNumber", required = false) String accountNumber) throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {

        if (card == null)
            card = new CardDataVW();
        Page<CardDataVW> cards;
        cards = readCardService.export(card, fromIssueDate, toIssueDate, fromExpDate, toExpDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(), details, accountNumber);
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", cards != null ? cards.getTotalElements() : 0);
        return cards != null ?
                ResponseObject.FETCHED_SUCCESS(cards.getContent(), extra) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/change-mobile", method = RequestMethod.POST)
    public ResponseObject changeMobileNumber(@RequestParam(value = "mobNm") String mobileNumber,
                                             @RequestParam(value = "oldMobileNumber") String oldMobileNumber,
                                             @RequestParam(value = "cardNo") String cardNo) {
        ResponseEntity <ActionResponse> response = readCardService.changeMobileNumber(null,cardNo, mobileNumber , oldMobileNumber);
        return response != null ?
                ResponseObject.FETCHED_SUCCESS(response, null) :
                ResponseObject.RESPONSE_WITH_ERRORS();
    }

    @RequestMapping(value = "/change-product", method = RequestMethod.POST)
    public ResponseObject changeProduct(@RequestParam(value = "productId") Long productId,
                                        @RequestParam(value = "oldProduct") String oldProduct,
                                        @RequestParam(value = "newProduct") String newProduct,
                                        @RequestParam(value = "cardNo") String cardNo) {
        ResponseEntity <ActionResponse> response = readCardService.changeProduct(null,cardNo ,oldProduct ,newProduct ,productId);
        return response != null ?
                ResponseObject.FETCHED_SUCCESS(response, null) :
                ResponseObject.RESPONSE_WITH_ERRORS();
    }
    // Long actionRequestId, String cardNo, String expiryDate, String deliveryBranch,int instantCard,int oldExpiry,int oldCardNumber
    // @PreAuthorize("hasAnyAuthority('Admin','Search Cards')")
    @RequestMapping(value = "/reissue", method = RequestMethod.POST)
    public ResponseEntity<ActionResponse> reissue(
                                @ParameterName(value = "actionRequestId", required = false) Long actionRequestId,
                                 @ParameterName(value = "cardNo", required = false) String cardNo,
                                 @ParameterName(value = "expiryDate", required = false) String expiryDate,
                                 @ParameterName(value = "deliveryBranch", required = false) String deliveryBranch,
                                 @ParameterName(value = "instantCard", required = false) int instantCard,
                                 @ParameterName(value = "oldExpiry", required = false) int oldExpiry,
                                 @ParameterName(value = "oldCardNumber", required = false) int oldCardNumber,
                                 @ParameterName(value = "oldValue", required = false) String oldValue,
                                 @ParameterName(value = "newValue", required = false) String newValue,
                                 @RequestParam(value = "accountNumber", required = false) String accountNumber) throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {
        // if (card == null)
        //     card = new CardsBo();
        ResponseEntity<ActionResponse> cards = readCardService.reissuecard( actionRequestId,  cardNo,  expiryDate,  deliveryBranch, instantCard, oldExpiry, oldCardNumber,oldValue,newValue);
        //Page<CardDataVW> cards = readCardService.search(card,fromIssueDate,toIssueDate,pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),null);
        // HashMap<String, Object> extra = new HashMap<>();
        // extra.put("count", cards != null ? cards.getTotalElements() : 0);
        return cards;
    }
    @RequestMapping(value = "/renew", method = RequestMethod.POST)
    public ResponseEntity<ActionResponse> renewcard(
                                @ParameterName(value = "actionRequestId", required = false) Long actionRequestId,
                                 @ParameterName(value = "cardNo", required = false) String cardNo,
                                 @ParameterName(value = "expiryDate", required = false) String expiryDate,
                                 @ParameterName(value = "deliveryBranch", required = false) String deliveryBranch,
                                 @ParameterName(value = "instantCard", required = false) int instantCard,
                                 @ParameterName(value = "oldExpiry", required = false) int oldExpiry,
                                 @ParameterName(value = "oldCardNumber", required = false) int oldCardNumber,
                                 @ParameterName(value = "oldValue", required = false) String oldValue,
                                 @ParameterName(value = "newValue", required = false) String newValue,
                                 @RequestParam(value = "accountNumber", required = false) String accountNumber) throws InvocationTargetException, IntrospectionException, IllegalAccessException, ParseException, NoSuchFieldException {
        // if (card == null)
        //     card = new CardsBo();
        ResponseEntity<ActionResponse> cards = readCardService.renewcard( actionRequestId,  cardNo,  expiryDate,  deliveryBranch, instantCard, oldExpiry, oldCardNumber,oldValue,newValue);
        //Page<CardDataVW> cards = readCardService.search(card,fromIssueDate,toIssueDate,pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),null);
        // HashMap<String, Object> extra = new HashMap<>();
        // extra.put("count", cards != null ? cards.getTotalElements() : 0);
        return cards;
    }
}
