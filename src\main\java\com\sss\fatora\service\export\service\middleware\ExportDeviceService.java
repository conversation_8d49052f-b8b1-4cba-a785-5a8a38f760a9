package com.sss.fatora.service.export.service.middleware;

import com.sss.fatora.domain.middleware.device.Device;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.read.FatoraBankService;
import com.sss.fatora.utils.service.ContentUtilService;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ExportDeviceService {
    private final ConfigService configService;
    final ContentUtilService contentUtilService;
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private final FatoraBankService fatoraBankService;

    public ExportDeviceService(ConfigService configService, ContentUtilService contentUtilService, FatoraBankService fatoraBankService) {
        this.configService = configService;
        this.contentUtilService = contentUtilService;
        this.fatoraBankService = fatoraBankService;
    }

    void initWorkbook() {
        workbook = new SXSSFWorkbook();
        this.sheet = workbook.createSheet("Devices");

    }

    private List<String> projection;

    private void writeHeaderLine(List<String> headers) {
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setBold(true);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);
        style.setBorderBottom(BorderStyle.THICK);
        style.setBorderTop(BorderStyle.THICK);
        int i = 0;
//        if (details)
//            projection = new ArrayList<>(configService.getDeviceWithDetailsHeaders());
//        else
        projection = new ArrayList<>(headers);
        for (String iterator : projection) {
            createCell(row, i++, iterator, style);
        }
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        sheet.setColumnWidth(columnCount, 8000);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue((String) value);
        }
        cell.setCellStyle(style);
    }

    private void writeDataLines(List<Device> deviceList, List<String> fields) {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THICK);
        style.setBorderRight(BorderStyle.THICK);
        style.setFont(font);
        for (Device device : deviceList) {
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            String bankName = fatoraBankService.getBankNameByCode(device.getBank()) != null ?
                    fatoraBankService.getBankNameByCode(device.getBank()) : device.getBank();
            device.setBank(bankName);
            setupRowByType(style, row, columnCount, fields, device);
        }
    }

    private void setupRowByType(CellStyle style, Row row, Integer columnCount, List<String> fields, Device device) {
//        List<String> fields;
//        if (details)
//            fields = configService.getDeviceWithDetailsColumns();
//        else
//            fields = configService.getDeviceMainColumns();
        for (String field : fields) {
            Object value = null;
            try {
//                if (field.equalsIgnoreCase("simId") || field.equalsIgnoreCase("terminalId"))
//                    continue;
                Field declaredField = device.getClass().getDeclaredField(field);
                declaredField.setAccessible(true);
                value = declaredField.get(device);
                if(value!=null)
                value = processDate(value);

            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
            createCell(row, columnCount++, value, style);
        }
    }

    private Object processDate(Object value) {
        Boolean isDate = false;
        Date dateObject = null;
        try {
            dateObject = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(value.toString());
            isDate = true;
        } catch (ParseException e) { }
        if (isDate)
            value = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateObject);
        return value;
    }

    public void export(List<Device> deviceList, HttpServletResponse response, List<String> headers, List<String> fields) throws IOException {
        initWorkbook();
        writeHeaderLine(headers);
        writeDataLines(deviceList, fields);
        File file = contentUtilService.makeExcelFile();
        FileOutputStream fileOutputStream = new FileOutputStream(file.getPath());
        workbook.write(fileOutputStream);
        workbook.dispose();
        workbook = null;
        fileOutputStream.flush();
        fileOutputStream.close();
        byte[] cards = FileUtils.readFileToByteArray(file);
        response.setContentLength(cards.length);
        response.getOutputStream().write(cards);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

}
