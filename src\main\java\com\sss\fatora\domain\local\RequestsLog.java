package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;
@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "Requests_Log")
public class RequestsLog extends LocalDomain{

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "Action")
        @Nationalized
        private String action;

        @Column(name = "Request_Id")
        private String requestId;

        @Column(name = "User_Name")
        private String userName;

//        @Column(name = "Action_Value", length = 10000)
//        @Nationalized
//        private String actionValue;

//        @ManyToOne
//        @JoinColumn(name = "User_Id" , referencedColumnName = "id")
//        @JsonIgnoreProperties("logs")
//        private ApplicationUser applicationUser;

        @Override
        protected <T extends GenericDao> Class<T> defineDao() {
            return null;
        }

}
