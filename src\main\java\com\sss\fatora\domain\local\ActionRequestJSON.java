package com.sss.fatora.domain.local;
import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.converter.GenericConverter;
import com.sss.fatora.domain.read.DTO.AccountDTO;
import com.sss.fatora.domain.read.DTO.ReissueCardDTO;
import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class ActionRequestJSON extends GenericConverter {

    private Long id;
    private String oldValue;
    @ExcelProperty( name = "STATUS")
    private String newValue;
    private Long expDate;
    private Integer status;
    private Integer productID;
    private Account account;
    private AccountDTO accountDTO;
    private ReissueCardDTO reissueCardDTO;
}
