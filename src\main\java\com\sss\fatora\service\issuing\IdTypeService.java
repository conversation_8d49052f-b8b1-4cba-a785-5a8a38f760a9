package com.sss.fatora.service.issuing;

import com.sss.fatora.dao.issuing.IdTypeDao;
import com.sss.fatora.domain.issuing.IdType;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IdTypeService {
    final IdTypeDao idTypeDao;

    public IdTypeService(IdTypeDao idTypeDao){
        this.idTypeDao = idTypeDao;
    }

    /**
     * This Function Get All IdTypes
     * Note : IdType Example Are (Passport , NationalId)
     * */
    public List<IdType> getIdTypes(){
        return idTypeDao.getAllIdTypes();
    }
}
