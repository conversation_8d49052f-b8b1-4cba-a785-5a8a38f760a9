package com.sss.fatora.domain.middleware.terminals;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class TerminalResponse {

    private Result result;
    private String targetUrl;
    private Boolean success;
    private ResponseError error;
    private Boolean unAuthorizedRequest;
    private Boolean __abp;
}
