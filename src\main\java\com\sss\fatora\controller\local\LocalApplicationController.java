package com.sss.fatora.controller.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.LocalApplicationDao;
import com.sss.fatora.domain.converter.Application;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.LocalApplicationService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/application")
public class LocalApplicationController extends GenericController<LocalApplicationService, LocalApplicationDao, LocalApplication, Integer> {

    @Autowired
    ContentUtilService contentUtilService;

    public LocalApplicationController() {

    }

    @RequestMapping(value = "/get-form-info", method = RequestMethod.GET)
    public ResponseObject getFormInfo() {
        Map formInfo = service.getFormInfo();
        return formInfo != null ?
                ResponseObject.FETCHED_SUCCESS(formInfo, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @PreAuthorize("hasAnyAuthority('Admin','Submit Cards')")
    @RequestMapping(value = "/request-card", method = RequestMethod.PUT)
    public ResponseObject requestCard(@ParameterName("applicationList") List<LocalApplication> applicationList) throws Exception {
        List<LocalApplication> localApplications = service.requestCard(applicationList);
        if(CustomUserDetails.getCurrentInstance().getErrorsList().size() > 0)
        return ResponseObject.FETCHED_SUCCESS_WITH_ERRORS(localApplications, null);
               else return ResponseObject.FETCHED_SUCCESS(localApplications,null);
    }


    @RequestMapping(value = "/save-draft", method = RequestMethod.POST)
    public ResponseObject saveDraft(@ParameterName("application") Application application) throws Exception {
        LocalApplication localApplication = service.saveDraft(application);
        return localApplication != null ?
                ResponseObject.ADDED_SUCCESS(localApplication, null) :
                ResponseObject.RESPONSE_WITH_ERRORS();
    }

    @RequestMapping(value = "/import-draft1", method = RequestMethod.POST)
    public ResponseObject importDraft1(@RequestParam(value = "content", required = false) MultipartFile multipartFile, HttpServletResponse response) throws Exception {
        int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
        String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);

        TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                multipartFile.getContentType(), extension);
        if (tempContentModel == null)
            return new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        List<LocalApplication> localApplications = service.saveDraftFromExcel(tempContentModel);
        return localApplications != null ?
                ResponseObject.ADDED_SUCCESS(localApplications, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }

    @RequestMapping(value = "/import-draft", method = RequestMethod.POST)
    public ResponseObject importDraft(@ParameterName("file") TempContentModel tempContentModel, HttpServletResponse response) throws Exception {
        List<LocalApplication> localApplications = service.saveDraftFromExcel(tempContentModel);
        return localApplications != null ?
                ResponseObject.ADDED_SUCCESS(localApplications, null) :
                ResponseObject.ADDING_FAILED(null, null);
    }


    @RequestMapping(value = "/edit-draft", method = RequestMethod.PUT)
    public ResponseObject editDraft(@ParameterName("application") LocalApplication application) throws Exception {
        LocalApplication localApplication = service.editDraft(application);
        return localApplication != null ?
                ResponseObject.UPDATED_SUCCESS(localApplication, null) :
                ResponseObject.RESPONSE_WITH_ERRORS();
    }

    @RequestMapping(value = "/get-applications", method = RequestMethod.POST)
    public ResponseObject getApplicationsByStatus(@ParameterName(value = "pagination", required = false) Pagination pagination,
                                                  @ParameterName("status") String status) throws Exception {
        Long countPage = new Long(0);
        Page<LocalApplication> applicationList = service.getApplicationByStatus(status, pagination, countPage);

        Map<String, Object> count = new HashMap<>();
        count.put("count", applicationList.getTotalElements());
        return applicationList != null ?
                ResponseObject.FETCHED_SUCCESS(applicationList.getContent(), count) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/get-submitted", method = RequestMethod.POST)
    public ResponseObject getSubmittedAppliations(@ParameterName(value = "pagination", required = false) Pagination pagination) throws Exception {
        Long countPage = new Long(0);
        Page<LocalApplication> applicationList = service.getSubmittedApplications(pagination, countPage);

        Map<String, Object> count = new HashMap<>();
        count.put("count", applicationList.getTotalElements());
        return applicationList != null ?
                ResponseObject.FETCHED_SUCCESS(applicationList.getContent(), count) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/archive-applications", method = RequestMethod.PUT)
    public ResponseObject archiveApplications(@ParameterName(value = "idList") List<Long> idList) throws Exception {
        Long countPage = new Long(0);
        List<LocalApplication> archiveApplications = service.moveToArchive(idList);

        return archiveApplications != null ?
                ResponseObject.FETCHED_SUCCESS(idList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/move-to-draft", method = RequestMethod.PUT)
    public ResponseObject moveToDraft(@ParameterName(value = "idList") List<Long> idList) {
        List<LocalApplication> draftApplications = service.moveToDraft(idList);

        return draftApplications != null ?
                ResponseObject.FETCHED_SUCCESS(idList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/delete-applications", method = RequestMethod.POST)
    public ResponseObject deleteApplications(@ParameterName(value = "applicationList") List<LocalApplication> applicationList) throws Exception {

        Boolean delete = service.deleteApplications(applicationList);

        return delete ?
                ResponseObject.DELETED_SUCCESS(delete, null) :
                ResponseObject.DELETING_FAILED(null, null);
    }

    @PostMapping(value = "/search")
    public ResponseObject searchArchived(@ParameterName(value = "filter", required = false) LocalApplication localApplication,
                                         @ParameterName(value = "fromDate", required = false) Long fromDate,
                                         @ParameterName(value = "toDate", required = false) Long toDate,
                                         @ParameterName(value = "pagination", required = false) Pagination pagination,
                                         @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException, JsonProcessingException {
        if (localApplication == null)
            localApplication = new LocalApplication();
        Page<LocalApplication> localApplications = service.mainSearch(localApplication, fromDate, toDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", localApplications != null ? localApplications.getTotalElements() : 0);
        return localApplications!=null?
                ResponseObject.FETCHED_SUCCESS(localApplications.getContent(), extra):
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
