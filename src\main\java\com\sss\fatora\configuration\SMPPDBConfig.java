package com.sss.fatora.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "smppDBEntityManagerFactory",
        transactionManagerRef = "smppDBTransactionManager",
        basePackages = "com.sss.fatora.dao.smpp"
)
public class SMPPDBConfig {
    @Autowired
    Environment env;

    @Bean(name = "smppDBDataSourceProperties")
    @ConfigurationProperties("spring.datasource-smpp-db")
    public DataSourceProperties smppDBDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "smppDBDataSource")
    public DataSource smppDBDataSource() {
        DataSourceProperties smppDBDataSourceProperties = smppDBDataSourceProperties();
        return DataSourceBuilder.create()
                .driverClassName(smppDBDataSourceProperties.getDriverClassName())
                .url(smppDBDataSourceProperties.getUrl())
                .username(smppDBDataSourceProperties.getUsername())
                .password(smppDBDataSourceProperties.getPassword())
                .build();
    }

    @Bean(name = "smppDBEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            @Qualifier("readingDBEntityManagerFactoryBuilder") EntityManagerFactoryBuilder smppDBEntityManagerFactoryBuilder) {

        Map<String, String> JpaProperties = new HashMap<>();

        JpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.properties.hibernate.format_sql"));
        JpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
        JpaProperties.put("hibernate.hbm2ddl.auto", "none");
        JpaProperties.put("hibernate.dialect", env.getProperty("spring.datasource-smpp-db.hibernate.dialect"));

        return smppDBEntityManagerFactoryBuilder
                .dataSource(smppDBDataSource())
                .packages("com.sss.fatora.domain.smpp")
                //.persistenceUnit("secondaryDataSource")
                .properties(JpaProperties)
                .build();
    }

    @Bean(name = "smppDBTransactionManager")
    public PlatformTransactionManager smppDBTransactionManager(
            @Qualifier("smppDBEntityManagerFactory") EntityManagerFactory smppDBEntityManagerFactory) {
        return new JpaTransactionManager(smppDBEntityManagerFactory);
    }

}
