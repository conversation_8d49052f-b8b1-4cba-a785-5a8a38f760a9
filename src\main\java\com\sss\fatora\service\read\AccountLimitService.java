package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.AccountLimitDao;
import com.sss.fatora.domain.read.AccountLimit;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AccountLimitService extends GenericReadService<AccountLimitDao, AccountLimit,Integer> {
    public List<AccountLimit> getByAccountNumber(String accountNumber) {
       return dao.getByAccountNumber(accountNumber);
    }
}
