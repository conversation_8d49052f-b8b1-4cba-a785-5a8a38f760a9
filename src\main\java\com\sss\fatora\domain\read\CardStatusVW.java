package com.sss.fatora.domain.read;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_CARD_STATUS")
public class CardStatusVW {
    @Id
    @Column(name = "CD_STAT")
    private Long cdStat;

    @Column(name = "DESCX")
    private String descX;
    @Column(name = "IS_ACTIVE")
    private Integer active;
    @Column(name = "FP_CHANGE_STATUS")
    private Integer fpChangeStatus;


}
