package com.sss.fatora.domain.middleware.terminals.dto.EPOS;

import com.sss.fatora.domain.middleware.terminals.dto.POS.AddTerminalRequest;
import com.sss.fatora.domain.middleware.terminals.dto.TerminalDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AddEPosRequest extends TerminalDto {
    private String projectManagerName;
    private String projectManagerEmail;
    private String projectManagerMobile;
    private String technicalManagerName;
    private String technicalManagerEmail;
    private String technicalManagerMobile;

    private List<String> terminalsIds;
    private String IPAddress;
}
