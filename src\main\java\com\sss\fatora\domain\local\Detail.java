package com.sss.fatora.domain.local;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

import java.util.List;

public class Detail {
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "details")
    List<String> details;

    public List<String> getDetails() {
        return details;
    }

    public void setDetails(List<String> details) {
        this.details = details;
    }
}
