package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalPersonDTO {

    @JsonProperty(value = "Id_Type")
    String idType;
    @JsonProperty(value = "Id_Number")
    String idNumber;
    @JsonProperty(value = "First_Name")
    String firstName;
    @JsonProperty(value = "Last_Name")
    String lastName;
    @JsonProperty(value = "Father_Name")
    String fatherName;
    @JsonProperty(value = "Mother_Name")
    String motherName;
    @JsonProperty(value = "Gender")
    String gender;
    @JsonProperty(value = "Date_of_Birth")
    Date dateOfBirth;
    @JsonProperty(value = "Place_of_Birth")
    String placeOfBirth;
}
