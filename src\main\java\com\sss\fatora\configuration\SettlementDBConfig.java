package com.sss.fatora.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "settlementDBEntityManagerFactory",
        transactionManagerRef = "settlementDBTransactionManager",
        basePackages = "com.sss.fatora.dao.settlement"
)
public class SettlementDBConfig {

    @Autowired
    Environment env;

    @Bean(name = "settlementDBDataSourceProperties")
    @ConfigurationProperties("spring.datasource-settlement-db")
    public DataSourceProperties settlementDBDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "settlementDBDataSource")
    public DataSource settlementDBDataSource() {
        DataSourceProperties settlementDBDataSourceProperties = settlementDBDataSourceProperties();
        return DataSourceBuilder.create()
                .driverClassName(settlementDBDataSourceProperties.getDriverClassName())
                .url(settlementDBDataSourceProperties.getUrl())
                .username(settlementDBDataSourceProperties.getUsername())
                .password(settlementDBDataSourceProperties.getPassword())
                .build();
    }

    @Bean(name = "settlementDBEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            @Qualifier("readingDBEntityManagerFactoryBuilder") EntityManagerFactoryBuilder settlementDBEntityManagerFactoryBuilder) {

        Map<String, String> JpaProperties = new HashMap<>();

        JpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.properties.hibernate.format_sql"));
        JpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
        JpaProperties.put("hibernate.hbm2ddl.auto", "update");
        JpaProperties.put("hibernate.dialect", env.getProperty("spring.datasource-settlement-db.hibernate.dialect"));

        return settlementDBEntityManagerFactoryBuilder
                .dataSource(settlementDBDataSource())
                .packages("com.sss.fatora.domain.settlement")
                //.persistenceUnit("secondaryDataSource")
                .properties(JpaProperties)
                .build();
    }

    @Bean(name = "settlementDBTransactionManager")
    public PlatformTransactionManager settlementDBTransactionManager(
            @Qualifier("settlementDBEntityManagerFactory") EntityManagerFactory settlementDBEntityManagerFactory) {
        return new JpaTransactionManager(settlementDBEntityManagerFactory);
    }

}
