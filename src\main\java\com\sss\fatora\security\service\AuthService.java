package com.sss.fatora.security.service;

import com.fasterxml.jackson.core.JsonProcessingException;

import com.sss.fatora.domain.local.ApplicationUserPrivilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ApplicationUserPrivilegeService;
import com.sss.fatora.utils.model.HibernateAwareObjectMapper;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@Service
public class AuthService {
    final ApplicationUserPrivilegeService applicationUserPrivilegeService;
    @Autowired
    Environment environment;

    @Autowired
    public AuthService(ApplicationUserPrivilegeService applicationUserPrivilegeService) {
        this.applicationUserPrivilegeService = applicationUserPrivilegeService;
    }

    public void buildLoginSuccessResponse(HttpServletRequest request, HttpServletResponse servletResponse,
                                          CustomUserDetails userDetails) {
        Map<String, Object> extraData = new HashMap<>();
        HibernateAwareObjectMapper mapper = new HibernateAwareObjectMapper();
        Set<ApplicationUserPrivilege> applicationUserPrivileges = applicationUserPrivilegeService.getAllPrivilegeForUser(userDetails.getApplicationUser().getId());
        userDetails.getApplicationUser().setApplicationUserPrivileges(applicationUserPrivileges);
        extraData.put("server_url", getServerURL());
        try {
            String response = mapper.writeValueAsString(ResponseObject.LOGGED_IN(userDetails.getApplicationUser(), extraData));
            servletResponse.setContentType("application/json");
            servletResponse.getWriter().print(response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void buildLoginFailedResponse(HttpServletRequest request, HttpServletResponse servletResponse,
                                         String msg) {
        HibernateAwareObjectMapper mapper = new HibernateAwareObjectMapper();
        try {
            String response = mapper.writeValueAsString(ResponseObject.LOGIN_FAILED(msg, null));
            servletResponse.setContentType("application/json");
            servletResponse.getWriter().print(response);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String getServerURL() {

        Object bank= RequestContextHolder.getRequestAttributes().getAttribute("bank", 1);
        if (bank != null && bank.toString().equals("SIIB"))
            return environment.getProperty("SIIB_SERVER_URL");
        else
            return environment.getProperty("FATORA_SERVER_URL");

    }
}
