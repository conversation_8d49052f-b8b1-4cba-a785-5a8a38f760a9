package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.ProductsDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.read.Products;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("readingDBTransactionManager")
public class ProductsService extends GenericReadService<ProductsDao, Products, Long> {

    /**
     * This Function Get All Products For A Bank Of The User That Request The Function
     */
    public List<Products> getAllProductsByBankCode() {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            String bankCode = "";
            if (applicationUser.getBank() != null) {
                if (applicationUser.getBank().getPrefix().equals("SIIB"))
                    return dao.findAll();
                else {
                    bankCode = applicationUser.getBank().getCode();
                    return dao.getAllProductsByBankCode(bankCode);
                }
            } else
                return dao.findAll();

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    /**
     * @param productId This Parameter Is For The Id Of The Product
     *                  <p>
     *                  This Function Get A Product By Id
     */
    public Products getProductById(Long productId) {
        try {
            return dao.getProductById(productId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @param productNumber This Parameter Is For The Number Of The Product
     *                      <p>
     *                      This Function Get A Bank Code By Product Number
     */
    public String getBankCodeByProductNumber(Long productNumber) {
        return dao.getBankCodeByProductNumber(productNumber);
    }

    public Long getProductNumberByLabel(String productOldName) {
        return dao.getProductNumberByLabel(productOldName);
    }
}
