// package com.sss.fatora.service.local;

// import com.sss.fatora.dao.local.AcquiringRequestsLogDao;
// import com.sss.fatora.domain.local.*;
// import com.sss.fatora.service.generic.GenericService;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
// import java.util.List;

// @Service
// @Transactional("localDBTransactionManager")
// public class AcquiringRequestsLogService extends GenericService<AcquiringRequestsLogDao, AcquiringRequestsLog, Long> {

//         /**
//          * @param requestId This Parameter Is For User Id
//          *               <p>
//          *               This Function Get All Logs For This RequestAction_Request
//          */
//         public List<AcquiringRequestsLog> getAcquiringRequestsLogs(String requestId) {
//             return dao.getAcquiringRequestsLogs(requestId);
//         }



// }
