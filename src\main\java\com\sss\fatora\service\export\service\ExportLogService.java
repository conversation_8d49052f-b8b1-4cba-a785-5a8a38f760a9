package com.sss.fatora.service.export.service;

import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.domain.local.Log;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.LogType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ExportLogService {
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ExportService exportService;
    final ConfigService configService;
    final ContentUtilService contentUtilService;

    public ExportLogService(PaginationService paginationService, PrivilegeService privilegeService, ExportService exportService, ConfigService configService, ContentUtilService contentUtilService) {
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.exportService = exportService;
        this.configService = configService;
        this.contentUtilService = contentUtilService;
    }

    /**
     * @param log             This Parameter Is For The Value Of Filters Send From The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To Search In
     * @param filterOperator  There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     *                        <p>
     *                        This Function Add Certain Conditions To Where Clause That Can't Be Added To The Filter (Log)
     *                        Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     */
    public byte[] search(Log log, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        Page<Log> logs;
        String additionalConstraint = "";
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();

        if (log == null)
            log = new Log();

        if (fromDate == null) {
            fromDate = new Date(1612178075113L).getTime();
        }
        if (toDate == null) {
            toDate = new Date(7258118400L * 1000).getTime();
        }
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");

        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            List<Privilege> privileges = privilegeService.getPrivilegesById(PrivilegeType.LOG_PRIVILEGE.getType(), applicationUser.getId());
            if (!log.getType().equalsIgnoreCase(LogType.Actions.getType()) && !log.getType().equalsIgnoreCase(LogType.Requests.getType()) && !privileges.stream().anyMatch(privilege ->
                    privilege.getName().equalsIgnoreCase("View All Logs"))) {
                log.setBank(new Bank(applicationUser.getBank().getId()));
            }

            additionalConstraint = "Log.creationDate BETWEEN '" + f.format(DateUtils.getStartDayOfDate(fromDate)) + "' AND '" + f.format(DateUtils.getEndDayOfDate(toDate)) + "'";


            if (log.getType().equalsIgnoreCase(LogType.Actions.getType()) ||log.getType().equalsIgnoreCase(LogType.Requests.getType()))
                additionalConstraint += " AND ( Log.bank = " + applicationUser.getBank().getId()
                        + " or " + getLogsByUserBankBins(applicationUser) + " ) ";
            else
                additionalConstraint += " AND Log.bank = " + applicationUser.getBank().getId();

        } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()))
            additionalConstraint = "Log.creationDate between '" + f.format(DateUtils.getStartDayOfDate(fromDate)) + "' AND '" + f.format(DateUtils.getEndDayOfDate(toDate)) + "'";

        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        } else {
            pagination.setSize(applicationUser.getMaxExportLimit());
        }
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("Log.creationDate");
            pagination.setOrderType("DESC");
        }
        File file = contentUtilService.makeExcelFile();
        TempContentModel tempContentModel = contentUtilService.makeTempModel(file);

        List<String> columns = setColumns(log);
        List<String> headers = setHeaders(log);

        if (log.getBank() == null)
            log.setBank(new Bank());
        if (log.getApplicationUser() == null)
            log.setApplicationUser(new ApplicationUser());
        log.getApplicationUser().setForcePasswordUpdated(null);
        logs = exportService.dynamicSearch(log
                , pagination
                , additionalConstraint
                , filterOperator
                , filterProjectionList()
                , file.getPath()
                , headers
                , columns);

        return contentUtilService.fetchAsBytes(tempContentModel);
    }

    /**
     * @param log This Parameter Is For Checking The Type Of Log
     *            <p>
     *            This Function Return The Headers Of The Excel File This Depend On Type Of Log
     */
    private List<String> setHeaders(Log log) {
        if (log.getType().equalsIgnoreCase("searches")) {
            return configService.getLogsSearchesHeaders();
        } else if (log.getType().equalsIgnoreCase("details")) {
            return configService.getLogsDetailsHeaders();
        } else if (log.getType().equalsIgnoreCase("actions")) {
            return configService.getLogsActionsHeaders();
        } else if (log.getType().equalsIgnoreCase("requests")) {
            return configService.getLogsRequestsHeaders();
        } else {
            return configService.getLogsExportsHeaders();
        }
    }

    /**
     * @param log This Parameter Is For Checking The Type Of Log
     *            <p>
     *            This Function Return The Columns Of The Excel File This Depend On Type Of Log
     */
    private List<String> setColumns(Log log) {
        if (log.getType().equalsIgnoreCase("searches")) {
            return configService.getLogsSearchesColumns();
        } else if (log.getType().equalsIgnoreCase("details")) {
            return configService.getLogsDetailsColumns();
        } else if (log.getType().equalsIgnoreCase("actions")) {
            return configService.getLogsActionsColumns();
        } else if (log.getType().equalsIgnoreCase("requests")) {
            return configService.getLogsRequestsColumns();
        } else {
            return configService.getLogsExportsColumns();
        }
    }

    /**
     * This Function Get All Columns Of Logs And Change Them To Be Like
     * (TableName.Column As Column) To Be Used In The Select Section Of The Query
     */
    private List<String> filterProjectionList() {
        List<String> projection = new ArrayList<>(configService.getLogsColumns());
        projection = projection.stream()
                .distinct()
                //.filter(privileges::contains)
                .map(s -> "Log." + s + " AS " + s)
                .collect(Collectors.toList());
        projection.add("bank.fullName as bankName");
        projection.add("applicationUser.fullName as fullName");
        return projection;
    }

    private String getLogsByUserBankBins(ApplicationUser applicationUser) {
        String additionalConstraint = "";
        String BinString = applicationUser.getBank().getBin();
        String[] Bins = BinString.split(",");
        for (int BinCount = 0; BinCount < Bins.length; BinCount++) {
            String Bin = Bins[BinCount];
            if (BinCount < Bins.length - 1)
                additionalConstraint += "Log.entityId like  '" + Bin + "%' or ";
            else
                additionalConstraint += "Log.entityId like  '" + Bin + "%' ";
        }
        return additionalConstraint;
    }
}
