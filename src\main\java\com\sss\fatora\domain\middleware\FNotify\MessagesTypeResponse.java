package com.sss.fatora.domain.middleware.FNotify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class MessagesTypeResponse {

    @JsonProperty("MessageTypes")
    private List<String> MessageTypes;

    @JsonProperty("ErrorMessage")
    private String ErrorMessage;
}
