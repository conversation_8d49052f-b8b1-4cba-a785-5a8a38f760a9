package com.sss.fatora.utils.controller;

import com.sss.fatora.utils.model.ResponseObject;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;


@RestController
@RequestMapping("/content-util/")
public class ContentUtilsController {

    private final ContentUtilService contentUtilService;

    private final String[] illegalExtensions = new String[]{"exe", "jar", "bat"};

    @Autowired
    public ContentUtilsController(ContentUtilService contentUtilService) {
        this.contentUtilService = contentUtilService;
    }

    @RequestMapping(value = "upload", method = RequestMethod.POST)
    public ResponseObject uploadToServer(@RequestParam(value = "content", required = false) MultipartFile multipartFile) throws IOException, IllegalAccessException {
        try {
            Assert.notNull(multipartFile, "upload_null_file");
            int indexOfDelimiter = multipartFile.getOriginalFilename().lastIndexOf(".");
            String extension = multipartFile.getOriginalFilename().substring(indexOfDelimiter + 1);
            if (Arrays.asList(illegalExtensions).stream().anyMatch(s -> s.equalsIgnoreCase(extension))) {
                throw new IllegalAccessException();
            }
            TempContentModel tempContentModel = contentUtilService.makeTempModel(multipartFile.getBytes(),
                    multipartFile.getContentType(), extension);
            return ResponseObject.ADDED_SUCCESS(tempContentModel, null);
        } catch (Exception exception) {
            throw exception;
        }

    }
}
