package com.sss.fatora.service.export.service;

import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.log.Log;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class ExportCustomerService {
    final PrivilegeService privilegeService;
    final ConfigService configService;
    final ExportService exportService;

    @Autowired
    ContentUtilService contentUtilService;

    public ExportCustomerService(PrivilegeService privilegeService, ConfigService configService, ExportService exportService) {
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.exportService = exportService;
    }

    /**
     * @param customer       This Parameter Is For The Value Of Filters Send From The Front-End
     * @param filterOperator There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param projection     This Is A List Of Columns For Select Statement In Search Query
     * @param details        This Parameter Is For Deciding If The Excel Is With Details
     *                       <p>
     *                       This Function Can Add Certain Conditions To Where Clause That Can't Be Added To The Filter (Customer)
     *                       Directly And Then Request The Dynamic Search Function ( dynamicSearch() )
     */
    public byte[] search(Customer customer, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection, Boolean details) throws Exception {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            String additionalConstraint = "";
            Page<Customer> customers;
            if (pagination == null)
                pagination = new Pagination();
            if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
                String bankPrefix = applicationUser.getBank().getPrefix();
                if(!bankPrefix.equals("SIIB"))
                additionalConstraint += " substring(Customer.customerNumber,0,4) LIKE '" + bankPrefix + "' ";
            }
            if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
                pagination.setOrderBy("Customer.id");
                pagination.setOrderType("DESC");
            }
            File file = contentUtilService.makeExcelFile();
            TempContentModel tempContentModel = contentUtilService.makeTempModel(file);
            if (details == false) {
                customers = exportService.dynamicSearch(customer
                        , pagination
                        , additionalConstraint
                        , filterOperator
                        , projection
                        , file.getPath()
                        , configService.getCustomerHeaders()
                        , configService.getCustomerColumns());

                return contentUtilService.fetchAsBytes(tempContentModel);
            } else {
                customers = exportService.dynamicSearch(customer
                        , pagination
                        , additionalConstraint
                        , filterOperator
                        , projection
                        , file.getPath()
                        , configService.getCustomerHeadersWithDetails()
                        , configService.getCustomerColumnsWithDetails());

                return contentUtilService.fetchAsBytes(tempContentModel);
            }
        } catch (Exception exception) {
            throw exception;
        }
    }

    /**
     * @param customer        This Parameter Is For The Value Of Filters Send From The Front-End
     * @param filterOperators There Is An Specific Operator For Every Filter {@link com.sss.fatora.utils.constants.Operator}
     * @param details         This Parameter Is For Deciding If The Excel Is With Details
     *                        <p>
     *                        This Function Get UserPrivileges And Domain Columns And Then Request The Intersection Function
     *                        ( filterProjectionList() ) On Them And Then Request Search Function ( search() )
     */
    @Log(actionType = ActionType.CUSTOMER_EXPORT)
    public byte[] export(Customer customer, Pagination pagination, Map<String, Operator> filterOperators, Boolean details) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getCustomerMainColumns());
        if (details)
            projection.addAll(configService.getCustomerDetailsColumns());
        projection = filterProjectionList(projection, privileges);
        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        return search(customer, pagination, filterOperators, projection, details);

    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As Column) To Be Used In The
     *                   Select Section Of The Query
     */
    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> {
                            if (s.equalsIgnoreCase("customerIdType")) {
                                s = "IdType.name AS " + s;
                            } else if (s.equalsIgnoreCase("customerCountry"))
                                s = "CountryCodes.countryName AS " + s;
                            else if (s.equalsIgnoreCase("customerNationality"))
                                s = "NationalityCodes.countryName AS " + s;
                            else
                                s = "Customer." + s + " AS " + s;
                            return s;
                        }

                )
                .collect(Collectors.toList());
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This Specific Domain (Customer)
     */
    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.CUSTOMER_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }
}


