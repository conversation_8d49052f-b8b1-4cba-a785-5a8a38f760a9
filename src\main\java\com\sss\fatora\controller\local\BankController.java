package com.sss.fatora.controller.local;

import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.BankDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.service.local.BankService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import com.sss.fatora.utils.model.TempContentModel;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/bank")
public class BankController extends GenericController<BankService, BankDao, Bank, Integer> {

    @Override
    @PreAuthorize("hasAnyAuthority('Admin','Define Banks')")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResponseObject save(@ParameterName("domain") Bank bank,
                               @ParameterName(value = "tempContentModel",required = false) TempContentModel tempContentModel) throws Exception {
        try {
            Bank savedDomain = service.saveBankWithContent(bank,tempContentModel);
            return ResponseObject.ADDED_SUCCESS(savedDomain, null);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @PreAuthorize("hasAnyAuthority('Admin','Banks')")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResponseObject search(@ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @ParameterName(value = "bank", required = false) Bank bank) throws IntrospectionException, ParseException, IllegalAccessException, NoSuchFieldException, InvocationTargetException {

        Page<Bank> page = service.search(pagination, bank);
        Map<String, Object> count = new HashMap<>();
        count.put("count", page.getTotalElements());
        return ResponseObject.FETCHED_SUCCESS(page.getContent(), count);

    }

    @Override
    @PreAuthorize("hasAnyAuthority('Admin','Define Banks')")
    @RequestMapping(method = RequestMethod.PUT)
    public ResponseObject update(@ParameterName("domain") Bank bank,
                               @ParameterName(value = "tempContentModel",required = false) TempContentModel tempContentModel) throws Exception {
        try {
            Bank bank1 = service.saveBankWithContent(bank,tempContentModel);
            return ResponseObject.UPDATED_SUCCESS(bank1, null);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


    @Override
    @PreAuthorize("hasAnyAuthority('Admin','Define Banks')")
    public ResponseObject getAll(@ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @RequestParam(required = false) int... status) {
        return super.getAll(pagination, status);
    }

    @PreAuthorize("hasAnyAuthority('Admin')")
    @RequestMapping(value = "get-users", method = RequestMethod.GET)
    public ResponseObject getAll(@ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @RequestParam(required = false) int bank_id) {
        try {

            Page<ApplicationUser> page = service.getUsers(pagination, bank_id);
            Map<String, Object> count = new HashMap<>();
            if (page != null) {
                count.put("count", page.getTotalElements());
                return ResponseObject.FETCHED_SUCCESS(page.getContent(), count);
            } else {
                return ResponseObject.FETCHING_FAILED(null, null);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        }
    }


}