package com.sss.fatora.service.converter;


import com.sss.fatora.domain.converter.CustomerContact;
import com.sss.fatora.domain.converter.CustomerContactData;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class CustomerContactService extends GenericConverterService<CustomerContact> {
    @Autowired
    CustomerContactDataService customerContactDataService;

    public CustomerContact prepareCustomerContactObject(CustomerContact customerContact, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(customerContact,excelIndexes,row);
        customerContact.setContact_data(customerContactDataService.prepareCustomerContactDataObject(new CustomerContactData(),excelIndexes,row));
        return customerContact;
    }

    public CustomerContact prepareCustomerContactObjectFromPanel(CustomerContact customerContact, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(customerContact,fixedValues);
        customerContact.setContact_data(customerContactDataService.prepareCustomerContactDataObjectFromPanel(customerContact.getContact_data(),fixedValues));
        return customerContact;
    }
}
