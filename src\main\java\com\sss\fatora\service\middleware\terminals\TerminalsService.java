package com.sss.fatora.service.middleware.terminals;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.dao.local.AcquiringRequestDao;
import com.sss.fatora.domain.local.AcquiringActionRequest;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.middleware.terminals.*;
import com.sss.fatora.domain.middleware.terminals.clients.clientRequest;
import com.sss.fatora.domain.middleware.terminals.clients.clientsResponse;
import com.sss.fatora.domain.middleware.terminals.dto.ATM.AddATMRequest;
import com.sss.fatora.domain.middleware.terminals.dto.ATM.ChangeDenominationsRequest;
import com.sss.fatora.domain.middleware.terminals.dto.EPOS.*;
import com.sss.fatora.domain.middleware.terminals.dto.POS.*;
import com.sss.fatora.domain.middleware.terminals.mcc.mccListResponse;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.export.service.middleware.ExportTerminalService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.*;
import com.sss.fatora.utils.model.Pagination;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class TerminalsService {

    private final AcquiringRequestDao acquiringRequestDao;

    private final PrivilegeService privilegeService;
    private final Environment environment;

    private final ObjectMapper objectMapper;

    private final ConfigService configService;
    final RestTemplate restTemplate;
    private final ExportTerminalService exportTerminalService;
    private static final Logger LOGGER = Logger.getLogger(TerminalsService.class.getName());
    private final String posPrefix = "POS";
    private final String eposPrefix = "ePOS";
    private final String atmPrefix = "ATM";

    public TerminalsService(PrivilegeService privilegeService, Environment environment, ConfigService configService,
            RestTemplate restTemplate, ExportTerminalService exportTerminalService,
            AcquiringRequestDao acquiringRequestDao, ObjectMapper objectMapper) {
        this.privilegeService = privilegeService;
        this.environment = environment;
        this.configService = configService;
        this.restTemplate = restTemplate;
        this.exportTerminalService = exportTerminalService;
        this.objectMapper = objectMapper;
        this.acquiringRequestDao = acquiringRequestDao;
    }

    public TerminalResponse getPOSTerminals(TerminalRequest terminalRequest, Pagination pagination) {
        String url = environment.getProperty("Base_Url");
        Map<String, List<String>> fieldsMap = new HashMap<>();

        fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH.getType(), null, posPrefix);
        url += environment.getProperty("GetPOSTerminals");
        // fieldsMap.get("fields").add("terminalNumber");

        return setUpTerminalRequest(terminalRequest, pagination, url, fieldsMap.get("fields"));
    }

    public TerminalResponse getPOSTerminalById(TerminalRequestById terminalRequest, Pagination pagination) {

        Map<String, List<String>> fieldsMap = new HashMap<>();
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetPOSTerminalById");

        fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH_WITH_DETAILS.getType(), null, posPrefix);

        return setUpTerminalRequestById(terminalRequest, pagination, url, fieldsMap.get("fields"));
    }

    public TerminalResponse getEPOSTerminals(TerminalRequest terminalRequest, Pagination pagination) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetEPOSTerminals");
        Map<String, List<String>> fieldsMap = new HashMap<>();
        fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH.getType(), null, eposPrefix);
        // fieldsMap.get("fields").add("terminalNumber");

        return setUpTerminalRequest(terminalRequest, pagination, url, fieldsMap.get("fields"));
    }

    public TerminalResponse getEPOSTerminalById(TerminalRequestById terminalRequest, Pagination pagination) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetEPOSTerminalById");
        Map<String, List<String>> fieldsMap = new HashMap<>();
        fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH_WITH_DETAILS.getType(), null,
                eposPrefix);

        return setUpTerminalRequestById(terminalRequest, pagination, url, fieldsMap.get("fields"));
    }

    public TerminalResponse getATMTerminals(TerminalRequest terminalRequest, Pagination pagination, Boolean details) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetATMs");
        Map<String, List<String>> fieldsMap = new HashMap<>();
        if (details) {
            fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH_WITH_DETAILS.getType(), null,
                    atmPrefix);
            fieldsMap.get("fields").add("pid");

        } else {
            fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.SEARCH_ATM_Main.getType(), null, atmPrefix);
            // fieldsMap.get("fields").add("terminalNumber");
        }
        return setUpTerminalRequest(terminalRequest, pagination, url, fieldsMap.get("fields"));
    }

    public TerminalResponse GetTerminalsByMerchantNumber(TerminalRequest terminalRequest) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetTerminalsByMerchant");
        List<String> fields = new ArrayList<>();
        fields.addAll(configService.getTerminalColumns());
        fields.addAll(fields.size(), configService.getTerminalColumnsWithDetails());
        return setUpTerminalRequest(terminalRequest, null, url, fields);
    }

    private TerminalResponse setUpTerminalRequest(TerminalRequest terminalRequest, Pagination pagination, String url,
            List<String> fields) {

        HttpHeaders headers = new HttpHeaders();
        if (terminalRequest == null) {
            terminalRequest = new TerminalRequest();
        }
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
            String bankCode = applicationUser.getBank().getCode();
            terminalRequest.setBank(bankCode);
        }
        if (pagination == null) {
            terminalRequest.setMaxResultCount(10);
            terminalRequest.setSkipCount(0);
        } else {
            terminalRequest.setSorting(pagination.getOrderBy());
            terminalRequest.setMaxResultCount(pagination.getSize());
            terminalRequest.setSkipCount(pagination.getStart());
        }
        terminalRequest.setFields(fields);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(terminalRequest, headers);
        LOGGER.info(" url :" + url);
        LOGGER.info(" \nRequest :" + new ObjectMapper().valueToTree(request).toString());
        ResponseEntity<TerminalResponse> response = restTemplate.postForEntity(url, request, TerminalResponse.class);
        LOGGER.info(" \nResponse :" + new ObjectMapper().valueToTree(response).toString());
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    private TerminalResponse setUpTerminalRequestById(TerminalRequestById terminalRequest, Pagination pagination,
            String url, List<String> fields) {

        HttpHeaders headers = new HttpHeaders();
        if (terminalRequest == null) {
            terminalRequest = new TerminalRequestById();
        }
        if (pagination == null) {
            terminalRequest.setMaxResultCount(10);
            terminalRequest.setSkipCount(0);
        } else {
            terminalRequest.setSorting(pagination.getOrderBy());
            terminalRequest.setMaxResultCount(pagination.getSize());
            terminalRequest.setSkipCount(pagination.getStart());
        }
        terminalRequest.setFields(fields);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(terminalRequest, headers);
        LOGGER.info(" url :" + url);
        LOGGER.info(" \nRequest :" + new ObjectMapper().valueToTree(request).toString());
        ResponseEntity<TerminalResponse> response = restTemplate.postForEntity(url, request, TerminalResponse.class);
        LOGGER.info(" \nResponse :" + new ObjectMapper().valueToTree(response).toString());
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    public clientsResponse getClientEGateData(clientRequest clientRequest) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetClients");
        return setUpClientRequest(clientRequest, url);
    }

    private clientsResponse setUpClientRequest(clientRequest clientRequest, String url) {
        HttpHeaders headers = new HttpHeaders();
        if (clientRequest == null) {
            clientRequest = new clientRequest();
        }
        clientRequest.setFields(configService.getClientColumns());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(clientRequest, headers);
        ResponseEntity<clientsResponse> response = restTemplate.postForEntity(url, request, clientsResponse.class);
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    public mccListResponse getMCCList() {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetMCCList");
        ResponseEntity<mccListResponse> response = restTemplate.postForEntity(url, null, mccListResponse.class);
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    public TerminalResponse getTerminalCustomer(String terminalNumber) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetTerminalCustomer");

        Map<String, Object> request = new HashMap<>();
        request.put("terminalNumber", terminalNumber); 

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);

        ResponseEntity<TerminalResponse> response = restTemplate.postForEntity(url, entity, TerminalResponse.class);

        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody();
    }

    public TerminalResponse exportPOSTerminals(TerminalRequest terminalRequest, Boolean withDetails,
            HttpServletResponse response) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetPOSTerminals");
        Map<String, List<String>> fieldsMap;
        Pagination pagination = new Pagination();
        pagination.setSize(0);
        pagination.setStart(0);
        try {
            TerminalResponse terminalList;
            List<String> headers;
            if (withDetails) {
                headers = configService.getTerminalPOSExportDetailsHeaders();
                fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_POS_WITH_DETAILS.getType(),
                        headers, posPrefix);
                terminalList = setUpTerminalRequest(terminalRequest, pagination, url, fieldsMap.get("fields"));
            } else {
                headers = configService.getTerminalExportMainHeaders();
                fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_WITHOUT_DETAILS.getType(), headers,
                        posPrefix);
                terminalList = setUpTerminalRequest(terminalRequest, pagination, url, fieldsMap.get("fields"));
                fieldsMap.get("fields").remove("feePercentage");
            }
            exportTerminalService.export(terminalList.getResult().getItems(), response, fieldsMap.get("headers"),
                    fieldsMap.get("fields"));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return new TerminalResponse();
    }

    public TerminalResponse exportEPOSTerminals(TerminalRequest terminalRequest, Boolean withDetails,
            HttpServletResponse response) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetEPOSTerminals");
        Map<String, List<String>> fieldsMap;
        Pagination pagination = new Pagination();
        pagination.setSize(0);
        pagination.setStart(0);
        try {
            TerminalResponse terminalList;
            if (withDetails) {
                List<String> headers = configService.getTerminalEPOSExportDetailsHeaders();
                fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_EPOS_WITH_DETAILS.getType(),
                        headers, eposPrefix);
                terminalList = setUpTerminalRequest(terminalRequest, pagination, url, fieldsMap.get("fields"));

            } else {
                List<String> headers = configService.getTerminalExportMainHeaders();
                fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_WITHOUT_DETAILS.getType(), headers,
                        eposPrefix);
                terminalList = setUpTerminalRequest(terminalRequest, pagination, url, fieldsMap.get("fields"));
                fieldsMap.get("fields").remove("feePercentage");
            }

            exportTerminalService.export(terminalList.getResult().getItems(), response, fieldsMap.get("headers"),
                    fieldsMap.get("fields"));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

        return new TerminalResponse();
    }

    public TerminalResponse exportATMTerminals(TerminalRequest terminalRequest, Boolean withDetails,
            HttpServletResponse response) {
        String url = environment.getProperty("Base_Url") + environment.getProperty("GetATMs");
        Map<String, List<String>> fieldsMap;
        Pagination pagination = new Pagination();
        pagination.setSize(0);
        pagination.setStart(0);
        try {
            if (withDetails) {
                List<String> headers = configService.getTerminalATMExportDetailsHeaders();
                fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_ATM_WITH_DETAILS.getType(),
                        headers, atmPrefix);
            } else {
                List<String> headers = configService.getTerminalExportMainHeaders();
                fieldsMap = setFieldsByPrivilege(PaymentMiddlewareRequestType.EXPORT_WITHOUT_DETAILS.getType(), headers,
                        atmPrefix);
            }
            TerminalResponse terminalList = setUpTerminalRequest(terminalRequest, pagination, url,
                    fieldsMap.get("fields"));
            exportTerminalService.export(terminalList.getResult().getItems(), response, fieldsMap.get("headers"),
                    fieldsMap.get("fields"));
            return new TerminalResponse();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private Map<String, List<String>> setFieldsByPrivilege(String type, List<String> projectionHeaders, String prefix) {
        List<String> projection = new ArrayList<>();
        List<String> privileges;
        if (prefix.equalsIgnoreCase(posPrefix))
            privileges = getPrivilegesNamesByUserId(PrivilegeType.TERMINAL_POS_COLUMN.getType());
        else if (prefix.equalsIgnoreCase(eposPrefix))
            privileges = getPrivilegesNamesByUserId(PrivilegeType.TERMINAL_EPOS_COLUMN.getType());
        else
            privileges = getPrivilegesNamesByUserId(PrivilegeType.TERMINAL_ATM_COLUMN.getType());

        if (PaymentMiddlewareRequestType.SEARCH.getType().equalsIgnoreCase(type)) {
            projection = configService.getTerminalColumns();
            // projection.add("feePercentage");
        } else if (PaymentMiddlewareRequestType.SEARCH_ATM_Main.getType().equalsIgnoreCase(type)) {
            projection = configService.getATMTerminalColumns();
            // projection.add("feePercentage");
        } else if (PaymentMiddlewareRequestType.SEARCH_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getTerminalColumnsWithDetails();
        } else if (PaymentMiddlewareRequestType.EXPORT_WITHOUT_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getTerminalExportMainColumns();
            // projection.add("feePercentage");
        } else if (PaymentMiddlewareRequestType.EXPORT_POS_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getTerminalPOSExportDetailsColumns();
        } else if (PaymentMiddlewareRequestType.EXPORT_EPOS_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getTerminalEPOSExportDetailsColumns();
        } else if (PaymentMiddlewareRequestType.EXPORT_ATM_WITH_DETAILS.getType().equalsIgnoreCase(type)) {
            projection = configService.getTerminalATMExportDetailsColumns();
        } else if (PaymentMiddlewareRequestType.GET_TERMINALS_BY_MERCHANT_NUMBER.getType().equalsIgnoreCase(type)) {
            List<String> fields = new ArrayList<>();
            fields.addAll(configService.getTerminalColumns());
            fields.addAll(fields.size(), configService.getTerminalColumnsWithDetails());
            projection = fields;
        } else
            projection = configService.getTerminalColumns();
        return filterProjectionList(projectionHeaders, projection, privileges, prefix);
    }

    private List<String> getPrivilegesNamesByUserId(String type) {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(type, applicationUser.getId()).stream()
                .map(Privilege::getName).collect(Collectors.toList());
        return privileges;
    }

    private Map<String, List<String>> filterProjectionList(List<String> headers, List<String> projection,
            List<String> privileges, String prefix) {
        List<String> projections = new ArrayList<>();
        int index = 0;
        List<String> headersTemp = new ArrayList<>();

        for (String proj : projection) {
            if (proj.equalsIgnoreCase("agentShortDesc") || proj.equalsIgnoreCase("agentNumber"))
                if (privileges.contains(prefix + "_" + "branch")) {
                    projections.add(proj);
                    if (headers != null)
                        headersTemp.add(headers.get(index));
                }
            if (privileges.contains(prefix + "_" + proj)) {
                projections.add(proj);
                if (headers != null && headers.size() > index)
                    headersTemp.add(headers.get(index));
            }

            index++;
        }
        if (headers != null) {
            headers.clear();
            headers.addAll(headersTemp);
        }
        projection.clear();
        projection.addAll(projections);
        Map<String, List<String>> returnedMap = new HashMap<>();
        returnedMap.put("headers", headers);
        returnedMap.put("fields", projection);
        return returnedMap;
    }

    // POS
    public void addPos(AddTerminalRequest terminalRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.ADD_POS.getAction());
        acquiringRequestEntity.setEntityId(terminalRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());
        // acquiringRequestEntity.setOldValue(null);
        // try {
        // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(terminalRequest));
        // } catch (Exception e) {
        // e.printStackTrace();
        // }

        acquiringRequestDao.save(acquiringRequestEntity);

    }

    public void closePosTerminal(ClosePOSTerminalRequest closePOSTerminalRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.CLOSE_POS_TERMINAL.getAction());
        acquiringRequestEntity.setEntityId(closePOSTerminalRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());

        try {
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(closePOSTerminalRequest));
        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void replacePOSDevice(ReplacePOSDeviceRequest replacePOSDeviceRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.REPLACE_POS_DEVICE.getAction());
        acquiringRequestEntity.setEntityId(replacePOSDeviceRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());

        try {
            Map<String, String> oldValue = new HashMap<>();
            oldValue.put("serialNumber", replacePOSDeviceRequest.getOldSerialNumber());
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, String> newValue = new HashMap<>();
            newValue.put("serialNumber", replacePOSDeviceRequest.getNewSerialNumber());
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void changeAccountPOS(ChangeAccountPOSRequest changeAccountPOSRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_ACCOUNT_POS.getAction());
        acquiringRequestEntity.setEntityId(changeAccountPOSRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());

        try {
            Map<String, String> oldValue = new HashMap<>();
            oldValue.put("accountNumber", changeAccountPOSRequest.getOldAccountNumber());
            oldValue.put("accountType", changeAccountPOSRequest.getOldAccountType());
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, String> newValue = new HashMap<>();
            newValue.put("accountNumber", changeAccountPOSRequest.getNewAccountNumber());
            newValue.put("accountType", changeAccountPOSRequest.getNewAccountType());
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void editGPSCoordinates(EditGPSCoordinatesPOSRequest editGPSCoordinatesPOSRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.EDIT_GPS_COORDINATES.getAction());
        acquiringRequestEntity.setEntityId(editGPSCoordinatesPOSRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());

        try {
            Map<String, String> oldValue = new HashMap<>();
            oldValue.put("latitude", editGPSCoordinatesPOSRequest.getOldLatitude());
            oldValue.put("longitude", editGPSCoordinatesPOSRequest.getOldLongitude());
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, String> newValue = new HashMap<>();
            newValue.put("latitude", editGPSCoordinatesPOSRequest.getNewLatitude());
            newValue.put("longitude", editGPSCoordinatesPOSRequest.getNewLongitude());
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    // EPOS
    public void addEPos(AddEPosRequest terminalRequest, MultipartFile logo) {

        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.ADD_EPOS.getAction());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());
        try {
            String filePath = "";
            if (logo != null && !logo.isEmpty()) {

                BufferedImage image = ImageIO.read(logo.getInputStream());
                double width = image.getWidth();
                double height = image.getHeight();
                boolean isWide = (width == Double
                        .parseDouble(Objects.requireNonNull(environment.getProperty("WhidthWideImage")))
                        && height == Double
                                .parseDouble(Objects.requireNonNull(environment.getProperty("HeightWideImage"))));
                boolean isSquared = (width == Double
                        .parseDouble(Objects.requireNonNull(environment.getProperty("WhidthSquaredImage")))
                        && height == Double
                                .parseDouble(Objects.requireNonNull(environment.getProperty("HeightSquaredImage"))));

                if (!isWide && !isSquared) {
                    throw new IllegalArgumentException("Invalid image dimensions. Allowed sizes: 135x72 or 72x72.");
                }
                String formattedDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HHmmss"));

                String resourcesPath = ResourceUtils.getURL("classpath:epos-logos/").getPath();
                String filename = formattedDate + "_" + logo.getOriginalFilename();
                filePath = resourcesPath + filename;
                logo.transferTo(new File(filePath));
            }

            Map<String, Object> newValue = new HashMap<>();
            newValue.put("eposRequest", terminalRequest);
            newValue.put("logo", filePath);

            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));
            acquiringRequestDao.save(acquiringRequestEntity);
            // Return proper error response
        } catch (IllegalArgumentException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void closeEPosTerminal(CloseEPOSTerminalRequest closeEPOSTerminalRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.CLOSE_EPOS_TERMINAL.getAction());
        acquiringRequestEntity.setEntityId(closeEPOSTerminalRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());

        try {
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(closeEPOSTerminalRequest));
        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void manageEPOSTerminalsIds(ManageEPOSTerminalRequest manageEPOSTerminalRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.MANAGE_EPOS_TERMINAL_IDS.getAction());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());

        try {
            Map<String, List<String>> oldValue = new HashMap<>();
            oldValue.put("terminalIds", manageEPOSTerminalRequest.getOldTerminalIds());
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, List<String>> newValue = new HashMap<>();
            newValue.put("terminalIds", manageEPOSTerminalRequest.getNewTerminalIds());
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void changeAccountEPOS(ChangeAccountEPOSRequest changeAccountEPOSRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_ACCOUNT_EPOS.getAction());
        acquiringRequestEntity.setEntityId(changeAccountEPOSRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());

        try {
            Map<String, String> oldValue = new HashMap<>();
            oldValue.put("accountNumber", changeAccountEPOSRequest.getOldAccountNumber());
            oldValue.put("accountType", changeAccountEPOSRequest.getOldAccountType());
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, String> newValue = new HashMap<>();
            newValue.put("accountNumber", changeAccountEPOSRequest.getNewAccountNumber());
            newValue.put("accountType", changeAccountEPOSRequest.getNewAccountType());
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void changeEGateDataEPOS(ChangeEGateDataEPOSRequest changeEGateDataEPOSRequest, MultipartFile logo) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_EGATE_DATA.getAction());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        acquiringRequestEntity.setCreationDate(new Date());
        try {
            String filePath = "";
            if (logo != null && !logo.isEmpty()) {
                String formattedDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HHmmss"));

                String resourcesPath = ResourceUtils.getURL("classpath:epos-logos/").getPath();
                String filename = formattedDate + "_" + logo.getOriginalFilename();
                filePath = resourcesPath + filename;
                logo.transferTo(new File(filePath));
            }
            Map<String, Object> oldValue = new HashMap<>();
            oldValue.put("projectManagerName", changeEGateDataEPOSRequest.getOldProjectManagerName());
            oldValue.put("projectManagerEmail", changeEGateDataEPOSRequest.getOldProjectManagerEmail());
            oldValue.put("projectManagerMobile", changeEGateDataEPOSRequest.getOldProjectManagerMobile());
            oldValue.put("technicalManagerName", changeEGateDataEPOSRequest.getOldTechnicalManagerName());
            oldValue.put("technicalManagerEmail", changeEGateDataEPOSRequest.getOldTechnicalManagerEmail());
            oldValue.put("technicalManagerMobile", changeEGateDataEPOSRequest.getOldTechnicalManagerMobile());
            oldValue.put("IPAddress", changeEGateDataEPOSRequest.getOldIPAddress());
            oldValue.put("logo", changeEGateDataEPOSRequest.getOldLogo());

            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, Object> newValue = new HashMap<>();
            newValue.put("projectManagerName", changeEGateDataEPOSRequest.getNewProjectManagerName());
            newValue.put("projectManagerEmail", changeEGateDataEPOSRequest.getNewProjectManagerEmail());
            newValue.put("projectManagerMobile", changeEGateDataEPOSRequest.getNewProjectManagerMobile());
            newValue.put("technicalManagerName", changeEGateDataEPOSRequest.getNewTechnicalManagerName());
            newValue.put("technicalManagerEmail", changeEGateDataEPOSRequest.getNewTechnicalManagerEmail());
            newValue.put("technicalManagerMobile", changeEGateDataEPOSRequest.getNewTechnicalManagerMobile());
            newValue.put("IPAddress", changeEGateDataEPOSRequest.getNewIPAddress());
            newValue.put("logo", filePath);

            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    // ATM
    public void addATM(AddATMRequest terminalRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.ADD_ATM.getAction());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        // acquiringRequestEntity.setOldValue(null);
        try {
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(terminalRequest));
        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void changeDenominations(ChangeDenominationsRequest terminalRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_ATM_DENOMINATIONS.getAction());
        acquiringRequestEntity.setEntityId(terminalRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        try {
            Map<String, String> oldValue = new HashMap<>();
            oldValue.put("cassetteOne", terminalRequest.getOldCassetteOne());
            oldValue.put("cassetteTwo", terminalRequest.getOldCassetteTwo());
            oldValue.put("cassetteThree", terminalRequest.getOldCassetteThree());
            oldValue.put("cassetteFour", terminalRequest.getOldCassetteFour());
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, String> newValue = new HashMap<>();
            newValue.put("cassetteOne", terminalRequest.getNewCassetteOne());
            newValue.put("cassetteTwo", terminalRequest.getNewCassetteTwo());
            newValue.put("cassetteThree", terminalRequest.getNewCassetteThree());
            newValue.put("cassetteFour", terminalRequest.getNewCassetteFour());
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }

    public void changeAccountATM(ChangeAccountPOSRequest changeAccountPOSRequest) {
        AcquiringActionRequest acquiringRequestEntity = new AcquiringActionRequest();

        acquiringRequestEntity.setAction(AcquiringActionType.CHANGE_ACCOUNT_ATM.getAction());
        acquiringRequestEntity.setEntityId(changeAccountPOSRequest.getTerminalId());
        acquiringRequestEntity.setEntityName(ModuleName.TERMINALS.getName());
        try {
            Map<String, String> oldValue = new HashMap<>();
            oldValue.put("accountNumber", changeAccountPOSRequest.getOldAccountNumber());
            oldValue.put("accountType", changeAccountPOSRequest.getOldAccountType());
            // acquiringRequestEntity.setOldValue(objectMapper.writeValueAsString(oldValue));

            Map<String, String> newValue = new HashMap<>();
            newValue.put("accountNumber", changeAccountPOSRequest.getNewAccountNumber());
            newValue.put("accountType", changeAccountPOSRequest.getNewAccountType());
            // acquiringRequestEntity.setNewValue(objectMapper.writeValueAsString(newValue));

        } catch (Exception e) {
            e.printStackTrace();
        }

        acquiringRequestDao.save(acquiringRequestEntity);
    }
}
