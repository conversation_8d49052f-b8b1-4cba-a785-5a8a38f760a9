package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "Acquiring_Requests")
public class AcquiringActionRequest extends LocalDomain {

    public AcquiringActionRequest(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "Action_Type")
    String actionType;

    @Column(name = "Action_Value")
    String actionValue;

    @Column(name = "Request_Date")
    Date requestDate;

    @Column(name = "Action_Details")
    String actionDetails;

    @Column(name = "User_Request_Status")
    String userRequestStatus;

    @Column(name = "Action_Status")
    String action;

    @Column(name = "Error_Message")
    String errorMessage;
    @Column(name = "Approval_Date")
    Date approvalDate;
    @Column(name = "Request_Status")
    String requestStatus;
    @Column(name="Description")
    String description;
    // @Column(name = "Serial_Number")
    // String serialNumber;
    // @Column(name = "Terminal_Id")
    // String terminalId;
    // @Column(name = "Latitude")
    // String latitude;
    // @Column(name = "Longitude")
    // String longitude;
    // @Column(name = "Pairs")
    // List<Map<String, String>> pairs;
    @Column(name = "EntityName")
    private String entityName;

    @Column(name = "EntityId")
    private String entityId;

    // @Column(name = "Archived_Date")
    // Date archivedDate;

    @ManyToOne
    @JoinColumn(name = "User_Id", referencedColumnName = "id")
    @JsonIgnoreProperties("actionRequests")
    ApplicationUser applicationUser;

    @Transient
    AcquiringActionRequestJSON acquiringActionRequest;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }

}
