package com.sss.fatora.domain.converter;


import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.List;

@Setter
@Getter
@XmlType(propOrder = {"command", "cardholder_number", "cardholder_name", "contact", "sec_word"})
public class CardHolder extends GenericConverter {
    @ExcelProperty(name = "ns1:command5")
    private String command;
    @ExcelProperty(name = "ns1:cardholder_number")
    private String cardholder_number;
    @ExcelProperty(name = "ns1:cardholder_name")
    private String cardholder_name;
    @JacksonXmlProperty(localName = "contact")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Contact> contact;
    private SecWord sec_word;

}
