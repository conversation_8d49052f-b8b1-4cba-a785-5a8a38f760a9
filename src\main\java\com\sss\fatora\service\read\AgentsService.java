package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.AgentsDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.read.Agents;
import com.sss.fatora.domain.read.Products;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("readingDBTransactionManager")
public class AgentsService extends GenericReadService<AgentsDao, Agents, Long> {

    /**
     * Author Comment: Maria <PERSON>
     * get agents which stands for bank branches for this user
     * if bank user get the bank branches
     * if Fatora user get all branches
     */
    public List<Agents> getAllAgentsByBankCode() {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            String bankCode = "";
            if (applicationUser.getBank() != null) {
                if (applicationUser.getBank().getPrefix().equals("SIIB"))
                    return dao.findAll();
                bankCode = applicationUser.getBank().getCode();
                return dao.getAllAgentsByBankCode(bankCode);
            } else
                return dao.findAllAgents();

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }
}
