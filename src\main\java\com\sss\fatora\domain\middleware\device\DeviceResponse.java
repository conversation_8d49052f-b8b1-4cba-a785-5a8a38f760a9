package com.sss.fatora.domain.middleware.device;

import com.sss.fatora.domain.middleware.response.SearchMiddlewareResult;
import com.sss.fatora.domain.middleware.terminals.ResponseError;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class DeviceResponse<T>  {
   /* @JsonProperty("result")
    private SearchMiddlewareResult<T> result;*/

    private SearchMiddlewareResult<Device> result;
    private String targetUrl;
    private Boolean success;
    private ResponseError error;
    private Boolean unAuthorizedRequest;
    private Boolean __abp;
}
