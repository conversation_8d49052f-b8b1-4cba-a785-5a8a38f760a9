package com.sss.fatora.domain.middleware.FNotify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class MessagesAPIResponse {

    @JsonProperty("Messages")
    private List<MessageResponse> Messages;

    @JsonProperty("TotalSize")
    private Integer TotalSize;

    @JsonProperty("ErrorMessage")
    private String ErrorMessage;
}
