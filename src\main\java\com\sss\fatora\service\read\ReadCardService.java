package com.sss.fatora.service.read;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.sss.fatora.dao.read.ReadCardDao;
import com.sss.fatora.domain.converter.*;
import com.sss.fatora.domain.issuing.XmlIssueError;
import com.sss.fatora.domain.local.ActionRequest;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.domain.local.SoapResponse;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.CardsBo;
import com.sss.fatora.domain.read.DTO.CardLanguageDTO;
import com.sss.fatora.domain.read.DTO.CardMobileNumberDTO;
import com.sss.fatora.domain.read.DTO.ChangeProductDTO;
import com.sss.fatora.domain.read.DTO.CloseCardDTO;
import com.sss.fatora.domain.read.DTO.ReissueCardDTO;
import com.sss.fatora.domain.read.DTO.ActionResponse;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.converter.ApplicationService;
import com.sss.fatora.service.generic.GenericReadService;
import com.sss.fatora.service.local.*;
import com.sss.fatora.utils.constants.*;
import com.sss.fatora.utils.log.*;
import com.sss.fatora.utils.model.DateUtils;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.service.ObjectConverter;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.persistence.EntityManager;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional("readingDBTransactionManager")
public class ReadCardService extends GenericReadService<ReadCardDao, CardDataVW, Long> {

    final ReadCardDao readCardDao;
    final BankService bankService;
    final ApplicationUserPrivilegeService applicationUserPrivilegeService;
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;
    final AgentsService agentsService;
    final LanguagesService languagesService;
    final CurrencyCodesService currencyCodesService;
    final CountryCodesService countryCodesService;
    final LocalApplicationService localApplicationService;
    final ApplicationService applicationConverterService;
    final CardsBoService cardsBoService;
    final ProductsService productsService;
    final MessageSource messageSource;

    /*
     * List<String> mainProjection;
     * List<String> detailsProjection;
     */

    @Autowired
    @Qualifier("readingDBEntityManagerFactory")
    private EntityManager entityManager;

    public ReadCardService(ReadCardDao readCardDao, BankService bankService,
            ApplicationUserPrivilegeService applicationUserPrivilegeService,
            PaginationService paginationService, PrivilegeService privilegeService, ConfigService configService,
            AgentsService agentsService, LanguagesService languagesService, CountryCodesService countryCodesService,
            CurrencyCodesService currencyCodesService,
            LocalApplicationService localApplicationService,
            ApplicationService applicationConverterService, CardsBoService cardsBoService,
            ProductsService productsService,
            MessageSource messageSource, Environment environment, RestTemplate restTemplate,
            ActionRequestService actionRequestService) {
        this.readCardDao = readCardDao;
        this.bankService = bankService;
        this.applicationUserPrivilegeService = applicationUserPrivilegeService;
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.agentsService = agentsService;
        this.languagesService = languagesService;
        this.countryCodesService = countryCodesService;
        this.currencyCodesService = currencyCodesService;
        this.localApplicationService = localApplicationService;
        this.applicationConverterService = applicationConverterService;
        this.productsService = productsService;
        this.messageSource = messageSource;

        this.cardsBoService = cardsBoService;
        this.environment = environment;
        this.restTemplate = restTemplate;
        this.actionRequestService = actionRequestService;
    }

    /**
     * This Function Get All The Cards In Db
     */
    public List<CardDataVW> getCards() {
        try {
            return readCardDao.findAll();
        } catch (Exception exception) {
            throw exception;
        }
    }

    public CardDataVW getCardByNumber(String cardId) {

        try {
            return readCardDao.getCardByNumber(cardId);
        } catch (Exception exception) {
            throw exception;
        }
    }

    /**
     * @param card                      This Parameter Is For The Value Of Filters
     *                                  Send From The Front-End
     * @param fromIssueDate,toIssueDate This Two Parameters Represent The Period We
     *                                  Want To Search In (Card Issuing Date)
     * @param fromExpDate,toExpDate     This Two Parameters Represent The Period We
     *                                  Want To Search In (Card Expiration Date)
     * @param filterOperator            There Is An Specific Operator For Every
     *                                  Filter
     *                                  {@link com.sss.fatora.utils.constants.Operator}
     * @param projection                This Is A List Of Columns For Select
     *                                  Statement In Search Query
     * @param accountNumber             This Parameter Is For Adding The Account
     *                                  Number To Where Clause ( This Var Is In
     *                                  Another Object )
     *                                  <p>
     *                                  This Function Add Certain Conditions To
     *                                  Where Clause That Can't Be Added To The
     *                                  Filter (CardDataVW)
     *                                  Directly And Then Request The Dynamic Search
     *                                  Function ( dynamicSearch() )
     */

    // @Log(actionType = ActionType.REISSUE_CARD)
    // public ResponseEntity<ActionResponse> reissuecard(Long actionRequestId,
    // String cardNo, String expiryDate, String deliveryBranch,int instantCard,int
    // oldExpiry,int oldCardNumber) {
    // try {

    // //card data
    // CardsBo card = cardsBoService.getCardBoByNumber(cardNo);
    // ReissueCardDTO resissueCardDTO = new ReissueCardDTO(cardNo, expiryDate,
    // card.getCustomerNumber(),
    // card.getCardHolderNumber(),card.getAgentId(),deliveryBranch,oldCardNumber,oldExpiry,instantCard);
    // String url = environment.getProperty("FatoraServiceUrl") +
    // environment.getProperty("reissueCardApi");
    // String username = environment.getProperty("BasicAuthUsername");
    // String password = environment.getProperty("BasicAuthPassword");
    // HttpHeaders headers = new HttpHeaders();
    // headers.setBasicAuth(username, password);
    // headers.setContentType(MediaType.APPLICATION_JSON);
    // HttpEntity<ReissueCardDTO> request = new HttpEntity<>(resissueCardDTO,
    // headers);
    // ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url,
    // request, ActionResponse.class, new Object());
    // System.out.println("request : "+ request);
    // System.out.println("*****************************************************************");
    // System.out.println("response : "+ response);
    // if (response.getBody() != null) {
    // if (response.getBody().getSuccess() == 0) {
    // XmlMapper xmlMapper = new XmlMapper();
    // xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
    // false);
    // XmlIssueError result = xmlMapper.readValue(response.getBody().getError(),
    // XmlIssueError.class);
    // response.getBody().setError(result.getFaultString());
    // actionRequestService.saveErrorMessage(actionRequestId,
    // response.getBody().getError());
    // }
    // } else
    // actionRequestService.saveErrorMessage(actionRequestId,
    // ActionStatus.CONNECTION_ERROR.getType());

    // return response;
    // } catch (Exception e) {
    // e.printStackTrace();
    // actionRequestService.saveErrorMessage(actionRequestId,
    // ActionStatus.CONNECTION_ERROR.getType());
    // return null;
    // }
    // }
    public Page<CardDataVW> search(CardDataVW card, Long fromIssueDate, Long toIssueDate, Long fromExpDate,
            Long toExpDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection,
            String accountNumber) throws ParseException, InvocationTargetException, IntrospectionException,
            NoSuchFieldException, IllegalAccessException {
        try {
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            String additionalConstraint = "";
            String date1 = "";
            String date2 = "";
            String oracleFormat = "yyyy-MM-dd HH24:MI:ss";
            String javaFormat2 = "yyyy-MM-dd HH:mm:ss";
            Page<CardDataVW> cards;
            String bins = "";

            if (card == null)
                card = new CardDataVW();
            if (fromIssueDate == null)
                fromIssueDate = new Date(*********).getTime();
            if (toIssueDate == null)
                toIssueDate = new Date(System.currentTimeMillis() * 100).getTime();
            if (toExpDate == null)
                toExpDate = new Date(System.currentTimeMillis() * 100).getTime();

            String clause = getClause(card, fromExpDate, toExpDate, accountNumber);
            DateFormat f = new SimpleDateFormat(javaFormat2);
            date1 = "to_timestamp('" + f.format(DateUtils.getStartDayOfDate(fromIssueDate)) + "','" + oracleFormat
                    + "')";
            date2 = "to_timestamp('" + f.format(DateUtils.getEndDayOfDate(toIssueDate)) + "','" + oracleFormat + "')";

            if (applicationUser.getBank() != null) {
                bins = applicationUser.getBank().getBinCodes().stream().map(s -> "CardDataVW.crefNo LIKE '" + s + "%'")
                        .collect(Collectors.joining(" OR "));
            }

            if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType())) {
                additionalConstraint = "(" + bins + ") " +
                        "AND CardDataVW.issueDate BETWEEN " + date1 + " AND " + date2;
                // additionalConstraint = "substring(CardDataVW.crefNo,0,6) IN (" + bins + ") "
                // +
                // "AND CardDataVW.issueDate BETWEEN " + date1 + " AND " + date2;
            } else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()))
                additionalConstraint = "CardDataVW.issueDate BETWEEN " + date1 + " AND " + date2;
            if (accountNumber != null) {
                accountNumber = accountNumber.replace("*", "%");
                String accountNumberClause = " AND CardDataVW.crefNo IN (SELECT AccountVW.cardNo FROM AccountVW AS AccountVW WHERE AccountVW.acctNo LIKE '"
                        + accountNumber + "' )";
                additionalConstraint = additionalConstraint.concat(accountNumberClause);
            }
            additionalConstraint = additionalConstraint.concat(clause);

            if (pagination == null)
                pagination = new Pagination();
            if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
                pagination.setOrderBy("CardDataVW.issueDate DESC , CardDataVW.cardId ");
                pagination.setOrderType("DESC");
            }
            cards = this.dynamicSearch(card, pagination, additionalConstraint, filterOperator, projection, true);

            if (cards == null) {
                return Page.empty();
            }
            return cards;
        } catch (Exception exception) {
            throw exception;
        }
    }

    /**
     * @param card                      This Parameter Is For The Value Of Filters
     *                                  Send From The Front-End
     * @param fromIssueDate,toIssueDate This Two Parameters Represent The Period We
     *                                  Want To Search In (Card Issuing Date)
     * @param fromExpDate,toExpDate     This Two Parameters Represent The Period We
     *                                  Want To Search In (Card Expiration Date)
     * @param filterOperators           There Is An Specific Operator For Every
     *                                  Filter
     *                                  {@link com.sss.fatora.utils.constants.Operator}
     * @param accountNumber             This Parameter Is For Adding The Account
     *                                  Number To Where Clause ( This Var Is In
     *                                  Another Object )
     *                                  <p>
     *                                  This Function Get UserPrivileges And Domain
     *                                  Columns And Then Request The Intersection
     *                                  Function
     *                                  ( filterProjectionList() ) On Them And Then
     *                                  Request Search Function ( search() )
     */

    @Log(actionType = ActionType.CARD_SEARCH)
    public Page<CardDataVW> mainSearch(CardDataVW card, Long fromIssueDate, Long toIssueDate, Long fromExpDate,
            Long toExpDate, Pagination pagination, Map<String, Operator> filterOperators, String accountNumber)
            throws NoSuchFieldException, IntrospectionException, IllegalAccessException, ParseException,
            InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getCardMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);
        Page<CardDataVW> cards = search(card, fromIssueDate, toIssueDate, fromExpDate, toExpDate, pagination,
                filterOperators, projection, accountNumber);
        return cards;
    }

    /**
     * @param filter   This Parameter Is For The Value Of Filters Send From The
     *                 Front-End
     * @param recordId This Parameter Is Set In The Filter Param For Where Clause Of
     *                 The Query
     *                 <p>
     *                 This Function Get UserPrivileges And Domain Columns And Then
     *                 Request The Intersection Function
     *                 ( filterProjectionList() ) On Them , Set The Desired Filter
     *                 Values To The Filter Object And Then
     *                 Request Search Function ( search() )
     */

    @Log(actionType = ActionType.CARD_DETAILS)
    public CardDataVW getWithDetails(CardDataVW filter, String recordId) throws NoSuchFieldException,
            IntrospectionException, IllegalAccessException, ParseException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getCardMainColumns());
        projection.addAll(configService.getCardDetailsColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        projection = filterProjectionList(projection, privileges);

        filter.setCrefNo(recordId);
        Page<CardDataVW> cards = search(filter, null, null, null, null, null, new HashMap<>(), projection, null);
        if (cards != null) {
            return !cards.isEmpty() ? ObjectConverter.convertToObject(cards.getContent().get(0), CardDataVW.class)
                    : null;
        }
        return null;
    }

    /**
     * @param projection List Of Columns That Will Be Selected In The Final Query
     * @param privileges List Of Privileges That Will Be Intersected With The List
     *                   Of Columns
     *                   The Job Of This Function Is To Intersect Between The Two
     *                   List And Then Return
     *                   The Result As A List Of Strings (TableName.Column As
     *                   Column) To Be Used In The
     *                   Select Section Of The Query
     */

    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {

        List<String> projections = projection.stream()
                .distinct()
                .filter(privileges::contains)
                .map(s -> "CardDataVW." + s + " AS " + s)
                .collect(Collectors.toList());
        // projections.add("CardDataVW.issueDate AS issueDate");
        // projections.add("CardDataVW.expDate AS expDate");
        return projections;
    }

    /**
     * @param card                  This Parameter Is For The Value Of Filters Send
     *                              From The Front-End
     * @param fromExpDate,toExpDate This Two Parameters Represent The Period We Want
     *                              To Search In
     *                              <p>
     *                              This Function Add An Additional Where Condition
     *                              To The Query
     */
    private String getClause(CardDataVW card, Long fromExpDate, Long toExpDate, String accountNumber) {
        String fExpDate = null;
        String tExpDate = null;
        String clause;
        if (card.getExpDate() != null && fromExpDate == null) {
            fromExpDate = new Date(System.currentTimeMillis()).getTime();
            card.setExpDate(null);
            // card.setExpDate(Long.valueOf(DateUtils.getYearMonthFromCurrentDate()));
            // filterOperator.put("expDate", Operator.EQUALS_OR_GREATER_THAN);
        } else if (card.getExpDate() == null && fromExpDate == null) {
            fromExpDate = new Date(*********).getTime();
        } else if (card.getExpDate() != null && fromExpDate != null) {
            card.setExpDate(null);
        }
        fExpDate = getExpDate(fromExpDate);
        tExpDate = getExpDate(toExpDate);
        clause = " AND CardDataVW.expDate BETWEEN " + fExpDate + " AND " + tExpDate;
        // if( accountNumber != null){
        // clause = clause.concat(" AND AccountVW.acctNo LIKE '%" + accountNumber + "%'
        // ");
        // }
        return clause;
    }

    /**
     * @param expDate This Parameter Is A TimeStamp Date
     *                <p>
     *                This Function Change The Date From TimeStamp To Another
     *                Irregular Format
     *                That Is Like (202211)
     */

    private String getExpDate(Long expDate) {
        String stringDate;
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        calendar.setTimeInMillis(expDate);
        Integer dateYear = calendar.get(Calendar.YEAR);
        Integer dateMonth = calendar.get(Calendar.MONTH) + 1;
        /*
         * This 'If' Is For EXAMPLE.
         * year = 2022, month = 11 So It Become -> 202211 Do Not Need To Add Any Zero
         */
        if (dateMonth >= 10) {
            stringDate = dateYear.toString() + dateMonth.toString();
        }
        /*
         * This 'Else' Is For EXAMPLE.
         * year = 2022, month = 1 So It Become -> 202201 We Add A Zero After Year Before
         * Month
         */
        else {
            stringDate = dateYear.toString() + "0" + dateMonth.toString();
        }
        return stringDate;
    }

    /**
     * This Function Get A List Of All Privileges Names That This User Has For This
     * Specific Domain (Customer)
     */

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService
                .getPrivilegesById(PrivilegeType.CARD_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }

    // Deprecated
    @Log(actionType = ActionType.CARD_EXPORT)
    public Page<CardDataVW> export(CardDataVW card, Long fromIssueDate, Long toIssueDate, Long fromExpDate,
            Long toExpDate, Pagination pagination, Map<String, Operator> filterOperators, Boolean details,
            String accountNumber) throws NoSuchFieldException, IntrospectionException, IllegalAccessException,
            ParseException, InvocationTargetException {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String> projection = new ArrayList<>(configService.getCardMainColumns());
        if (privileges == null || privileges.isEmpty())
            return null;
        if (details)
            projection.addAll(configService.getCardDetailsColumns());
        projection = filterProjectionList(projection, privileges);
        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(configService.getMaxExportSize());
        }
        Page<CardDataVW> cards = search(card, fromIssueDate, toIssueDate, fromExpDate, toExpDate, pagination,
                filterOperators, projection, accountNumber);
        return cards;
    }

    /**
     * @param cardNo          This Parameter Represent The Number Of The Card
     * @param oldMobileNumber This Parameter Represent The Old Product Of The Card
     *                        As An String ( Used For The Log )
     * @param mobileNumber    This Parameter Represent The New Product Of The Card
     *                        As An String
     *                        ( Used For The Log And Is Used For The Soap Request )
     *                        <p>
     *                        This Function Fill An Xml Object For Change The Mobile
     *                        Number Of The Card And Then Sent It To
     *                        Fatora Servers (SmartVista Core)
     */


    @Log(actionType = ActionType.CHANGE_MOBILE_NUMBER)
    public ResponseEntity<ActionResponse> changeMobileNumber(Long actionRequestId, @LogCardNumber String cardNo,
            @LogMobileNumber String mobileNumber,
            @LogOldValue String oldMobileNumber) {
        try {

            // card data
            CardsBo card = cardsBoService.getCardBoByNumber(cardNo);
            if (card.getCommunAdress().equals(mobileNumber)) {
                actionRequestService.saveErrorMessage(actionRequestId,
                        messageSource.getMessage("mobile_exist", null, null));
                CustomUserDetails.getCurrentInstance().getErrorsList()
                        .add(mobileNumber + " " + messageSource.getMessage("mobile_exist", null, null));
            }
            CardMobileNumberDTO cardMobileNumberDTO = new CardMobileNumberDTO(card.getCardNumber(),
                    card.getCustomerNumber(), card.getAgentId(), card.getContractNumber(), card.getCardHolderNumber(),
                    card.getCommunAdress(), mobileNumber, card.getPreferredLang(), card.getPreferredLang());
            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("changeCardMobile");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<CardMobileNumberDTO> request = new HttpEntity<>(cardMobileNumberDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    /**
     * @param cardNo     This Parameter Represent The Number Of The Card
     * @param productId  This Parameter Represent The New Product Of The Card As An
     *                   Integer ( Used For The Soap Request )
     * @param oldProduct This Parameter Represent The Old Product Of The Card As An
     *                   String ( Used For The Log )
     * @param newProduct This Parameter Represent The New Product Of The Card As An
     *                   String ( Used For The Log )
     *                   <p>
     *                   This Function Fill An Xml Object For Change The Product Of
     *                   The Card And Then Sent It To Fatora Servers
     *                   (SmartVista Core)
     */

    @Log(actionType = ActionType.CHANGE_PRODUCT)
    public ResponseEntity<ActionResponse> changeProduct(Long actionRequestId, @LogCardNumber String cardNo,
            @LogOldValue String oldProduct,
            @LogIdAndProduct String newProduct, Long productId) {
        try {

            // card data
            CardsBo card = cardsBoService.getCardBoByNumber(cardNo);

            ChangeProductDTO changeProductDTO = new ChangeProductDTO(card.getCustomerNumber(),
                    card.getContractNumber(), String.valueOf(productsService.getProductById(productId).getProductNumber()),productId,card.getAgentId());
            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("changeCardProduct");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<ChangeProductDTO> request = new HttpEntity<>(changeProductDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    XmlIssueError result = xmlMapper.readValue(response.getBody().getError(), XmlIssueError.class);
                    response.getBody().setError(result.getFaultString());
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    final Environment environment;
    final RestTemplate restTemplate;
    final ActionRequestService actionRequestService;

    @Log(actionType = ActionType.Close_Card)
    public ResponseEntity<ActionResponse> closeCard(Long actionRequestId, @LogCardNumber String cardNo,
            @LogStatus String newStatus) {
        try {

            // card data
            CardDataVW card = readCardDao.getCardByNumber(cardNo);
            CloseCardDTO closeCardDTO = new CloseCardDTO(card.getCrefNo(), card.getStatusDesc(),
                    card.getExpDate().toString(), card.getCustomerNumberCards());
            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("destroyCardApi");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<CloseCardDTO> request = new HttpEntity<>(closeCardDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    XmlIssueError result = xmlMapper.readValue(response.getBody().getError(), XmlIssueError.class);
                    response.getBody().setError(result.getFaultString());
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    @Log(actionType = ActionType.CHANGE_LANGUAGE)
    public ResponseEntity<ActionResponse> changeLanguage(Long actionRequestId, @LogCardNumber String cardNo,
            @LogOldValue String oldLanguage, @LogLanguage String language) {
        try {
            CardDataVW card = readCardDao.getCardByNumber(cardNo);
            CardLanguageDTO cardLanguageDTO = CardLanguageDTO.builder().Card_Number(cardNo).Language(language)
                    .Customer_Number(card.getCustomerNumberCards()).Expiry_Date(card.getExpDate().toString()).build();

            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("changeCardLanguage");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<CardLanguageDTO> request = new HttpEntity<>(cardLanguageDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    @Log(actionType = ActionType.REISSUE_CARD)
    public ResponseEntity<ActionResponse> reissuecard(Long actionRequestId, String cardNo, String expiryDate,
            String deliveryBranch, int instantCard, int oldExpiry, int oldCardNumber, @LogOldValue String oldValue,
            @LogStatus String newValue) {
        try {

            // card data
            CardsBo card = cardsBoService.getCardBoByNumber(cardNo);
            ReissueCardDTO resissueCardDTO = new ReissueCardDTO(cardNo, expiryDate, card.getCustomerNumber(),
                    card.getCardHolderNumber(), card.getAgentId(), deliveryBranch, oldCardNumber, oldExpiry,
                    instantCard);
            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("reissueCardApi");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<ReissueCardDTO> request = new HttpEntity<>(resissueCardDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    XmlIssueError result = xmlMapper.readValue(response.getBody().getError(), XmlIssueError.class);
                    response.getBody().setError(result.getFaultString());
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    @Log(actionType = ActionType.RENEW_CARD)
    public ResponseEntity<ActionResponse> renewcard(Long actionRequestId, String cardNo, String expiryDate,
            String deliveryBranch, int instantCard, int oldExpiry, int oldCardNumber, @LogOldValue String oldValue,
            @LogStatus String newValue) {
        try {

            // card data
            CardsBo card = cardsBoService.getCardBoByNumber(cardNo);
            ReissueCardDTO resissueCardDTO = new ReissueCardDTO(cardNo, expiryDate, card.getCustomerNumber(),
                    card.getCardHolderNumber(), card.getAgentId(), deliveryBranch, oldCardNumber, oldExpiry,
                    instantCard);
            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("reissueCardApi");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<ReissueCardDTO> request = new HttpEntity<>(resissueCardDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    XmlIssueError result = xmlMapper.readValue(response.getBody().getError(), XmlIssueError.class);
                    response.getBody().setError(result.getFaultString());
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    @Log(actionType = ActionType.INSTANT_REISSUE_CARD)
    public ResponseEntity<ActionResponse> instantReissuecard(Long actionRequestId, String cardNo, String expiryDate,
            String deliveryBranch, int instantCard, int oldExpiry, int oldCardNumber, @LogOldValue String oldValue,
            @LogStatus String newValue) {
        try {

            // card data
            CardsBo card = cardsBoService.getCardBoByNumber(cardNo);
            ReissueCardDTO resissueCardDTO = new ReissueCardDTO(cardNo, expiryDate, card.getCustomerNumber(),
                    card.getCardHolderNumber(), card.getAgentId(), deliveryBranch, oldCardNumber, oldExpiry,
                    instantCard);
            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("reissueCardApi");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<ReissueCardDTO> request = new HttpEntity<>(resissueCardDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    XmlIssueError result = xmlMapper.readValue(response.getBody().getError(), XmlIssueError.class);
                    response.getBody().setError(result.getFaultString());
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }

    @Log(actionType = ActionType.INSTANT_RENEW_CARD)
    public ResponseEntity<ActionResponse> instantRenewcard(Long actionRequestId, String cardNo, String expiryDate,
            String deliveryBranch, int instantCard, int oldExpiry, int oldCardNumber, @LogOldValue String oldValue,
            @LogStatus String newValue) {
        try {

            // card data
            CardsBo card = cardsBoService.getCardBoByNumber(cardNo);
            ReissueCardDTO resissueCardDTO = new ReissueCardDTO(cardNo, expiryDate, card.getCustomerNumber(),
                    card.getCardHolderNumber(), card.getAgentId(), deliveryBranch, oldCardNumber, oldExpiry,
                    instantCard);
            String url = environment.getProperty("FatoraServiceUrl") + environment.getProperty("reissueCardApi");
            String username = environment.getProperty("BasicAuthUsername");
            String password = environment.getProperty("BasicAuthPassword");
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(username, password);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<ReissueCardDTO> request = new HttpEntity<>(resissueCardDTO, headers);
            ResponseEntity<ActionResponse> response = restTemplate.postForEntity(url, request, ActionResponse.class,
                    new Object());
            System.out.println("request : " + request);
            System.out.println("*****************************************************************");
            System.out.println("response : " + response);
            if (response.getBody() != null) {
                if (response.getBody().getSuccess() == 0) {
                    XmlMapper xmlMapper = new XmlMapper();
                    xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    XmlIssueError result = xmlMapper.readValue(response.getBody().getError(), XmlIssueError.class);
                    response.getBody().setError(result.getFaultString());
                    actionRequestService.saveErrorMessage(actionRequestId, response.getBody().getError());
                }
            } else
                actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            actionRequestService.saveErrorMessage(actionRequestId, ActionStatus.CONNECTION_ERROR.getType());
            return null;
        }
    }
}
