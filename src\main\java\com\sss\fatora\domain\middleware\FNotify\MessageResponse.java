package com.sss.fatora.domain.middleware.FNotify;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter

public class MessageResponse implements Serializable {

    @JsonProperty("Id")
    private Long Id;

    @JsonProperty("Identifier")
    private String Identifier;

    @JsonProperty("Mobile_Number")
    private String Mobile_Number;

    @JsonProperty("Message_Type")
    private String Message_Type;

    @JsonProperty("Text")
    private String Text;

    @JsonProperty("Response")
    private String Response;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "Asia/Damascus")
    @JsonProperty("FNotif_Receive_Date")
    private String FNotif_Receive_Date;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "Asia/Damascus")
    @JsonProperty("Provider_Response_Date")
    private String Provider_Response_Date;

    @JsonProperty("Success")
    private Boolean Success;
}

