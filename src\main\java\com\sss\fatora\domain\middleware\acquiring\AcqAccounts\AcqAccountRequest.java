package com.sss.fatora.domain.middleware.acquiring.AcqAccounts;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AcqAccountRequest extends AcqAccount{
    private String bank;
    private List<String> fields;
    private String sorting;
    private Integer maxResultCount;
    private Integer skipCount;
}
