package com.sss.fatora.service.read;


import com.sss.fatora.dao.read.CountryCodesDao;
import com.sss.fatora.domain.read.CountryCodes;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@Transactional("readingDBTransactionManager")
public class CountryCodesService extends GenericReadService<CountryCodesDao, CountryCodes, Long> {

    /**
     * This Function Get All Country Codes And Send Them To The Front-End
     * */
    public List<CountryCodes> getCountryCodes() {
        List<CountryCodes> countryCodes = dao.findAll();
        /* for (CountryCodes countryCode : countryCodes) {
         *//*  if (String.valueOf(countryCode.getCode()).length() == 2) {
                countryCode.setCode("0" + countryCode.getCode());

            }
            if (String.valueOf(countryCode.getCode()).length() == 1) {
                countryCode.setCode("00" + countryCode.getCode());

            }*//*

        }*/
        return countryCodes;
    }
}
