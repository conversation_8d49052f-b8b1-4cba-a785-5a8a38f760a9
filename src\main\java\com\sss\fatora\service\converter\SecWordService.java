package com.sss.fatora.service.converter;


import com.sss.fatora.domain.converter.SecWord;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class SecWordService extends GenericConverterService<SecWord> {
    public SecWord prepareSecWordObject(SecWord secWord, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {
        getPropertyValueByExcel(secWord, excelIndexes, row);
        return secWord;
    }
    public SecWord prepareSecWordObjectFromPanel(SecWord secWord, Map<String, String> fixedValues) throws IllegalAccessException {
        getPropertyValueByObject(secWord, fixedValues);
        return secWord;
    }
}
