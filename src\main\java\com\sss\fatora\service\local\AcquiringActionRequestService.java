package com.sss.fatora.service.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.dao.local.AcquiringRequestDao;
import com.sss.fatora.domain.local.*;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.domain.read.DTO.ActionResponse;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.converter.ExcelFileService;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.service.read.CardStatusService;
import com.sss.fatora.service.read.ProductsService;
import com.sss.fatora.service.read.ReadAccountService;
import com.sss.fatora.service.read.ReadCardService;
import com.sss.fatora.service.write.CrefTabService;
import com.sss.fatora.utils.constants.*;
import com.sss.fatora.utils.model.Pagination;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.sss.fatora.utils.constants.ActionType.*;

@Service
@Transactional("localDBTransactionManager")
public class AcquiringActionRequestService extends GenericService<AcquiringRequestDao, AcquiringActionRequest, Long> {

    @Autowired
    ReadCardService readCardService;
    @Autowired
    ReadAccountService readAccountService;
    @Autowired
    CrefTabService crefTabService;
    @Autowired
    ApplicationUserService applicationUserService;
    @Autowired
    CardStatusService cardStatusService;
    @Autowired
    LogService logService;
    @Autowired
    ExcelFileService excelFileService;
    @Autowired
    ActionRequestJSONService actionRequestJSONService;
    @Autowired
    RequestsLogService requestsLogService;
    @Autowired
    ProductsService productsService;

    /**
     * @param request This Parameter Is For The Information To Be Saved
     *                <p>
     *                This Function Is For Saving The Action Request In Db
     */

    public AcquiringActionRequest saveRequest(AcquiringActionRequest request) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Date date = new Date();
            RequestsLog requestsLog = new RequestsLog();
            // if (request.getActionType().equalsIgnoreCase(CHANGE_PRODUCT.getAction())) {
            //     setProductOldValue(request);
            // }
            String jsonActionRequestDetails = objectMapper.writeValueAsString(request.getAcquiringActionRequest());
            request.setActionDetails(jsonActionRequestDetails);
            request.setAction(ActionStatus.PENDING.getType());
            request.setUserRequestStatus(UserRequestStatus.PENDING.getType());
            request.setApplicationUser(CustomUserDetails.getCurrentInstance().getApplicationUser());
            request.setRequestDate(date);
            request.setModifiedDate(date);
            saveLog(request);
            AcquiringActionRequest savedActionRequest = dao.save(request);
            requestsLog.setAction(RequestsActionsEnum.CREATE.getStringValue());
            requestsLog.setRequestId(savedActionRequest.getId().toString());
            requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());
            requestsLogService.merge(requestsLog);
            return savedActionRequest;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return null;
    }

    // private void setProductOldValue(AcquiringActionRequest request) {
    //     String productOldName = request.getAcquiringActionRequest().getOldValue();
    //     Long productOldNumber = productsService.getProductNumberByLabel(productOldName);
    //     String oldValue = "";
    //     if (productOldNumber != null)
    //         oldValue = productOldNumber.toString();
    //     oldValue += "- " + productOldName;
    //     request.getAcquiringActionRequest().setOldValue(oldValue);
    // }

    public void saveLog(AcquiringActionRequest request) {
        try {

            com.sss.fatora.domain.local.Log log = new com.sss.fatora.domain.local.Log();
            log.setRecordStatus(1);
            log.setType(LogType.Actions.getType());
            log.setAction(request.getActionType());
            log.setCreatorId(CustomUserDetails.getCurrentInstance().getApplicationUser().getCreatorId());
            log.setCreationDate(new Date());
            log.setRelatedEntity(new CardDataVW().getRelatedEntity());
            log.setApplicationUser(CustomUserDetails.getCurrentInstance().getApplicationUser());
            log.setBank(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank());

            if (request.getActionType().equalsIgnoreCase(CHANGE_STATUS.getAction())) {
                log.setActionValue(request.getAcquiringActionRequest().getStatus().toString());
                log.setOldValue(request.getAcquiringActionRequest().getOldValue());
            } else if (request.getActionType().equalsIgnoreCase(CARD_RESET.getAction())) {
                log.setActionValue("0");
                log.setOldValue(request.getAcquiringActionRequest().getOldValue());
            } else if (!request.getActionType().equalsIgnoreCase(MANAGE_ACCOUNTS.getAction())) {
                log.setActionValue(request.getAcquiringActionRequest().getNewValue());
                log.setOldValue(request.getAcquiringActionRequest().getOldValue());
            }

            logService.merge(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param actionRequest   This Parameter Is For The Value Of Filters Send From
     *                        The Front-End
     * @param fromDate,toDate This Two Parameters Represent The Period We Want To
     *                        Search In
     * @param filterOperator  There Is An Specific Operator For Every Filter
     *                        {@link Operator}
     * @param fullname        This Parameter Is Added To Where Clause
     *                        <p>
     *                        This Function Add Certain Conditions To Where Clause
     *                        That Can't Be Added To The Filter (AcquiringActionRequest)
     *                        Directly And Then Request The Dynamic Search Function
     *                        ( dynamicSearch() )
     */

    public Page<AcquiringActionRequest> search(AcquiringActionRequest actionRequest, Long fromDate, Long toDate, Long fromModifiedDate,
            Long toModifiedDate,
            Long fromApprovalDate, Long toApprovalDate, Pagination pagination, Map<String, Operator> filterOperator,
            Integer userId)
            throws NoSuchFieldException, IllegalAccessException, IntrospectionException, InvocationTargetException {
        Page<AcquiringActionRequest> actionRequests;
        String additionalConstraint = "";
        String conditionForJoin = "";
        DateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.mmm");

        if (actionRequest == null)
            actionRequest = new AcquiringActionRequest();

        if (fromApprovalDate != null || toApprovalDate != null) {
            if (fromApprovalDate == null)
                fromApprovalDate = new Date(1612178075113L).getTime();
            if (toApprovalDate == null)
                toApprovalDate = new Date(7258118400L * 1000).getTime();
            additionalConstraint = "AcquiringActionRequest.approvalDate between '" + f.format(fromApprovalDate) + "' AND '"
                    + f.format(toApprovalDate) + "'";
        }
        if (fromModifiedDate != null || toModifiedDate != null) {
            if (fromModifiedDate == null)
                fromModifiedDate = new Date(1612178075113L).getTime();
            if (toModifiedDate == null)
                toModifiedDate = new Date(7258118400L * 1000).getTime();
            additionalConstraint = "AcquiringActionRequest.modifiedDate between '" + f.format(fromModifiedDate) + "' AND '"
                    + f.format(toModifiedDate) + "'";
        }
        if (fromModifiedDate == null && toModifiedDate == null && fromApprovalDate == null && toApprovalDate == null) {
            if (fromDate == null) {
                fromDate = new Date(1612178075113L).getTime();
            }
            if (toDate == null) {
                toDate = new Date(7258118400L * 1000).getTime();
            }
            additionalConstraint = "AcquiringActionRequest.requestDate between '" + f.format(fromDate) + "' AND '"
                    + f.format(toDate) + "'";
        } else if (fromDate != null || toDate != null) {
            if (fromDate == null) {
                fromDate = new Date(1612178075113L).getTime();
            }
            if (toDate == null) {
                toDate = new Date(7258118400L * 1000).getTime();
            }
            additionalConstraint += " And AcquiringActionRequest.requestDate between '" + f.format(fromDate) + "' AND '"
                    + f.format(toDate) + "'";
        }
        if (userId != null) {
            conditionForJoin = " AND ApplicationUser.id = '" + userId + "' ";
            additionalConstraint += conditionForJoin;
        }

        if (actionRequest.getUserRequestStatus() != null
                && actionRequest.getUserRequestStatus().equalsIgnoreCase("Processed")) {
            additionalConstraint += " AND (AcquiringActionRequest.userRequestStatus IN ('Approved','Rejected'))";
            actionRequest.setUserRequestStatus(null);
        }
        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("AcquiringActionRequest.creationDate");
            pagination.setOrderType("DESC");
        }



        actionRequests = this.dynamicSearch(actionRequest, pagination, additionalConstraint, filterOperator, null, true);

        return actionRequests;

    }

    /**
     * This Function Is For Adding A Join Statement To The Query
     */
    // public String addJoinToQuery() {
    //     String join = " LEFT JOIN ApplicationUser AS ApplicationUser ON ApplicationUser.id = AcquiringActionRequest.applicationUser ";
    //     return join;
    // }

    /**
     * @param actionRequest This Parameter Is For The Information To Be Sent To The
     *                      Respective Service
     *                      <p>
     *                      This Function Is For Approving The Action Sent And
     *                      Request A Specific Service By Action Type
     */
    public Object approveRequest(List<Long> requestIds) {
        Object resp = null;
        // List<Object> responseList = new ArrayList<>();
        for (Long acquiringActionRequestId : requestIds) {
            ObjectMapper objectMapper = new ObjectMapper();
            RequestsLog requestsLog = new RequestsLog();
            AcquiringActionRequest actionRequestItr = (AcquiringActionRequest) dao.getRequestById(acquiringActionRequestId);
            try {
                AcquiringActionRequestJSON j = objectMapper.readValue(actionRequestItr.getActionDetails(),
                        AcquiringActionRequestJSON.class);
                actionRequestItr.setAcquiringActionRequest(j);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            String type = actionRequestItr.getActionType();
            // if (type.equalsIgnoreCase(AcquiringActionType.CLOSE_POS_TERMINAL.getAction())) {
            //     ResponseEntity<ActionResponse> response = readCardService.changeProduct(actionRequestItr.getId(),
            //             actionRequestItr.getAcquiringActionRequest().getOldValue(),
            //             actionRequestItr.getAcquiringActionRequest().getNewValue(),
            //             Long.valueOf(actionRequestItr.getAcquiringActionRequest().getProductID()));
            //     if (response != null)
            //         resp = response.getBody();
            // } 
            actionRequestItr = getById(actionRequestItr.getId());
            if (resp != null && resp instanceof ActionResponse) {
                System.out.println("**resp instanceof ActionResponse**");
                if (((ActionResponse) resp).getSuccess() == 1)
                    actionRequestItr.setAction(ActionStatus.SUCCESSES.getType());
                else
                    actionRequestItr.setAction(ActionStatus.FAILED.getType());
            } else if ((resp != null && resp instanceof SoapResponse)) {
                System.out.println("**resp instanceof SoapResponse**");
                actionRequestItr.setAction(ActionStatus.SUCCESSES.getType());
            } else {
                actionRequestItr.setAction(ActionStatus.FAILED.getType());
            }
            if (actionRequestItr.getErrorMessage() != null
                    && actionRequestItr.getAction().equalsIgnoreCase(ActionStatus.SUCCESSES.getType())) {
                System.out.println("**Success Request With Error Message**");
                System.out.println("Date : " + new Date());
                System.out.println("**************************************");
            }
            if (actionRequestItr.getUserRequestStatus().equalsIgnoreCase(UserRequestStatus.PENDING.getType()))
                requestsLog.setAction(RequestsActionsEnum.APPROVE.getStringValue());
            // else
            // requestsLog.setAction(RequestsActionsEnum.REPROCESS.getStringValue());
            actionRequestItr.setUserRequestStatus(UserRequestStatus.APPROVED.getType());
            actionRequestItr.setApprovalDate(new Date());

            AcquiringActionRequest savedActionRequest = dao.save(actionRequestItr);
            requestsLog.setRequestId(savedActionRequest.getId().toString());
            requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());

            try {
                requestsLogService.merge(requestsLog);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
            actionRequestItr.setApplicationUser(CustomUserDetails.getCurrentUser());
        }
        return resp;
    }

    /**
     * @param requestIds This Parameter Indicates To The Requests To Be Rejected
     *                   <p>
     *                   This Function Is For Reject The Action Sent
     */
    public Boolean rejectRequest(List<Long> requestIds) {
        // actionRequest.setUserRequestStatus(UserRequestStatus.DENIED.getType());
        // actionRequest.setActionStatus(ActionStatus.USER_REQUEST_DENIED.getType());
        for (Long requestId : requestIds) {
            RequestsLog requestsLog = new RequestsLog();
            requestsLog.setAction(RequestsActionsEnum.REJECT.getStringValue());
            requestsLog.setRequestId(requestId.toString());
            requestsLog.setUserName(CustomUserDetails.getCurrentUser().getFullName());
            try {
                requestsLogService.merge(requestsLog);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
        // dao.rejectRequest(requestIds, userRequestStatus, actionStatus, CustomUserDetails.getCurrentUser().getId(),
                // new Date());
        return true;
    }

    public void saveErrorMessage(Long actionRequestID, String Error) {
        try {

            AcquiringActionRequest actionRequest = getById(actionRequestID.longValue());
            actionRequest.setErrorMessage(Error);
            merge(actionRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
