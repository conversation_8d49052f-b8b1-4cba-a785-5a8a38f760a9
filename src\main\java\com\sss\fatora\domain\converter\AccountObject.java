package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = {"account_link_flag", "is_pos_default", "is_atm_default"})
public class AccountObject extends GenericConverter {

    public AccountObject() {
    }

    public AccountObject(String account_link_flag, String is_pos_default, String is_atm_default) {
        this.account_link_flag = account_link_flag;
        this.is_pos_default = is_pos_default;
        this.is_atm_default = is_atm_default;
    }

    public AccountObject(String ref_id, String account_link_flag, String is_pos_default, String is_atm_default) {
        this.ref_id = ref_id;
        this.account_link_flag = account_link_flag;
        this.is_pos_default = is_pos_default;
        this.is_atm_default = is_atm_default;
    }

    @ExcelProperty(name = "ref_id10")
    private String ref_id;
    @ExcelProperty(name = "ns1:account_link_flag")
    private String account_link_flag;
    @ExcelProperty(name = "ns1:is_pos_default")
    private String is_pos_default;
    @ExcelProperty(name = "ns1:is_atm_default")
    private String is_atm_default;


    public String getRef_id() {
        return ref_id;
    }

    @XmlAttribute(name = "ref_id")
    public void setRef_id(String ref_id) {
        this.ref_id = ref_id;
    }
}
