package com.sss.fatora.utils.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sss.fatora.domain.middleware.response.ListMiddlewareResponse;
import com.sss.fatora.utils.model.HibernateAwareObjectMapper;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ObjectConverter {
    private final RestTemplate restTemplate;



    public ObjectConverter(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    private static ObjectMapper objectMapper = new ObjectMapper();
    private static HibernateAwareObjectMapper hibernateAwareObjectMapper=new HibernateAwareObjectMapper();

    public static <T> T convertToObject(Object domain, Class<T> clazz){
        try {
            if(domain instanceof String){
                return objectMapper.readValue((String)domain,clazz);
            }
            return objectMapper.convertValue(domain,clazz);
        }catch (Exception e){
            return null;
        }
        
    }

    public static HashMap convertObjectToMap(Object domain){

        HashMap map = hibernateAwareObjectMapper.convertValue(domain, HashMap.class);
        return map;
    }


    public  <T> List<T> getResponseList(String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(null, headers);
        headers.setContentType(MediaType.APPLICATION_JSON);
        restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
        ResponseEntity<ListMiddlewareResponse> response = restTemplate.getForEntity(url, ListMiddlewareResponse.class, request);
        if (response.getBody() != null && !response.getBody().getSuccess()) {
            return null;
        }
        return response.getBody().getResult();
    }


}
