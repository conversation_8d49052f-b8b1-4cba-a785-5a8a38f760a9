package com.sss.fatora.dao.local;

import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface BankDao extends GenericDao<Bank,Integer> {
    @Query(value="SELECT DISTINCT AU FROM ApplicationUser AU " +
            "INNER JOIN FETCH AU.bank B " +
            "WHERE B.id = :bankId",
            countQuery = "SELECT count(DISTINCT AU) FROM ApplicationUser AU ")
    Page<ApplicationUser> getUsers(@Param("bankId") Integer bank_id, Pageable pageRequest);

    @Query(value="SELECT DISTINCT B.code As Code , B.abbreviatedName AS BankName, B.prefix AS Prefix " +
            "FROM Bank B " +
            "WHERE B.id = :bankId")
    List<Map> getPrefixById(@Param("bankId") Integer id);

    @Query(value="SELECT DISTINCT B.code As Code , B.abbreviatedName AS BankName, B.prefix AS Prefix " +
            "FROM Bank B ")
    List<Map> getAllPrefixesById();
}
