//package com.sss.fatora.controller.local;
//
//import com.sss.fatora.controller.generic.GenericController;
//import com.sss.fatora.dao.local.LocalAccountDao;
//import com.sss.fatora.domain.local.application.LocalAccount;
//import com.sss.fatora.service.local.LocalAccountService;
//import com.sss.fatora.utils.model.ResponseObject;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/local-accounts")
//public class LocalAccountController extends GenericController<LocalAccountService, LocalAccountDao, LocalAccount,Integer> {
//
//    @RequestMapping(value = "/card-accounts", method = RequestMethod.GET)
//    public ResponseObject getCardAccounts(@RequestParam("cardId") Long cardId) {
//        List<LocalAccount> localApplicationAccountList  = service.getAccountsByApplicationId(cardId);
//        return localApplicationAccountList != null ?
//                ResponseObject.FETCHED_SUCCESS(localApplicationAccountList, null) :
//                ResponseObject.FETCHING_FAILED(null, null);
//    }
//
//}
