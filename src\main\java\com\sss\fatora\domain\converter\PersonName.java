package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

@Setter
@Getter
@XmlType(propOrder = { "surname","first_name","second_name"})
public class PersonName extends GenericConverter {
    @ExcelProperty(name = "language")
    private String language;


    @ExcelProperty(name = "ns1:surname")
    private String surname;
    @ExcelProperty(name = "ns1:first_name")
    private String first_name;


    @ExcelProperty(name = "ns1:second_name")
    private String second_name;


    public String getLanguage() {
        return language;
    }
    @XmlAttribute(name = "language")
    public void setLanguage(String language) {
        this.language = language;
    }
}
