package com.sss.fatora.domain.issuing;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.sss.fatora.domain.converter.GenericConverter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JacksonXmlRootElement(localName = "SOAP-ENV:Fault")
public class XmlIssueError extends GenericConverter {

    @JacksonXmlProperty(localName = "faultcode")
    @JacksonXmlElementWrapper(useWrapping = false)
    private String faultCode;

    @JacksonXmlProperty(localName = "faultstring")
    @JacksonXmlElementWrapper(useWrapping = false)
    private String faultString;
}
