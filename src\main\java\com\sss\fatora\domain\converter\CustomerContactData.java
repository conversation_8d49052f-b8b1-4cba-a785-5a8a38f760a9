package com.sss.fatora.domain.converter;


import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

@Getter
@Setter
@XmlType(propOrder = { "commun_method","commun_address"})
public class CustomerContactData extends GenericConverter {

    @ExcelProperty(name = "ns1:commun_method16")
    private String commun_method;
    @ExcelProperty(name = "ns1:commun_address17")
    private String commun_address;

}
