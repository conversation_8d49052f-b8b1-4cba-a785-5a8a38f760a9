package com.sss.fatora.dao.read;

import com.sss.fatora.domain.read.AccountBo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface AccountBoDao extends GenericReadDao<AccountBo, Long> {
    @Query("SELECT A FROM AccountBo A " +
            "WHERE A.cardNo=:cardNumber " +
            "AND A.posPrimary=true")
    AccountBo getPosAccountBoByNumber(@Param("cardNumber") String cardNumber);

    @Query("SELECT A FROM AccountBo A " +
            "WHERE A.cardNo=:cardNumber " +
            "AND A.atmPrimary=true")
    AccountBo getAtmAccountBoByNumber(@Param("cardNumber") String cardNumber);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Query("SELECT A FROM AccountBo A " +
            "WHERE A.acctNo=:accountNumber ")
    AccountBo getAccountBoByNumber(@Param("accountNumber") String accountNumber);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Query("SELECT A FROM AccountBo A " +
            "WHERE A.acctNo=:accountNumber " +
            "AND A.cardNo=:cardNumber")
    AccountBo getAccountBoByNumberAndCard(@Param("accountNumber") String accountNumber, @Param("cardNumber") String cardNumber);
}
