package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.TransTypeDao;
import com.sss.fatora.domain.read.ResponseCode;
import com.sss.fatora.domain.read.TransType;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional("readingDBTransactionManager")
public class TransTypeService extends GenericReadService<TransTypeDao, TransType, Long> {

    /**
     * This Function Get All Types Of Transactions And Return It To The Global Data
     * */

    public List<TransType> getAllTransTypes() {
        try {
            return dao.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }
}
