package com.sss.fatora.domain.middleware.Merchants;

import com.sss.fatora.domain.middleware.terminals.ResponseError;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class MerchantResponse {
    private MerchantResult result;
    private String targetUrl;
    private Boolean success;
    private ResponseError error;
    private Boolean unAuthorizedRequest;
    private Boolean __abp;
}
