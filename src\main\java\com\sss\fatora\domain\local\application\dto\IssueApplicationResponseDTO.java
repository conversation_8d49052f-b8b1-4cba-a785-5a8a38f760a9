package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class IssueApplicationResponseDTO {

    @JsonProperty(value = "Container_Response_Code")
    private String containerResponseCode;

    @JsonProperty(value = "Card_Data")
    private CardDataResponseDTO cardData;

    @JsonProperty(value = "Error_Message")
    private String errorMessage;
}
