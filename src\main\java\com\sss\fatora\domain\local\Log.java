package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;
import javax.validation.constraints.Max;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "Log")
public class Log extends LocalDomain {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "Action")
    @Nationalized
    private String action;

    @Column(name = "Related_Entity")
    @Nationalized
    private String relatedEntity;

    @Column(name = "Entity_Id")
    @Nationalized
    private String entityId;


    @Column(name = "Query", length = 10000)
    @Nationalized
    private String query;

    @Column(name = "Opened_Record")
    private Long openedRecord;

    @Column(name = "Action_Value", length = 10000)
    @Nationalized
    private String actionValue;


    @Column(name = "Old_Value", length = 10000)
    @Nationalized
    private String oldValue;

    @Column(name = "Export_Options", length = 10000)
    @Nationalized
    private String exportOptions;

    @Column(name = "Log_Type")
    @Nationalized
    private String type;

    @ManyToOne
    @JoinColumn(name = "User_Id" , referencedColumnName = "id")
    @JsonIgnoreProperties("logs")
    private ApplicationUser applicationUser;

    @ManyToOne
    @JoinColumn(name = "Bank_Id" , referencedColumnName = "id")
    @JsonIgnoreProperties({"logs","image"})
    private Bank bank;

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}
