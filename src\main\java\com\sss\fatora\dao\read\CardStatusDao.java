package com.sss.fatora.dao.read;


import com.sss.fatora.domain.read.CardStatusVW;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CardStatusDao extends  GenericReadDao<CardStatusVW, Long> {
    @Query("SELECT CS FROM CardStatusVW CS " +
    "WHERE CS.fpChangeStatus=1")
    List<CardStatusVW>  getActiveChangeStatus();

    @Query("SELECT CS FROM CardStatusVW CS " +
            "WHERE CS.fpChangeStatus=1 OR CS.cdStat=0")
    List<CardStatusVW>  getActiveChangeCardsStatusWithValid();

    @Query("SELECT CS.descX FROM CardStatusVW CS " +
            "WHERE CS.cdStat=:cdStat")
    String getByCdStat(@Param("cdStat") Long cdStat);

    @Query("SELECT CS.cdStat FROM CardStatusVW CS " +
            "WHERE CS.descX like :desc")
    Integer getStatusByDesc(String desc);
}
