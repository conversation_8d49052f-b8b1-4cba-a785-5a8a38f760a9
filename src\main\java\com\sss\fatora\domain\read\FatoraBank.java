package com.sss.fatora.domain.read;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "FAT_COM_BANKS")
public class FatoraBank {

    @Id
    @Column(name = "ID")
    Integer id;

    @Column(name = "CODE")
    String code;

    @Column(name = "NAME")
    String name;

    @Column(name = "SHORT_NAME")
    String shortName;
}
