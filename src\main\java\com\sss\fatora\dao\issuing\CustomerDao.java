package com.sss.fatora.dao.issuing;

import com.sss.fatora.dao.read.GenericReadDao;
import com.sss.fatora.domain.issuing.Customer;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.List;

public interface CustomerDao extends GenericReadDao<Customer,Long> {
    @Query("SELECT C FROM Customer C " +
            "WHERE C.customerNumber LIKE :number")
    Customer getByCustomerNumber(@Param("number")String customerNumber);

    @Query("SELECT C FROM Customer C " +
            "WHERE C.customerNumber LIKE :number")
    List<Customer> getFirstByCustomerNumber(@Param("number")String customerNumber);
}
