package com.sss.fatora.dao.issuing;

import com.sss.fatora.domain.issuing.Cardholder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CardholderDao extends JpaRepository<Cardholder,Long> {
    @Query("SELECT C FROM Cardholder C " +
            "WHERE C.customerId =:customerId")
    List<Cardholder> getByCustomerNumber(@Param("customerId") Long customerId);
}
