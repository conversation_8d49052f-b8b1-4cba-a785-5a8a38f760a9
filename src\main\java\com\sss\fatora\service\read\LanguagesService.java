package com.sss.fatora.service.read;


import com.sss.fatora.dao.read.LanguagesDao;
import com.sss.fatora.domain.read.Languages;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Transactional("readingDBTransactionManager")
public class LanguagesService extends GenericReadService<LanguagesDao, Languages, Long> {

}
