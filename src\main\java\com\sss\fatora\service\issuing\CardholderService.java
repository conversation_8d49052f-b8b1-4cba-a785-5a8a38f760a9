package com.sss.fatora.service.issuing;

import com.sss.fatora.dao.issuing.CardholderDao;
import com.sss.fatora.dao.issuing.CustomerDao;
import com.sss.fatora.dao.issuing.PersonDao;
import com.sss.fatora.domain.issuing.Cardholder;
import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.issuing.Person;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CardholderService {
    final CardholderDao cardholderDao;
    final PersonDao personDao;

    public CardholderService(CardholderDao cardholderDao, PersonDao personDao){
        this.cardholderDao = cardholderDao;
        this.personDao = personDao;
    }

    /**
     * @param customerId This Parameter Is For The Customer Id
     *
     * This Function Get All CardHolders For This Specific Customer
     * */
    public List<Cardholder> getCardHolderByCustomerNumber(Long customerId) {
        List<Cardholder> requestedCardHolders = cardholderDao.getByCustomerNumber(customerId);
        return requestedCardHolders;
    }

}
