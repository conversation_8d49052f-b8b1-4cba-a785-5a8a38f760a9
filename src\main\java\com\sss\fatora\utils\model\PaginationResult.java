package com.sss.fatora.utils.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaginationResult {
    private Object searchResult;
    private Long totalCount;
    private Integer pageSize;
    private String minIdxOfCurrentPage;
    private String maxIdxOfCurrentPage;

}
