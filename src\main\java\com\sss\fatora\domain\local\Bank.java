package com.sss.fatora.domain.local;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.LocalDomain;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Nationalized;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "Bank")
public class Bank extends LocalDomain {

    /*@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BANK_SEQ")
    @SequenceGenerator(sequenceName = "BANK_SEQ",initialValue = 1, allocationSize = 1, name = "BANK_SEQ")*/

    public Bank(Integer id) {
        this.id=id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "Full_Name")
    @Nationalized
    private String fullName;

    @Column(name = "Abbreviated_Name")
    @Nationalized
    private String abbreviatedName;

    @Column(name = "Code")
    @Nationalized
    private String code;

    @Column(name = "Bin")
    @Nationalized
    private String bin;

    @Column(name="Image",length = *********)
    @Nationalized
    private String image;

    @Column(name="Image_Content_Type")
    @Nationalized
    private String imageContentType;

    @Column(name = "Prefix")
    private String prefix;

    @OneToMany(mappedBy = "bank")
   // @JsonManagedReference
   // @JsonIgnoreProperties("bank")
    private Set<ApplicationUser> applicationUsers;

    @OneToMany(mappedBy = "bank")
    @JsonIgnoreProperties("bank")
    private Set<Log> logs;

    @Transient
    private List<String> binCodes;

    public List<String> getBinCodes() {
        if(bin!=null){
            return Arrays.asList(bin.replace("[","").replace("]","").split(","));
        }
        return Collections.emptyList();
    }

    public void setBinCodes(List<String> binCodes) {
        this.binCodes = binCodes;
    }

    @Override
    protected <T extends GenericDao> Class<T> defineDao() {
        return null;
    }
}
