package com.sss.fatora.service.converter;

import com.sss.fatora.domain.converter.PersonName;
import com.sss.fatora.domain.converter.SecWord;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PersonNameService extends GenericConverterService<PersonName> {


    public PersonName preparePersonNameObject(PersonName personName, Map<String, Integer> excelIndexes, Row row) throws IllegalAccessException {

        getPropertyValueByExcel(personName,excelIndexes,row);
        return personName;
    }

    public PersonName preparePersonNameObjectFromPanel(PersonName personName, Map<String, String> fixedValues) throws IllegalAccessException {

        getPropertyValueByObject(personName,fixedValues);
        return personName;
    }
}
