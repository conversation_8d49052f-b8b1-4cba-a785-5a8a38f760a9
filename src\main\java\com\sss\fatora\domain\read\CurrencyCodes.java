package com.sss.fatora.domain.read;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "FAT_FP_CURRENCY_CODES")
public class CurrencyCodes {
    @Id
    @Column(name = "CODE")
    Long code;
    @Column(name = "SHORT_NAME")
    String shortNumber;
    @Column(name = "DESCRIPTION")
    String description;
    @Column(name = "PULLDOWN")
    String pullDown;


}
