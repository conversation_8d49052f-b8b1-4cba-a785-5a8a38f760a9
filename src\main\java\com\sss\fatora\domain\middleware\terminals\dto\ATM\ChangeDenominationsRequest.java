package com.sss.fatora.domain.middleware.terminals.dto.ATM;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class ChangeDenominationsRequest {
    private String terminalId;
    private String oldCassetteOne;
    private String oldCassetteTwo;
    private String oldCassetteThree;
    private String oldCassetteFour;
    private String newCassetteOne;
    private String newCassetteTwo;
    private String newCassetteThree;
    private String newCassetteFour;
}
