package com.sss.fatora.utils.log;

import com.sss.fatora.dao.local.LogDao;
import com.sss.fatora.domain.converter.Account;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.domain.local.Log;
import com.sss.fatora.domain.middleware.CapturedCards.CapturedCard;
import com.sss.fatora.domain.read.TransactionDataVW;
import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.utils.constants.ActionType;
import com.sss.fatora.utils.constants.LogType;
import com.sss.fatora.utils.model.Pagination;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Parameter;

@Aspect
@Component
public class LogAspect {

    @Autowired
    LogDao logDao;

    /**
     * This Function Save Logs For Some Actions ( Change Mobile Number, Reset Pin ..... ), Searches (SettlementTransaction
     * Search, Card Transaction .....), Exports (SettlementTransaction Export , Card Export ..... ) And Details (SettlementTransaction
     * Details , Card Details ..... ) . The Values Of The Saved Log Depends On The Log Type
     * <p>
     * Note : After The Respective Function End's ( The One That Have @Log Above It ) log() Aspect Works
     */
    @AfterReturning(value = "@annotation(com.sss.fatora.utils.log.Log)", returning = "returnValue")
    public void log(JoinPoint joinPoint, Object returnValue) {
        try {
            Object[] args = joinPoint.getArgs();
            ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
            Bank bank = null;
            if (applicationUser.getBank() != null)
                bank = applicationUser.getBank();
            Log log = new Log();
            ActionType action = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(com.sss.fatora.utils.log.Log.class).actionType();
            Parameter[] parameter = ((MethodSignature) joinPoint.getSignature()).getMethod().getParameters();
            String[] parameterNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
            String exportsOptions = action.getAction() + "_All_Pages_";
            int i = 0;
            for (Object arg : args) {
                if (action.getType().equalsIgnoreCase(LogType.EXPORTS.getType())) {
                    exportsOptions = exportOptionsForLog(log, action, exportsOptions, arg, parameterNames[i]);
                    if (arg instanceof CapturedCard) {
                        log.setActionValue(((CapturedCard) arg).getTerminalId());
                        log.setAction(action.getAction());
                        log.setType(action.getType());
                        log.setRelatedEntity("CapturedCards");
                    }
                }
                if (action.getType().equalsIgnoreCase(LogType.Requests.getType())) {
                    log.setAction(action.getAction());
                    log.setType(action.getType());
                    if (parameter[i].isAnnotationPresent(LogCardNumber.class)) {
                        log.setEntityId(arg.toString());
                    } else if (parameter[i].isAnnotationPresent(LogAccountNumber.class)) {
                        if (arg instanceof Account)
                            log.setActionValue(((Account) arg).getAccount_number());
                        else
                            log.setActionValue(arg.toString());
                    } else if (parameter[i].isAnnotationPresent(LogMobileNumber.class) || parameter[i].isAnnotationPresent(LogIdAndProduct.class) || parameter[i].isAnnotationPresent(LogStatus.class)|| parameter[i].isAnnotationPresent(LogLanguage.class)) {
                        log.setActionValue(arg.toString());
                    } else if (parameter[i].isAnnotationPresent(LogOldValue.class)) {
                        log.setOldValue(arg.toString());
                    }
                }
                if (arg instanceof Loggable) {
                    log.setEntityId((((Loggable) arg).fetchId()));
                    log.setRelatedEntity((((Loggable) arg).getRelatedEntity()));
                    log.setAction(action.getAction());
                    log.setType(action.getType());
                    log.setQuery((((Loggable) arg).getQuery()));
                }
                if (action.getType().equalsIgnoreCase(LogType.DETAILS.getType())) {
                    if (arg instanceof SettlementTransaction)
                        log.setEntityId(((SettlementTransaction) returnValue).getRrn());
                    if (arg instanceof TransactionDataVW)
                        log.setEntityId(((TransactionDataVW) returnValue).getOnlineRrn());
                }
                i++;
            }
            log.setBank(bank);
            log.setApplicationUser(applicationUser);
            logDao.save(log);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    private String exportOptionsForLog(Log log, ActionType action, String exportsOptions, Object arg, String parameterName) {
        if (arg instanceof Pagination) {
            exportsOptions = action.getAction() + "_Current_Page_";
        }
        if (arg instanceof Boolean && parameterName.equals("details")) {
            if ((Boolean) arg)
                exportsOptions += ActionType.WITH_DETAILS.getAction();
            else
                exportsOptions += ActionType.WITHOUT_DETAILS.getAction();
            log.setExportOptions(exportsOptions);
        }
        return exportsOptions;
    }
}
