package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CardMobileNumberDTO {
    @JsonProperty(value = "Card_Number")
    String Card_Number;
    @JsonProperty(value = "Customer_Number")
    String Customer_Number;
    @JsonProperty(value = "Agent_Id")
    String Agent_Id;
    @JsonProperty(value = "Contract_Number")
    String Contract_Number;
    @JsonProperty(value = "Cardholder_Number")
    String Cardholder_Number;
    @JsonProperty(value = "Old_Mobile")
    String Old_Mobile;
    @JsonProperty(value = "New_Mobile")
    String New_Mobile;
    @JsonProperty(value = "Old_Language")
    String Old_Language;
    @JsonProperty(value = "New_Language")
    String New_Language;

}
