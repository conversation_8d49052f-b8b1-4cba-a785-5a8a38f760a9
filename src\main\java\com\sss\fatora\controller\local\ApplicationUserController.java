package com.sss.fatora.controller.local;

import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.ApplicationUserDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ApplicationUserService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/user")
public class ApplicationUserController
                extends GenericController<ApplicationUserService, ApplicationUserDao, ApplicationUser, Integer> {
        @Override
        @PreAuthorize("hasAnyAuthority('Admin','Define Users')")
        public ResponseObject getAll(@ParameterName(value = "pagination", required = false) Pagination pagination,
                        @RequestParam(required = false) int... status) {
                return super.getAll(pagination, status);
        }

        @RequestMapping(value = "enable-user", method = RequestMethod.POST)
        public ResponseObject enableUser(@RequestParam("id") Integer id) throws Exception {
                Boolean returned_user = service.enableUser(id);
                return returned_user != null ? ResponseObject.ADDED_SUCCESS(returned_user, null)
                                : ResponseObject.ADDING_FAILED(null, null);
        }

        @RequestMapping(value = "disable-user", method = RequestMethod.POST)
        public ResponseObject disableUser(@RequestParam("id") Integer id) throws Exception {
                Boolean returned_user = service.disableUser(id);
                return returned_user != null ? ResponseObject.ADDED_SUCCESS(returned_user, null)
                                : ResponseObject.ADDING_FAILED(null, null);
        }

        // @PreAuthorize("hasAnyAuthority('Adm','List Data')")
        @RequestMapping(value = "force_update_password", method = RequestMethod.POST)
        public ResponseObject forceUpdatePassword(CustomUserDetails customUserDetails,
                        @RequestParam("oldPassword") String oldPassword,
                        @RequestParam("newPassword") String newPassword) throws Exception {
                ResponseObject returned_user = service.forceUpdateUser(customUserDetails.getApplicationUser(),
                                oldPassword, newPassword);
                return returned_user != null ? returned_user : ResponseObject.UPDATING_FAILED(null, null);
        }

        @PreAuthorize("hasAnyAuthority('Admin','Users')")
        @RequestMapping(value = "/search", method = RequestMethod.POST)
        public ResponseObject search(@ParameterName(value = "pagination", required = false) Pagination pagination,
                        @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator,
                        @ParameterName(value = "filter", required = false) ApplicationUser applicationUser)
                        throws IntrospectionException, ParseException, IllegalAccessException, NoSuchFieldException,
                        InvocationTargetException {

                Page<ApplicationUser> page = service.search(pagination,
                                Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap(),
                                applicationUser);
                Map<String, Object> count = new HashMap<>();
                count.put("count", page.getTotalElements());
                return ResponseObject.FETCHED_SUCCESS(page.getContent(), count);

        }

        @PreAuthorize("hasAnyAuthority('Admin')")
        @RequestMapping(value = "/reset-password", method = RequestMethod.POST)
        public ResponseObject resetPassword(
                        @ParameterName(value = "applicationUserId", required = false) Integer applicationUserId,
                        @RequestParam("newPassword") String newPassword,
                        @RequestParam(value = "oldPassword", required = false) String oldPassword) {
                Boolean success = service.resetPassword(applicationUserId, newPassword, oldPassword);
                return success ? ResponseObject.UPDATED_SUCCESS(null, null)
                                : ResponseObject.UPDATING_FAILED(null, null);
        }

        @RequestMapping(value = "/change-password", method = RequestMethod.POST)
        public ResponseObject changePassword(
                        @ParameterName(value = "applicationUserId", required = false) Integer applicationUserId,
                        @RequestParam("newPassword") String newPassword,
                        @RequestParam(value = "oldPassword", required = false) String oldPassword) {
                Boolean success = service.resetPassword(applicationUserId, newPassword, oldPassword);
                return success ? ResponseObject.UPDATED_SUCCESS(null, null)
                                : ResponseObject.UPDATING_FAILED(null, null);
        }

        @PreAuthorize("hasAnyAuthority('Force Delete')")
        @RequestMapping(value = "/force-delete", method = RequestMethod.DELETE)
        public ResponseObject forceDeleteUser(@RequestParam(value = "id") Integer id) {
                Boolean success = service.delete(id, true);
                return success ? ResponseObject.DELETED_SUCCESS(null, null) : ResponseObject.RESPONSE_WITH_ERRORS();
        }

        @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
        public ResponseObject deleteUser(@RequestParam(value = "id") Integer id) {
                Boolean success = service.delete(id, false);
                return success ? ResponseObject.DELETED_SUCCESS(null, null) : ResponseObject.RESPONSE_WITH_ERRORS();
        }

        @RequestMapping(value = "/prefix", method = RequestMethod.GET)
        public ResponseObject userPrefix(@RequestParam("forSearch") Boolean forSearch) {
                List<Map> prefixes = service.getUserPrefix(forSearch);
                return prefixes != null ? ResponseObject.FETCHED_SUCCESS(prefixes, null)
                                : ResponseObject.FETCHING_FAILED(null, null);
        }

        @RequestMapping(value = "/bank-and-internal", method = RequestMethod.GET)
        public ResponseObject getExternalAndInternalUsers() {
                List<Map<String,Object>> users = service.getExternalAndInternalUsers();
                return users != null ? ResponseObject.FETCHED_SUCCESS(users, null)
                                : ResponseObject.FETCHING_FAILED(null, null);
        }

}
