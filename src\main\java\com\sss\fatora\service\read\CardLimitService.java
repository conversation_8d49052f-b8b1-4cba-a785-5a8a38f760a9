package com.sss.fatora.service.read;

import com.sss.fatora.dao.read.CardLimitDao;
import com.sss.fatora.domain.read.CardLimit;
import com.sss.fatora.service.generic.GenericReadService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CardLimitService extends GenericReadService<CardLimitDao, CardLimit,Integer> {
    public List<CardLimit> getByCardNumber(String cardNumber) {
        return dao.getByCardNumber(cardNumber);
    }
}
