#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000000006d94a6a5, pid=17068, tid=8236
#
# JRE version: Java(TM) SE Runtime Environment (8.0_31-b13) (build 1.8.0_31-b13)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.31-b07 mixed mode windows-amd64 compressed oops)
# Problematic frame:
# V  [jvm.dll+0x11a6a5]
#
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   http://bugreport.java.com/bugreport/crash.jsp
#

---------------  T H R E A D  ---------------

Current thread (0x000000001ab17000):  JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_in_vm, id=8236, stack(0x000000001af10000,0x000000001b010000)]

siginfo: ExceptionCode=0xc0000005, reading address 0xffffffffffffffff

Registers:
RAX=0x000000001b00f701, RBX=0x000000001ab17000, RCX=0x6a624f0000000029, RDX=0x0000000000000000
RSP=0x000000001b00f6b0, RBP=0x6a624f0000000029, RSI=0x000000001b00f7c8, RDI=0x00000000022e3180
R8 =0x000000001ab17000, R9 =0x0000000000000001, R10=0x0000000000008000, R11=0x000000001b00f600
R12=0x0000000000000000, R13=0x0000000019609fe8, R14=0x0000000000000000, R15=0x0000000000000000
RIP=0x000000006d94a6a5, EFLAGS=0x0000000000010202

Top of Stack: (sp=0x000000001b00f6b0)
0x000000001b00f6b0:   000000001ab17000 000000001fee5b60
0x000000001b00f6c0:   000000001b00f778 000000006db47c1b
0x000000001b00f6d0:   000000001ab17000 000000006db6447d
0x000000001b00f6e0:   0000000000000000 000000006ede9149
0x000000001b00f6f0:   00000000226322f8 000000006edf2912
0x000000001b00f700:   000000001ab17000 0000000000000000
0x000000001b00f710:   0000000000000000 0000000000000000
0x000000001b00f720:   6a624f0000000029 000000006edf1a09
0x000000001b00f730:   000000001b00f7c8 000000001b00f7a0
0x000000001b00f740:   0000000000000001 00000000226322f8
0x000000001b00f750:   6a624f0000000029 000000006edd53b3
0x000000001b00f760:   000000001b00f890 0000000000000001
0x000000001b00f770:   0000000000000001 00000000226322f8
0x000000001b00f780:   00000000f000100a 0000000000000000
0x000000001b00f790:   0000000000000000 0000000000000000
0x000000001b00f7a0:   0000000000000001 000000006edd5571 

Instructions: (pc=0x000000006d94a6a5)
0x000000006d94a685:   cc cc cc cc cc cc cc cc cc cc cc 48 83 ec 28 48
0x000000006d94a695:   85 c9 75 07 33 c0 48 83 c4 28 c3 48 89 5c 24 20
0x000000006d94a6a5:   48 8b 19 48 85 db 74 20 48 83 fb 37 74 1a 48 8b
0x000000006d94a6b5:   13 48 8b cb ff 52 10 84 c0 74 0d 48 8b c3 48 8b 


Register to memory mapping:

RAX=0x000000001b00f701 is pointing into the stack for thread: 0x000000001ab17000
RBX=0x000000001ab17000 is a thread
RCX=0x6a624f0000000029 is an unknown value
RDX=0x0000000000000000 is an unknown value
RSP=0x000000001b00f6b0 is pointing into the stack for thread: 0x000000001ab17000
RBP=0x6a624f0000000029 is an unknown value
RSI=0x000000001b00f7c8 is pointing into the stack for thread: 0x000000001ab17000
RDI=0x00000000022e3180 is an unknown value
R8 =0x000000001ab17000 is a thread
R9 =0x0000000000000001 is an unknown value
R10=0x0000000000008000 is an unknown value
R11=0x000000001b00f600 is pointing into the stack for thread: 0x000000001ab17000
R12=0x0000000000000000 is an unknown value
R13=0x0000000019609fe8 is an unknown value
R14=0x0000000000000000 is an unknown value
R15=0x0000000000000000 is an unknown value


Stack: [0x000000001af10000,0x000000001b010000],  sp=0x000000001b00f6b0,  free space=1021k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x11a6a5]
V  [jvm.dll+0x33447d]
C  [jdwp.dll+0x21a09]
C  [jdwp.dll+0x53b3]
C  [jdwp.dll+0x5571]
C  [jdwp.dll+0xf09c]
C  [jdwp.dll+0x1f2c9]
C  [jdwp.dll+0x1f49e]
V  [jvm.dll+0x1ac128]
V  [jvm.dll+0x22c194]
V  [jvm.dll+0x28576a]
C  [msvcr100.dll+0x21d9f]
C  [msvcr100.dll+0x21e3b]
C  [KERNEL32.DLL+0x17c24]
C  [ntdll.dll+0x6d721]


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x0000000020035800 JavaThread "DestroyJavaVM" [_thread_blocked, id=9476, stack(0x00000000021e0000,0x00000000022e0000)]
  0x0000000020035000 JavaThread "http-nio-8085-Acceptor" daemon [_thread_in_native, id=8728, stack(0x0000000024400000,0x0000000024500000)]
  0x0000000020038800 JavaThread "http-nio-8085-ClientPoller" daemon [_thread_in_native, id=11596, stack(0x0000000024300000,0x0000000024400000)]
  0x0000000020031000 JavaThread "http-nio-8085-exec-10" daemon [_thread_blocked, id=13020, stack(0x0000000024200000,0x0000000024300000)]
  0x0000000020033800 JavaThread "http-nio-8085-exec-9" daemon [_thread_blocked, id=13644, stack(0x0000000024100000,0x0000000024200000)]
  0x0000000020038000 JavaThread "http-nio-8085-exec-8" daemon [_thread_blocked, id=9816, stack(0x0000000024000000,0x0000000024100000)]
  0x0000000020032800 JavaThread "http-nio-8085-exec-7" daemon [_thread_blocked, id=15276, stack(0x0000000023f00000,0x0000000024000000)]
  0x0000000020030800 JavaThread "http-nio-8085-exec-6" daemon [_thread_blocked, id=4644, stack(0x0000000023e00000,0x0000000023f00000)]
  0x0000000020034000 JavaThread "http-nio-8085-exec-5" daemon [_thread_blocked, id=5660, stack(0x0000000023d00000,0x0000000023e00000)]
  0x0000000020036800 JavaThread "http-nio-8085-exec-4" daemon [_thread_blocked, id=9620, stack(0x0000000023c00000,0x0000000023d00000)]
  0x0000000020037000 JavaThread "http-nio-8085-exec-3" daemon [_thread_blocked, id=8836, stack(0x0000000023b00000,0x0000000023c00000)]
  0x000000001fb85000 JavaThread "http-nio-8085-exec-2" daemon [_thread_blocked, id=12772, stack(0x0000000023a00000,0x0000000023b00000)]
  0x000000001fb83800 JavaThread "http-nio-8085-exec-1" daemon [_thread_blocked, id=18812, stack(0x0000000021b30000,0x0000000021c30000)]
  0x000000001fb82000 JavaThread "http-nio-8085-BlockPoller" daemon [_thread_in_native, id=12324, stack(0x00000000020e0000,0x00000000021e0000)]
  0x000000001fb7f800 JavaThread "scheduling-1" [_thread_blocked, id=8500, stack(0x00000000005d0000,0x00000000006d0000)]
  0x000000001fb84000 JavaThread "container-0" [_thread_blocked, id=18864, stack(0x0000000022530000,0x0000000022630000)]
  0x000000001fb81000 JavaThread "Catalina-utility-2" [_thread_blocked, id=11736, stack(0x0000000022430000,0x0000000022530000)]
  0x000000001fb80800 JavaThread "Catalina-utility-1" [_thread_blocked, id=14720, stack(0x0000000022330000,0x0000000022430000)]
  0x000000001fb82800 JavaThread "HikariPool-4 housekeeper" daemon [_thread_blocked, id=16588, stack(0x0000000021c30000,0x0000000021d30000)]
  0x000000001fb7e800 JavaThread "HikariPool-3 housekeeper" daemon [_thread_blocked, id=17740, stack(0x0000000021a30000,0x0000000021b30000)]
  0x000000001fb7e000 JavaThread "HikariPool-2 housekeeper" daemon [_thread_blocked, id=9932, stack(0x0000000021430000,0x0000000021530000)]
  0x000000001fbde800 JavaThread "InterruptTimer" daemon [_thread_blocked, id=16040, stack(0x0000000021330000,0x0000000021430000)]
  0x000000001fbdc000 JavaThread "oracle.jdbc.driver.BlockSource.ThreadedCachingBlockSource.BlockReleaser" daemon [_thread_blocked, id=14404, stack(0x0000000021230000,0x0000000021330000)]
  0x000000001fbda800 JavaThread "Timer-0" daemon [_thread_blocked, id=12104, stack(0x0000000021130000,0x0000000021230000)]
  0x000000001d954800 JavaThread "HikariPool-1 housekeeper" daemon [_thread_blocked, id=2604, stack(0x0000000020730000,0x0000000020830000)]
  0x000000001ca67800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=16916, stack(0x000000001dc60000,0x000000001dd60000)]
  0x000000001b58a000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=9112, stack(0x000000001c150000,0x000000001c250000)]
  0x000000001b45e000 JavaThread "Service Thread" daemon [_thread_blocked, id=17004, stack(0x000000001bd10000,0x000000001be10000)]
  0x000000001b3cf000 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=15356, stack(0x000000001bc10000,0x000000001bd10000)]
  0x000000001b3b0000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=6596, stack(0x000000001bb10000,0x000000001bc10000)]
  0x000000001b392000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=14636, stack(0x000000001ba10000,0x000000001bb10000)]
  0x000000001ab1e000 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=12640, stack(0x000000001b110000,0x000000001b210000)]
  0x000000001ab1a800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=3388, stack(0x000000001b010000,0x000000001b110000)]
=>0x000000001ab17000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_in_vm, id=8236, stack(0x000000001af10000,0x000000001b010000)]
  0x0000000019602800 JavaThread "Attach Listener" daemon [_thread_blocked, id=5996, stack(0x000000001aa10000,0x000000001ab10000)]
  0x0000000019601800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=18264, stack(0x000000001a910000,0x000000001aa10000)]
  0x00000000023d7000 JavaThread "Finalizer" daemon [_thread_blocked, id=11684, stack(0x000000001a800000,0x000000001a900000)]
  0x00000000023d4000 JavaThread "Reference Handler" daemon [_thread_blocked, id=14576, stack(0x000000001a700000,0x000000001a800000)]

Other Threads:
  0x0000000019575800 VMThread [stack: 0x000000001a600000,0x000000001a700000] [id=16892]
  0x000000001b5a9000 WatcherThread [stack: 0x000000001c250000,0x000000001c350000] [id=18892]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap:
 PSYoungGen      total 335360K, used 35039K [0x0000000780600000, 0x0000000797680000, 0x00000007c0000000)
  eden space 324096K, 7% used [0x0000000780600000,0x0000000781d3e360,0x0000000794280000)
  from space 11264K, 99% used [0x0000000796b80000,0x0000000797679950,0x0000000797680000)
  to   space 18944K, 0% used [0x0000000795180000,0x0000000795180000,0x0000000796400000)
 ParOldGen       total 158208K, used 39970K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 25% used [0x0000000701200000,0x0000000703908bc8,0x000000070ac80000)
 Metaspace       used 74233K, capacity 78362K, committed 78592K, reserved 1118208K
  class space    used 9953K, capacity 10677K, committed 10752K, reserved 1048576K

Card table byte_map: [0x00000000118a0000,0x0000000011ea0000] byte_map_base: 0x000000000e097000

Marking Bits: (ParMarkBitMap*) 0x000000006e00d4f0
 Begin Bits: [0x00000000126a0000, 0x0000000015658000)
 End Bits:   [0x0000000015658000, 0x0000000018610000)

Polling page: 0x0000000000730000

CodeCache: size=245760Kb used=13679Kb max_used=13679Kb free=232080Kb
 bounds [0x00000000024e0000, 0x0000000003240000, 0x00000000114e0000]
 total_blobs=7825 nmethods=7158 adapters=586
 compilation: enabled

Compilation events (10 events):
Event: 3181.589 Thread 0x000000001b3cf000 7244       1       java.security.MessageDigest$Delegate::engineDigest (8 bytes)
Event: 3181.589 Thread 0x000000001b3cf000 nmethod 7244 0x0000000002d1f850 code [0x0000000002d1f9a0, 0x0000000002d1fae8]
Event: 3181.591 Thread 0x000000001b3cf000 7248       1       java.nio.channels.spi.AbstractSelector::cancelledKeys (5 bytes)
Event: 3181.591 Thread 0x000000001b3cf000 nmethod 7248 0x000000000323b3d0 code [0x000000000323b520, 0x000000000323b630]
Event: 3181.593 Thread 0x000000001b3cf000 7249   !   1       sun.security.provider.DigestBase::engineDigest (39 bytes)
Event: 3181.593 Thread 0x000000001b3cf000 nmethod 7249 0x000000000323b690 code [0x000000000323b820, 0x000000000323bb28]
Event: 3181.596 Thread 0x000000001b3cf000 7250   !   1       com.sun.crypto.provider.HmacCore::engineDoFinal (78 bytes)
Event: 3181.597 Thread 0x000000001b3cf000 nmethod 7250 0x000000000323bd10 code [0x000000000323bee0, 0x000000000323c298]
Event: 3181.597 Thread 0x000000001b3cf000 7251       1       javax.crypto.Mac::doFinal (64 bytes)
Event: 3181.598 Thread 0x000000001b3cf000 nmethod 7251 0x000000000323c690 code [0x000000000323c860, 0x000000000323cc08]

GC Heap History (10 events):
Event: 19.667 GC heap before
{Heap before GC invocations=13 (full 3):
 PSYoungGen      total 300544K, used 7632K [0x0000000780600000, 0x0000000795b00000, 0x00000007c0000000)
  eden space 282624K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000791a00000)
  from space 17920K, 42% used [0x0000000792b00000,0x0000000793274020,0x0000000793c80000)
  to   space 17408K, 0% used [0x0000000791a00000,0x0000000791a00000,0x0000000792b00000)
 ParOldGen       total 121856K, used 24764K [0x0000000701200000, 0x0000000708900000, 0x0000000780600000)
  object space 121856K, 20% used [0x0000000701200000,0x0000000702a2f3b8,0x0000000708900000)
 Metaspace       used 56830K, capacity 58968K, committed 59160K, reserved 1099776K
  class space    used 7577K, capacity 7973K, committed 8064K, reserved 1048576K
Event: 19.895 GC heap after
Heap after GC invocations=13 (full 3):
 PSYoungGen      total 300544K, used 0K [0x0000000780600000, 0x0000000795b00000, 0x00000007c0000000)
  eden space 282624K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000791a00000)
  from space 17920K, 0% used [0x0000000792b00000,0x0000000792b00000,0x0000000793c80000)
  to   space 17408K, 0% used [0x0000000791a00000,0x0000000791a00000,0x0000000792b00000)
 ParOldGen       total 158208K, used 25402K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 16% used [0x0000000701200000,0x0000000702ace928,0x000000070ac80000)
 Metaspace       used 56830K, capacity 58968K, committed 59160K, reserved 1099776K
  class space    used 7577K, capacity 7973K, committed 8064K, reserved 1048576K
}
Event: 43.733 GC heap before
{Heap before GC invocations=14 (full 3):
 PSYoungGen      total 300544K, used 282624K [0x0000000780600000, 0x0000000795b00000, 0x00000007c0000000)
  eden space 282624K, 100% used [0x0000000780600000,0x0000000791a00000,0x0000000791a00000)
  from space 17920K, 0% used [0x0000000792b00000,0x0000000792b00000,0x0000000793c80000)
  to   space 17408K, 0% used [0x0000000791a00000,0x0000000791a00000,0x0000000792b00000)
 ParOldGen       total 158208K, used 25402K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 16% used [0x0000000701200000,0x0000000702ace928,0x000000070ac80000)
 Metaspace       used 63099K, capacity 66056K, committed 66304K, reserved 1105920K
  class space    used 8387K, capacity 8914K, committed 8960K, reserved 1048576K
Event: 43.754 GC heap after
Heap after GC invocations=14 (full 3):
 PSYoungGen      total 300032K, used 11632K [0x0000000780600000, 0x0000000798700000, 0x00000007c0000000)
  eden space 282624K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000791a00000)
  from space 17408K, 66% used [0x0000000791a00000,0x000000079255c030,0x0000000792b00000)
  to   space 17408K, 0% used [0x0000000797600000,0x0000000797600000,0x0000000798700000)
 ParOldGen       total 158208K, used 25429K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 16% used [0x0000000701200000,0x0000000702ad5578,0x000000070ac80000)
 Metaspace       used 63099K, capacity 66056K, committed 66304K, reserved 1105920K
  class space    used 8387K, capacity 8914K, committed 8960K, reserved 1048576K
}
Event: 81.920 GC heap before
{Heap before GC invocations=15 (full 3):
 PSYoungGen      total 300032K, used 294256K [0x0000000780600000, 0x0000000798700000, 0x00000007c0000000)
  eden space 282624K, 100% used [0x0000000780600000,0x0000000791a00000,0x0000000791a00000)
  from space 17408K, 66% used [0x0000000791a00000,0x000000079255c030,0x0000000792b00000)
  to   space 17408K, 0% used [0x0000000797600000,0x0000000797600000,0x0000000798700000)
 ParOldGen       total 158208K, used 25429K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 16% used [0x0000000701200000,0x0000000702ad5578,0x000000070ac80000)
 Metaspace       used 67462K, capacity 70940K, committed 71168K, reserved 1110016K
  class space    used 9026K, capacity 9630K, committed 9728K, reserved 1048576K
Event: 81.949 GC heap after
Heap after GC invocations=15 (full 3):
 PSYoungGen      total 365568K, used 17394K [0x0000000780600000, 0x0000000798700000, 0x00000007c0000000)
  eden space 348160K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000795a00000)
  from space 17408K, 99% used [0x0000000797600000,0x00000007986fca88,0x0000000798700000)
  to   space 19968K, 0% used [0x0000000796000000,0x0000000796000000,0x0000000797380000)
 ParOldGen       total 158208K, used 26198K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 16% used [0x0000000701200000,0x0000000702b959d0,0x000000070ac80000)
 Metaspace       used 67462K, capacity 70940K, committed 71168K, reserved 1110016K
  class space    used 9026K, capacity 9630K, committed 9728K, reserved 1048576K
}
Event: 8473.219 GC heap before
{Heap before GC invocations=16 (full 3):
 PSYoungGen      total 365568K, used 365554K [0x0000000780600000, 0x0000000798700000, 0x00000007c0000000)
  eden space 348160K, 100% used [0x0000000780600000,0x0000000795a00000,0x0000000795a00000)
  from space 17408K, 99% used [0x0000000797600000,0x00000007986fca88,0x0000000798700000)
  to   space 19968K, 0% used [0x0000000796000000,0x0000000796000000,0x0000000797380000)
 ParOldGen       total 158208K, used 26198K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 16% used [0x0000000701200000,0x0000000702b959d0,0x000000070ac80000)
 Metaspace       used 73635K, capacity 77386K, committed 77696K, reserved 1116160K
  class space    used 9862K, capacity 10535K, committed 10624K, reserved 1048576K
Event: 8473.284 GC heap after
Heap after GC invocations=16 (full 3):
 PSYoungGen      total 347648K, used 11531K [0x0000000780600000, 0x0000000797680000, 0x00000007c0000000)
  eden space 335872K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000794e00000)
  from space 11776K, 97% used [0x0000000796000000,0x0000000796b42f20,0x0000000796b80000)
  to   space 11264K, 0% used [0x0000000796b80000,0x0000000796b80000,0x0000000797680000)
 ParOldGen       total 158208K, used 39642K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 25% used [0x0000000701200000,0x00000007038b6bc8,0x000000070ac80000)
 Metaspace       used 73635K, capacity 77386K, committed 77696K, reserved 1116160K
  class space    used 9862K, capacity 10535K, committed 10624K, reserved 1048576K
}
Event: 19858.852 GC heap before
{Heap before GC invocations=17 (full 3):
 PSYoungGen      total 347648K, used 347403K [0x0000000780600000, 0x0000000797680000, 0x00000007c0000000)
  eden space 335872K, 100% used [0x0000000780600000,0x0000000794e00000,0x0000000794e00000)
  from space 11776K, 97% used [0x0000000796000000,0x0000000796b42f20,0x0000000796b80000)
  to   space 11264K, 0% used [0x0000000796b80000,0x0000000796b80000,0x0000000797680000)
 ParOldGen       total 158208K, used 39642K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 25% used [0x0000000701200000,0x00000007038b6bc8,0x000000070ac80000)
 Metaspace       used 74204K, capacity 78362K, committed 78592K, reserved 1118208K
  class space    used 9952K, capacity 10677K, committed 10752K, reserved 1048576K
Event: 19858.894 GC heap after
Heap after GC invocations=17 (full 3):
 PSYoungGen      total 335360K, used 11238K [0x0000000780600000, 0x0000000797680000, 0x00000007c0000000)
  eden space 324096K, 0% used [0x0000000780600000,0x0000000780600000,0x0000000794280000)
  from space 11264K, 99% used [0x0000000796b80000,0x0000000797679950,0x0000000797680000)
  to   space 18944K, 0% used [0x0000000795180000,0x0000000795180000,0x0000000796400000)
 ParOldGen       total 158208K, used 39970K [0x0000000701200000, 0x000000070ac80000, 0x0000000780600000)
  object space 158208K, 25% used [0x0000000701200000,0x0000000703908bc8,0x000000070ac80000)
 Metaspace       used 74204K, capacity 78362K, committed 78592K, reserved 1118208K
  class space    used 9952K, capacity 10677K, committed 10752K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Internal exceptions (10 events):
Event: 18927.026 Thread 0x0000000020038000 Exception <a 'java/security/PrivilegedActionException'> (0x0000000791fbc0c0) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18927.029 Thread 0x0000000020038000 Exception <a 'java/security/PrivilegedActionException'> (0x0000000791fd4358) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18927.032 Thread 0x0000000020038000 Exception <a 'java/security/PrivilegedActionException'> (0x00000007920018d8) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18942.706 Thread 0x0000000020034000 Exception <a 'java/security/PrivilegedActionException'> (0x00000007921ddc78) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18942.712 Thread 0x0000000020034000 Exception <a 'java/security/PrivilegedActionException'> (0x00000007921f2cb8) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18973.889 Thread 0x0000000020034000 Exception <a 'java/security/PrivilegedActionException'> (0x0000000792361b98) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18990.087 Thread 0x0000000020034000 Exception <a 'java/security/PrivilegedActionException'> (0x00000007923eb928) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18990.094 Thread 0x0000000020034000 Exception <a 'java/security/PrivilegedActionException'> (0x00000007923f8a20) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18990.098 Thread 0x0000000020034000 Exception <a 'java/security/PrivilegedActionException'> (0x000000079240e6a0) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]
Event: 18990.103 Thread 0x0000000020034000 Exception <a 'java/security/PrivilegedActionException'> (0x00000007924229d8) thrown at [C:\workspace\8-2-build-windows-amd64-cygwin\jdk8u31\2394\hotspot\src\share\vm\prims\jvm.cpp, line 1312]

Events (10 events):
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode done
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode done
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode done
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode done
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode
Event: 20158.521 Executing VM operation: EnterInterpOnlyMode done


Dynamic libraries:
0x00007ff7781e0000 - 0x00007ff778214000 	C:\Program Files\Java\jdk1.8.0_31\bin\java.exe
0x00007ffbae960000 - 0x00007ffbaeb50000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffbad220000 - 0x00007ffbad2d2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffbac560000 - 0x00007ffbac805000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffbad6a0000 - 0x00007ffbad743000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffbadbc0000 - 0x00007ffbadc5e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffbadee0000 - 0x00007ffbadf77000 	C:\WINDOWS\System32\sechost.dll
0x00007ffbaceb0000 - 0x00007ffbacfcf000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbad080000 - 0x00007ffbad214000 	C:\WINDOWS\System32\USER32.dll
0x00007ffbabb30000 - 0x00007ffbabb51000 	C:\WINDOWS\System32\win32u.dll
0x00007ffbace80000 - 0x00007ffbacea6000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffbabb90000 - 0x00007ffbabd28000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffbac920000 - 0x00007ffbac9be000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffbab8d0000 - 0x00007ffbab9ca000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffbaaca0000 - 0x00007ffbaaf24000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.18362.1556_none_9e7e372fe45e41d5\COMCTL32.dll
0x00007ffbad360000 - 0x00007ffbad696000 	C:\WINDOWS\System32\combase.dll
0x00007ffbac830000 - 0x00007ffbac8b1000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbad050000 - 0x00007ffbad07e000 	C:\WINDOWS\System32\IMM32.DLL
0x000000006e090000 - 0x000000006e162000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\msvcr100.dll
0x000000006d830000 - 0x000000006e08a000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\server\jvm.dll
0x00007ffbae910000 - 0x00007ffbae918000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb9bb90000 - 0x00007ffb9bb99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffbadfa0000 - 0x00007ffbae00f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffba8890000 - 0x00007ffba88b4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffba8860000 - 0x00007ffba888d000 	C:\WINDOWS\SYSTEM32\WINMMBASE.dll
0x00007ffbac9c0000 - 0x00007ffbaca0a000 	C:\WINDOWS\System32\cfgmgr32.dll
0x000000006d820000 - 0x000000006d82f000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\verify.dll
0x000000006d7f0000 - 0x000000006d818000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\java.dll
0x000000006edd0000 - 0x000000006ee05000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\jdwp.dll
0x000000006edc0000 - 0x000000006edc8000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\npt.dll
0x000000006ed90000 - 0x000000006edb3000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\instrument.dll
0x000000006d7d0000 - 0x000000006d7e6000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\zip.dll
0x00007ffbae150000 - 0x00007ffbae837000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffbaddd0000 - 0x00007ffbade78000 	C:\WINDOWS\System32\shcore.dll
0x00007ffbabd30000 - 0x00007ffbac4ab000 	C:\WINDOWS\System32\windows.storage.dll
0x00007ffbab860000 - 0x00007ffbab87e000 	C:\WINDOWS\System32\profapi.dll
0x00007ffbab880000 - 0x00007ffbab8ca000 	C:\WINDOWS\System32\powrprof.dll
0x00007ffbab810000 - 0x00007ffbab820000 	C:\WINDOWS\System32\UMPDC.dll
0x00007ffbae010000 - 0x00007ffbae062000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffbab840000 - 0x00007ffbab851000 	C:\WINDOWS\System32\kernel.appcore.dll
0x00007ffbac810000 - 0x00007ffbac827000 	C:\WINDOWS\System32\cryptsp.dll
0x000000006ed80000 - 0x000000006ed89000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\dt_socket.dll
0x00007ffbaa940000 - 0x00007ffbaa9a7000 	C:\WINDOWS\system32\mswsock.dll
0x000000006d7b0000 - 0x000000006d7ca000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\net.dll
0x00007ffbaa6c0000 - 0x00007ffbaa78b000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffbae070000 - 0x00007ffbae078000 	C:\WINDOWS\System32\NSI.dll
0x00007ffbaa680000 - 0x00007ffbaa6ba000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb9e0e0000 - 0x00007ffb9e0ea000 	C:\Windows\System32\rasadhlp.dll
0x00007ffb9f320000 - 0x00007ffb9f397000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffbabb60000 - 0x00007ffbabb86000 	C:\WINDOWS\System32\bcrypt.dll
0x000000006ee10000 - 0x000000006ee1d000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\management.dll
0x000000006d790000 - 0x000000006d7a1000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\nio.dll
0x00007ffbaa4f0000 - 0x00007ffbaa523000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffbab700000 - 0x00007ffbab725000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffbaab60000 - 0x00007ffbaab6c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb9f7c0000 - 0x00007ffb9f7d6000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffba0740000 - 0x00007ffba075c000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffb83ce0000 - 0x00007ffb83cf6000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffb83cc0000 - 0x00007ffb83cda000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffb83c90000 - 0x00007ffb83c9e000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffba6fe0000 - 0x00007ffba6ffc000 	C:\WINDOWS\system32\NLAapi.dll
0x00007ffb83d80000 - 0x00007ffb83d95000 	C:\WINDOWS\system32\wshbth.dll
0x0000000180000000 - 0x0000000180286000 	F:\xampp\tomcat\bin\tcnative-1.dll
0x00007ffbab9d0000 - 0x00007ffbabb21000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffbab820000 - 0x00007ffbab832000 	C:\WINDOWS\System32\MSASN1.dll
0x000000006ed50000 - 0x000000006ed74000 	C:\Program Files\Java\jdk1.8.0_31\jre\bin\sunec.dll
0x00007ffbab200000 - 0x00007ffbab3f4000 	C:\WINDOWS\SYSTEM32\dbghelp.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:1109,suspend=y,server=n -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2020.1\captureAgent\debugger-agent.jar -Dfile.encoding=UTF-8 
java_command: com.sss.fatora.FatoraApplication
java_class_path (initial): C:\Program Files\Java\jdk1.8.0_31\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\deploy.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\access-bridge-64.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\cldrdata.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\dnsns.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\jaccess.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\jfxrt.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\localedata.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\nashorn.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunec.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunjce_provider.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunmscapi.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\sunpkcs11.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\ext\zipfs.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\javaws.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jfxswt.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\management-agent.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\plugin.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_31\jre\lib\rt.jar;F:\Projects\Fatora\target\classes;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.13\commons-codec-1.13.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.19\commons-compress-1.19.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curv
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-10.0.2
PATH=F:\programs\apache-maven-3.6.2\bin;C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-10.0.2\bin;C:\Program Files\Java\jre-10.0.2\bin;C:\Program Files\Java\jdk-10.0.2;C:\Program Files\Java\jre-10.0.2;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;F:\xampp\tomcat\bin;F:\xampp\mysql\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files\Microsoft SQL Server\120\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\120\Tools\Binn\ManagementStudio\;C:\Program Files (x86)\Microsoft SQL Server\120\DTS\Binn\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\RabbitMQ Server\rabbitmq_server-3.8.1\ebin;C:\Program Files\RabbitMQ Server;F:\programs\oracle\Oracle_Database_12c_Release_2_v12.2.0.1.0;search-ms:displayname=Search%20Results%20in%20Oracle_Database_12c_Release_2_v12.2.0.1.0&crumb=location:F%3A%5Cprograms%5Coracle%5COracle_Database_12c_Release_2_v12.2.0.1.0\bin;F:\app\pc\virtual\product\12.2.0\dbhome_1\bin;F:\intellij\IntelliJ IDEA 2019.3.1\bin;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Microsoft SQL Server\110\Tools\Binn\;F:\xampp\php;C:\ProgramData\ComposerSetup\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-10.0.2\bin;C:\Program Files\Java\jdk-10.0.2;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\heroku\bin;F:\Flutter\flutter\bin;C:\Users\<USER>\SDK\tools;C:\Users\<USER>\SDK\platform-tools;F:\programs\Sdk;F:\programs\Sdk\build-tools;F:\programs\Sdk\tools\bin;F:\programs\Sdk\cmdline-tools\latest\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin
USERNAME=pc
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 69 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 8.1 , 64 bit Build 9600 

CPU:total 4 (2 cores per cpu, 2 threads per core) family 6 model 69 stepping 1, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2

Memory: 4k page, physical 12502096k(5068500k free), swap 18531408k(4277244k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.31-b07) for windows-amd64 JRE (1.8.0_31-b13), built on Dec 17 2014 21:00:28 by "java_re" with MS VC++ 10.0 (VS2010)

time: Tue Jun 15 14:43:52 2021
elapsed time: 20158 seconds (0d 5h 35m 58s)

