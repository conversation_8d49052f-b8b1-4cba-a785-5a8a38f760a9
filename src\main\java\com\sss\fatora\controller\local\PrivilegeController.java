package com.sss.fatora.controller.local;

import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.BankDao;
import com.sss.fatora.dao.local.PrivilegeDao;
import com.sss.fatora.domain.local.Bank;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.service.local.BankService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@RequestMapping("/privilege")
public class PrivilegeController extends GenericController<PrivilegeService, PrivilegeDao, Privilege, Integer> {
    @Override
    @PreAuthorize("hasAnyAuthority('Admin')")
    public ResponseObject getAll(@ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @RequestParam(required = false) int... status) {
        List<Privilege> privilegeList = service.getAllPrivileges();
        return privilegeList != null ?
                ResponseObject.FETCHED_SUCCESS(privilegeList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }
}
