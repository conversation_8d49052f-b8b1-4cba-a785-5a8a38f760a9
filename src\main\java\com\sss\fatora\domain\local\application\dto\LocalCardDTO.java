package com.sss.fatora.domain.local.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class LocalCardDTO {

    @JsonProperty(value = "Card_Product")
    String cardProduct;
    @JsonProperty(value = "Contract_Type")
    String contractType;
    @JsonProperty(value = "Delivery_Branch")
    String deliveryBranch;
    @JsonProperty(value = "Card_Type")
    String cardType;
    @JsonProperty(value = "Cardholder")
    LocalCardholderDTO cardholder;

}
