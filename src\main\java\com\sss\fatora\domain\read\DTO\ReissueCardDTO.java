package com.sss.fatora.domain.read.DTO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sss.fatora.utils.annotation.ExcelProperty;
import com.sss.fatora.utils.log.Loggable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class ReissueCardDTO {
    @JsonProperty(value = "Card_Number")
    String Card_Number;
    @JsonProperty(value = "Expiry_Date")
    String Expiry_Date;
    @JsonProperty(value = "Customer_Number")
    String Customer_Number;
    @JsonProperty(value = "Cardholder_Number")
    String Cardholder_Number;
    @JsonProperty(value = "Agent_Id")
    String Agent_Id;
    @ExcelProperty( name = "DELIVERY BRANCH")
    @JsonProperty(value = "Delivery_Branch")
    String Delivery_Branch;
    @ExcelProperty( name = "OLD NUMBER")
    @JsonProperty(value = "Old_Number")
    int Old_Number;
    @ExcelProperty( name = "OLD EXPIRY")
    @JsonProperty(value = "Old_Expiry")
    int Old_Expiry;
    @JsonProperty(value = "Instant_Card")
    int Instant_Card;
    

}
