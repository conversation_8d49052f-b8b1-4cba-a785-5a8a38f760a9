package com.sss.fatora.domain.middleware.terminals.clients;

import com.sss.fatora.domain.middleware.terminals.ResponseError;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class clientsResponse {

    private List<client> result;
    private String targetUrl;
    private Boolean success;
    private ResponseError error;
    private Boolean unAuthorizedRequest;
    private Boolean __abp;
}
