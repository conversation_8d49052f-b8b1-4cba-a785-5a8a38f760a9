package com.sss.fatora.service.local;

import com.sss.fatora.dao.local.ApplicationUserPrivilegeDao;
import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.ApplicationUserPrivilege;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.service.generic.GenericService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
@Transactional("localDBTransactionManager")
public class ApplicationUserPrivilegeService extends GenericService<ApplicationUserPrivilegeDao, ApplicationUserPrivilege, Integer> {

    @Autowired
    PrivilegeService privilegeService;

    public Set<ApplicationUserPrivilege> getAllPrivilegeForUser(Integer userId) {
        return dao.getAllPrivilegeForUser(userId);
    }

    /**
     * @param userId This Parameter Is For The User Id
     * @param privilegeList This Parameter Is For The List Of Privileges
     *
     * This Function Delete All The Privileges In The List For This User
     * */
    public Boolean deleteAll(Integer userId, List<Integer> privilegeList) {
        try {
            dao.deleteAll(userId, privilegeList);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * @param userId This Parameter Is For The User Id
     * @param privilegeList This Parameter Is For The List Of Privileges
     *
     * This Function Add All The Privileges In The List For This User
     * */
    public Boolean addAll(Integer userId, List<Integer> privilegeList) {

        Set<ApplicationUserPrivilege> applicationUserPrivilegeList = dao.getAllPrivilegeForUser(userId);
        for (Integer privilegeId : privilegeList) {
            ApplicationUserPrivilege applicationUserPrivilege = new ApplicationUserPrivilege();
            applicationUserPrivilege.setPrivilege(new Privilege(privilegeId));
            applicationUserPrivilege.setApplicationUser(new ApplicationUser(userId));
            if (!applicationUserPrivilegeList.contains(applicationUserPrivilege)) {
                dao.save(applicationUserPrivilege);
            } else return false;
        }
        return true;
    }

    /**
     * @param userId This Parameter Is For The User Id
     * @param addedPrivileges This Parameter Is For The List Of Privileges Added To The User
     * @param deletedPrivileges This Parameter Is For The List Of Privileges Deleted From The User

     * This Function Add And Delete All The Specified Privileges In The Two Lists In addedPrivileges
     * And deletedPrivileges
     * */
    public Boolean managePrivileges(Integer userId, List<Integer> addedPrivileges, List<Integer> deletedPrivileges) {
        try {
            if (userId != null) {
                if (!deletedPrivileges.isEmpty()) {
                    deleteAll(userId, deletedPrivileges);
                }
                if (!addedPrivileges.isEmpty()) {
                    addAll(userId, addedPrivileges);
                }
                return true;
            } else
                return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * @param userId This Parameter Is For The User Id
     *
     * This Function Return All Available Privileges For This User
     * */
    public List<Privilege> getAvailablePrivilegesForUser(Integer userId) {
        List<Integer> privilegesIds = new ArrayList<Integer>();
        try {
            for (ApplicationUserPrivilege userPrivilege : getAllPrivilegeForUser(userId)) {
                privilegesIds.add(userPrivilege.getPrivilege().getId());
            }
            return privilegeService.getPrivilegesByList(privilegesIds);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param userId This Parameter Is For The User Id
     *
     * This Function Return All Privileges Assigned To This User
     * */
    public List<ApplicationUserPrivilege> getByUserId(Integer userId) {
        return dao.getByUserId(userId);
    }
}
