package com.sss.fatora.controller.local;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sss.fatora.controller.generic.GenericController;
import com.sss.fatora.dao.local.IssueApplicationDao;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.domain.local.application.IssueApplication;
import com.sss.fatora.domain.local.application.LocalAccount;
import com.sss.fatora.service.local.IssueApplicationService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/issue-application")
public class IssueApplicationController extends GenericController<IssueApplicationService, IssueApplicationDao, IssueApplication, Long> {

    @RequestMapping(value = "/instant-card", method = RequestMethod.POST)
    public ResponseObject instantCardCreation(@ParameterName("application") IssueApplication issueApplication) throws Exception {
        ResponseEntity localApplications = service.instantCardCreation(issueApplication);
        return localApplications != null ?
                ResponseObject.FETCHED_SUCCESS(localApplications,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }

    @RequestMapping(value = "/card-creation", method = RequestMethod.POST)
    public ResponseObject normalCardCreation(@ParameterName("application") IssueApplication issueApplication) throws Exception {
        IssueApplication localApplication = service.normalCardCreation(issueApplication);
        return localApplication != null ?
                ResponseObject.FETCHED_SUCCESS(localApplication,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }

    @RequestMapping(value = "/submit-card", method = RequestMethod.POST)
    public ResponseObject submitCardCreation(@ParameterName("applicationsList") List<IssueApplication> issueApplications) throws Exception {
        List<ResponseEntity> localApplication = service.submitCard(issueApplications);
        return localApplication != null ?
                ResponseObject.FETCHED_SUCCESS(localApplication,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }

    @RequestMapping(value = "/search", method = RequestMethod.POST )
    public ResponseObject searchArchived(@ParameterName(value = "filter", required = false) IssueApplication issueApplication,
                                         @ParameterName(value = "fromDate", required = false) Long fromDate,
                                         @ParameterName(value = "toDate", required = false) Long toDate,
                                         @ParameterName(value = "pagination", required = false) Pagination pagination,
                                         @ParameterName(value = "filterOperator", required = false) MapWrapper<Operator> filterOperator) throws InvocationTargetException, IntrospectionException, NoSuchFieldException, IllegalAccessException, JsonProcessingException {
        if (issueApplication == null)
            issueApplication = new IssueApplication();
        Page<IssueApplication> localApplications = service.mainSearch(issueApplication, fromDate, toDate, pagination, Optional.ofNullable(filterOperator).orElse(new MapWrapper<>()).getMap());
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("count", localApplications != null ? localApplications.getTotalElements() : 0);
        return localApplications!=null?
                ResponseObject.FETCHED_SUCCESS(localApplications.getContent(), extra):
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/get-form-info", method = RequestMethod.GET)
    public ResponseObject getFormInfo() {
        Map formInfo = service.getFormInfo();
        return formInfo != null ?
                ResponseObject.FETCHED_SUCCESS(formInfo, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/move-to-draft", method = RequestMethod.PUT)
    public ResponseObject moveToDraft(@ParameterName(value = "idList") List<Long> idList) {
        List<IssueApplication> draftApplications = service.moveToDraft(idList);

        return draftApplications != null ?
                ResponseObject.FETCHED_SUCCESS(idList, null) :
                ResponseObject.FETCHING_FAILED(null, null);
    }

    @RequestMapping(value = "/archive-applications", method = RequestMethod.PUT)
    public ResponseObject archiveApplications(@ParameterName(value = "idList") List<Long> idList) throws Exception {
        Long countPage = new Long(0);
        List<IssueApplication> archiveApplications = service.moveToArchive(idList);

        return archiveApplications != null ?
                ResponseObject.FETCHED_SUCCESS(idList, null) :
                ResponseObject.FETCHING_FAILED(null, null);

    }

    @RequestMapping(value = "/delete-applications", method = RequestMethod.POST)
    public ResponseObject deleteApplications(@ParameterName(value = "applicationList") List<IssueApplication> issueApplicationList) throws Exception {
        Boolean delete = service.deleteApplications(issueApplicationList);
        return delete ?
                ResponseObject.DELETED_SUCCESS(delete, null) :
                ResponseObject.DELETING_FAILED(null, null);
    }

    @RequestMapping(value = "/update-applications", method = RequestMethod.PUT)
    public ResponseObject updateApplications(@ParameterName("application") IssueApplication issueApplication) throws Exception {
        IssueApplication localApplication = service. normalCardCreation(issueApplication);
        return localApplication != null ?
                ResponseObject.FETCHED_SUCCESS(localApplication,null) :
                ResponseObject.FETCHING_FAILED(null,null);
    }
}
