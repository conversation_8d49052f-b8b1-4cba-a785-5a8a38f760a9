package com.sss.fatora.utils.constants;

public enum Operator {
    GREATER(">"),
    LESS("<"),
    EQUALS("="),
    NOT_EQUALS("<>"),
    NOT_EQUALS_OR_NULL("<>"),
    EQUALS_OR_GREATER_THAN(">="),
    EQUALS_OR_LESS_THAN("<="),
    CONTAINS("LIKE"),
    CONTAINS_WITH_STAR("CONTAINS_WITH_STAR"),
    INTERSECTS("INTERSECTS"),
    IN("IN"), INMV("IN"),
    ISNULL("is null");

    private final String stringValue;

    private Operator(String stringValue) {
        this.stringValue = stringValue;
    }

    public boolean equals(String opValue) {
        return (opValue == null) ? false : stringValue.equals(opValue);
    }

    public String getStringValue() {
        return stringValue;
    }

}
