package com.sss.fatora.controller.generic;


import com.sss.fatora.dao.generic.GenericDao;
import com.sss.fatora.domain.generic.GenericDomain;
import com.sss.fatora.service.generic.GenericService;
import com.sss.fatora.utils.annotation.ParameterName;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.model.MapWrapper;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.ResponseObject;
import com.sss.fatora.utils.model.TempContentModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class GenericController<Service extends GenericService<Dao, Domain, Id>,
        <PERSON><PERSON> extends GenericDao<Domain, Id> & Serializable,
        Domain extends GenericDomain & Serializable,
        Id extends Serializable> {

    @Autowired
    protected Service service;


    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public ResponseObject getAll(@ParameterName(value = "pagination", required = false) Pagination pagination,
                                 @RequestParam(required = false) int... status) {
        try {

            Page<Domain> page = service.getAll(pagination, status);
            Map<String, Object> count = new HashMap<>();
            if (page != null) {
                count.put("count", page.getTotalElements());
                return ResponseObject.FETCHED_SUCCESS(page.getContent(), count);
            } else {
                return ResponseObject.FETCHING_FAILED(null, null);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        }
    }

    @RequestMapping(value = "/filter", method = RequestMethod.POST)
    public ResponseObject getAllWithFilter(@ParameterName(value = "filter", required = false) Domain filter,
                                           @ParameterName(value = "pagination", required = false) Pagination pagination,
                                           @ParameterName(value = "filterOperator") MapWrapper<Operator> filterOperator
    ) {
        try {
            Page<Domain> page = service.dynamicSearch(filter, pagination, "", filterOperator.getMap(), null,false);
            Map<String, Object> count = new HashMap<>();
            if (page != null) {
                count.put("count", page.getTotalElements());
                return ResponseObject.FETCHED_SUCCESS(page.getContent(), count);
            } else {
                return ResponseObject.FETCHING_FAILED(null, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        }
    }

    @RequestMapping(method = RequestMethod.GET, path = "/by-id")
    public ResponseObject getById(@RequestParam("id") Id id) {
        try {
            Domain model = service.getById(id);
            return model != null ?
                    new ResponseObject(ResponseObject.Text.FETCHEDSUCCESSFULY, ResponseObject.ReturnCode.SUCCESS, null, model) :
                    new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);

        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseObject(ResponseObject.Text.FETCHINGFAILED, ResponseObject.ReturnCode.FAILED, null, null);
        }
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResponseObject save(@ParameterName("domain") @Valid Domain domain,
                               @ParameterName(value = "content", required = false) TempContentModel tempContentModel) throws Exception {
        try {

            Domain savedDomain = service.merge(domain);
            return ResponseObject.ADDED_SUCCESS(savedDomain, null);

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @RequestMapping(value = "/add-list", method = RequestMethod.POST)
    public ResponseObject saveList(@ParameterName("list") List<Domain> list) {
        try {
            Iterable<Domain> savedDomains = service.insert(list);
            return ResponseObject.ADDED_SUCCESS(savedDomains, null);

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @RequestMapping(method = RequestMethod.PUT)
    public ResponseObject update(@ParameterName("domain") Domain domain,
                                 @ParameterName(value = "tempContentModel", required = false) TempContentModel tempContentModel) throws Exception {
        try {

            Domain d = service.merge(domain);
            return ResponseObject.UPDATED_SUCCESS(d, null);

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    //    @RequestMapping(method = RequestMethod.PUT)
//    public ResponseObject update(@RequestParam("id") Id id, @ParameterName("domain") Domain domain) throws Exception {
//        try {
//            Domain d = service.update(id,domain);
//            return ResponseObject.UPDATED_SUCCESS(d, null);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
    @RequestMapping(method = RequestMethod.DELETE)
    public ResponseObject delete(@RequestParam("id") Id id) throws Exception {

        Boolean success = service.delete(id);
        return success ?
                ResponseObject.DELETED_SUCCESS(null, null) :
                ResponseObject.RESPONSE_WITH_ERRORS();

    }
}
