// package com.sss.fatora.dao.local;

// import com.sss.fatora.dao.generic.GenericDao;
// import com.sss.fatora.domain.local.AcquiringRequestsLog;
// import org.springframework.data.jpa.repository.Query;
// import org.springframework.data.repository.query.Param;

// import java.util.List;

// public interface AcquiringRequestsLogDao extends GenericDao<AcquiringRequestsLog,Long> {

//     List<AcquiringRequestsLog>getAcquiringRequestsLogs(@Param("requestId") String requestId);
// }
