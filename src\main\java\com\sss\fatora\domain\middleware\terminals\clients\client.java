package com.sss.fatora.domain.middleware.terminals.clients;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class client {
    private String enName;
    private String userName;
    private String type;
    private List<cardRanges> cardRanges;
    private List<Ip> ips;
}
