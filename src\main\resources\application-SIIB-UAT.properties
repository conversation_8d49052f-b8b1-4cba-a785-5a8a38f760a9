#Reading Oracle DB Configuration
spring.datasource-reading-db.url=******************************************
spring.datasource-reading-db.username=SIIB_FE_READONLY
spring.datasource-reading-db.password=SiiB_FEE_Reed123
spring.datasource-reading-db.driverClassName=oracle.jdbc.OracleDriver
spring.jpa.datasource-reading-db.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
spring.datasource-reading-db.hibernate.schema=SVISTA

#Card Issuing Oracle DB Configuration
spring.datasource-card-issuing-db.url=******************************************
spring.datasource-card-issuing-db.username=SIIB_BO_READONLY
spring.datasource-card-issuing-db.password=SiiB_BiO_Reed123
spring.datasource-card-issuing-db.driverClassName=oracle.jdbc.OracleDriver
spring.jpa.datasource-card-issuing-db.dialect=org.hibernate.dialect.Oracle12cDialect
spring.datasource-card-issuing-db.hibernate.schema=MAIN

# Settlement sql server DB Configuration (Old Aria)
spring.datasource-settlement-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource-settlement-db.url=****************************************************
spring.datasource-settlement-db.username=Readonly
spring.datasource-settlement-db.password=Readonly@123
spring.datasource-settlement-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect

# Local sql server DB Configuration
spring.datasource-local-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource-local-db.url=*************************************************************;
spring.datasource-local-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect
spring.datasource-local-db.username=fpanel
spring.datasource-local-db.password=aA@123456
#
## SMPP sql server DB Configuration old
spring.datasource-smpp-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource-smpp-db.url=**********************************************************
spring.datasource-smpp-db.username=SMPPTEST
spring.datasource-smpp-db.password=SMPPTEST_2021
spring.datasource-smpp-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect

# F Notify DB Configuration
spring.datasource-fnotify-db.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource-fnotify-db.url=************************************************************
spring.datasource-fnotify-db.username=Readonly
spring.datasource-fnotify-db.password=Readonly@123
spring.datasource-fnotify-db.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect

# Writing Oracle DB Configuration
spring.datasource-writing-db.url=******************************************
spring.datasource-writing-db.username=FEUPDATE
spring.datasource-writing-db.password=Fe_123_Update
spring.datasource-writing-db.driverClassName=oracle.jdbc.OracleDriver
spring.jpa.datasource-writing-db.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
spring.datasource-writing-db.hibernate.schema=SVISTA

##SIIB Card Issuing Oracle DB Configuration
#spring.datasource-siib-card-issuing-db.url=******************************************
#spring.datasource-siib-card-issuing-db.username=SIIB_BO_READONLY
#spring.datasource-siib-card-issuing-db.password=SiiB_BiO_Reed123
#spring.datasource-siib-card-issuing-db.driverClassName=oracle.jdbc.OracleDriver
#spring.jpa.datasource-siib-card-issuing-db.dialect=org.hibernate.dialect.Oracle12cDialect
#spring.datasource-siib-card-issuing-db.hibernate.schema=MAIN

##SIIB Reading Oracle DB Configuration
#spring.datasource-siib-reading-db.url=******************************************
#spring.datasource-siib-reading-db.username=SIIB_FE_READONLY
#spring.datasource-siib-reading-db.password=SiiB_FEE_Reed123
#spring.datasource-siib-reading-db.driverClassName=oracle.jdbc.OracleDriver
#spring.jpa.datasource-siib-reading-db.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
#spring.datasource-siib-reading-db.hibernate.schema=SVISTA

tempFolder=tempFiles
tempFolderUrl=http://localhost:8085/tempFiles/

spring.resources.static-locations=classpath:${tempFolder}/
spring.mvc.static-path-pattern=/tempFiles/**
spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=30MB
spring.servlet.multipart.maxRequestSize=30MB
server.tomcat.max-http-form-post-size=30MB

spring.jpa.hibernate.ddl-auto=none
spring.jpa.generate-ddl=false
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql = true
svboWebService=http://************:7007/sv/ApplicationService
server.address=localhost
server.port=8080


spring.batch.datasource.url=jdbc:h2:mem:dataflow
spring.batch.datasource.driverClassName=org.h2.Driver
spring.batch.datasource.username=sa
spring.batch.datasource.password=
spring.batch.datasource.database-platform=org.hibernate.dialect.H2Dialect
spring.batch.initialize-schema=always
spring.batch.job.enabled=false


FatoraServiceUrl=http://***********:7777/
FatoraPaymentServiceUrl=http://************:7777/
FatoraFNotifyUrl=http://************:5432/api/v1/
#Payment API Params
TERMINAL_ID_FIXED_VALUE =14740001
#Terminal , Merchant and Device
Base_Url=http://************/
SECRET =JeajryQ4n6VkL5H
SIIB_SERVER_URL = /SIIBfatora/
FATORA_SERVER_URL = /fatora/