package com.sss.fatora.domain.converter;

import com.sss.fatora.utils.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Account2 extends Account {

    private String id ="account_2";
    private String command = "CMMDCRUP";
    @ExcelProperty(name = "account_2:account_number")
    private String account_number;
    @ExcelProperty(name = "account_2:currency")
    private String currency;
    @ExcelProperty(name = "account_2:account_type")
    private String account_type;
    private AccountObject account_object;


//    public String getId() {
//        return super.getId();
//    }
//    @XmlAttribute(name = "id")
//    public void setId(String id) {
//        super.setId("account_2");
//    }
}
