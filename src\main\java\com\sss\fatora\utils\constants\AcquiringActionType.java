package com.sss.fatora.utils.constants;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AcquiringActionType {
    // POS
    ADD_POS("Add_POS", LogType.AcquiringRequests.getType()),
    CLOSE_POS_TERMINAL("Close_POS_Terminal", LogType.AcquiringRequests.getType()),
    REPLACE_POS_DEVICE("Replace_POS_Device", LogType.AcquiringRequests.getType()),
    CHANGE_ACCOUNT_POS("Change_POS_Account", LogType.AcquiringRequests.getType()),
    EDIT_GPS_COORDINATES("Edit_GPS_Coordinates", LogType.AcquiringRequests.getType()),
    ADD_EPOS("Add_EPOS", LogType.AcquiringRequests.getType()),
    CLOSE_EPOS_TERMINAL("Close_EPOS_Terminal", LogType.AcquiringRequests.getType()),
    MANAGE_EPOS_TERMINAL_IDS("Manage_EPOS_Terminal_Ids", LogType.AcquiringRequests.getType()),
    CHANGE_ACCOUNT_EPOS("Change_EPOS_Account", LogType.AcquiringRequests.getType()),
    CHANGE_EGATE_DATA("Change_EGate_Data", LogType.AcquiringRequests.getType()),
    ADD_ATM("Add_ATM", LogType.AcquiringRequests.getType()),
    CHANGE_ATM_DENOMINATIONS("Change_ATM_Denominations", LogType.AcquiringRequests.getType()),
    CHANGE_ACCOUNT_ATM("Change_ATM_Account", LogType.AcquiringRequests.getType()),
    CHANGE_MERCHANT_NAME("Change_Merchant_Name", LogType.AcquiringRequests.getType()),
    CHANGE_MERCHANT_MCC("Change_Merchant_MCC", LogType.AcquiringRequests.getType()),
    CHANGE_MERCHANT_MOBILE("Change_Merchant_Mobile", LogType.AcquiringRequests.getType()),
    CHANGE_MERCHANT_EMAIL("Change_Merchant_Email", LogType.AcquiringRequests.getType());

    private String action;
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}
