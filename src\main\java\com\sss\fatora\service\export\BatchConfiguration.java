package com.sss.fatora.service.export;

import com.sss.fatora.domain.settlement.SettlementTransaction;
import com.sss.fatora.domain.issuing.Customer;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.domain.local.Log;
import com.sss.fatora.domain.local.application.IssueApplication;
import com.sss.fatora.domain.read.CardDataVW;
import com.sss.fatora.service.local.ApplicationUserService;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.read.CardStatusService;
import com.sss.fatora.utils.model.Pagination;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

@Configuration
@EnableBatchProcessing
public class BatchConfiguration {
    private Long CHUNK;
    private static final String EXPORT_FILENAME = "export3.xlsx";

    private final JobBuilderFactory jobBuilderFactory;
    private final StepBuilderFactory stepBuilderFactory;
    private final JobLauncher jobLauncher;
    private SXSSFWorkbook workbook;

    @Autowired
    @Qualifier("readingDBEntityManagerFactory")
    EntityManagerFactory emf;

    @Autowired
    @Qualifier("localDBEntityManagerFactory")
    EntityManagerFactory localEntityManager;

    @Autowired
    @Qualifier("settlementDBEntityManagerFactory")
    EntityManagerFactory settlementEntityManager;

    @Autowired
    @Qualifier("issuingDBEntityManagerFactory")
    EntityManagerFactory issuingEntityManager;
    /*@Autowired
    @Qualifier("readingDBDataSource")
    DataSource dataSource;*/

    @Autowired
    ConfigService configService;

    @Autowired
    CardStatusService cardStatusService;
    @Autowired
    ApplicationUserService applicationUserService;

    @Autowired
    public BatchConfiguration(JobBuilderFactory jobBuilderFactory, StepBuilderFactory stepBuilderFactory, JobLauncher jobLauncher) {
        this.jobBuilderFactory = jobBuilderFactory;
        this.stepBuilderFactory = stepBuilderFactory;
        this.jobLauncher = jobLauncher;
    }

    /**
     * Note : This Class Is Batch Process For Read Chunks Of Data From The Db Then Create An Excel File
     * And Write Those Chunks Into The File
     */
    /*@Bean
    public BatchConfigurer configurer(@Qualifier("springBatchDataSource") DataSource batchDataSource) {
        return new DefaultBatchConfigurer(batchDataSource);
    }*/
    public void run(String query, Class t, Pagination pagination, String filePath, List<String> headers, List<String> columns, Boolean withPrivileges, Boolean encodeCardNo) throws Exception {
        JobExecution execution = jobLauncher.run(
                exportToXlsx(query, t, pagination, filePath, headers, columns, withPrivileges, encodeCardNo),
                new JobParametersBuilder().addLong("uniqueness", System.nanoTime()).toJobParameters()
        );
    }


    public Job exportToXlsx(String query, Class t, Pagination pagination, String filePath, List<String> headers, List<String> columns, Boolean withPrivileges, Boolean encodeCardNo) throws Exception {
        return jobBuilderFactory.get("exportToXlsx")
                .start(step(query, t, pagination, headers, columns, withPrivileges, encodeCardNo))
                .listener(jobListener(filePath))
                .build();
    }


    public Step step(String query, Class t, Pagination pagination, List<String> headers, List<String> columns, Boolean withPrivileges, Boolean encodeCardNo) throws Exception {
        return stepBuilderFactory.get("step")
                .<CardDataVW, CardDataVW>chunk(configService.getChunk())
                .reader(itemReader(t, query, pagination))
                .writer(itemWriter(t, headers, columns, withPrivileges, encodeCardNo))
                .build();
    }

    public <T> ItemReader<T> itemReader(Class<T> tClass, String query, Pagination pagination) throws Exception {
        JpaPagingItemReader<T> databaseReader = new JpaPagingItemReader<>();
        Integer start, end;
        start = pagination.getStart() * pagination.getSize();
        end = pagination.getStart() * pagination.getSize() + pagination.getSize();
        databaseReader.setCurrentItemCount(start);
        databaseReader.setMaxItemCount(end);
        databaseReader.setPageSize(configService.getChunk());
        if (Log.class.getSimpleName().equals(tClass.getSimpleName()) || LocalApplication.class.getSimpleName().equals(tClass.getSimpleName()) || IssueApplication.class.getSimpleName().equals(tClass.getSimpleName())) {
            databaseReader.setEntityManagerFactory(localEntityManager);
        } else if (SettlementTransaction.class.getSimpleName().equals(tClass.getSimpleName())) {
            databaseReader.setEntityManagerFactory(settlementEntityManager);
        } else if (Customer.class.getSimpleName().equals(tClass.getSimpleName())) {
            databaseReader.setEntityManagerFactory(issuingEntityManager);
        } else {
            databaseReader.setEntityManagerFactory(emf);
        }
        // JpaQueryProviderImpl<CardDataVW> jpaQueryProvider = new JpaQueryProviderImpl<>();
        databaseReader.setQueryString(query);

        // databaseReader.setQueryProvider(jpaQueryProvider);
        // databaseReader.setPageSize(1000);
        databaseReader.afterPropertiesSet();
        return databaseReader;
    }

    //@Bean
    public <T> ItemWriter<T> itemWriter(T t, List<String> headers, List<String> columns, Boolean withPrivileges, Boolean encodeCardNo) throws IOException {
        workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet("Sheet");
        return (ItemWriter<T>) new ExcelItemWriter(workbook, sheet, headers, columns, withPrivileges, encodeCardNo, cardStatusService,applicationUserService);
    }

    //@Bean
    JobListener jobListener(String filePath) throws IOException {
        File file = new File(filePath);
        return new JobListener(workbook, new FileOutputStream(file, false));
    }
}
