package com.sss.fatora.service.export.service;

import com.sss.fatora.domain.local.ApplicationUser;
import com.sss.fatora.domain.local.LocalApplication;
import com.sss.fatora.domain.local.Privilege;
import com.sss.fatora.security.model.CustomUserDetails;
import com.sss.fatora.service.local.ConfigService;
import com.sss.fatora.service.local.PrivilegeService;
import com.sss.fatora.utils.constants.Operator;
import com.sss.fatora.utils.constants.PrivilegeType;
import com.sss.fatora.utils.constants.UserType;
import com.sss.fatora.utils.model.Pagination;
import com.sss.fatora.utils.model.TempContentModel;
import com.sss.fatora.utils.service.ContentUtilService;
import com.sss.fatora.utils.service.PaginationService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class ExportApplicationsService {
    final PaginationService paginationService;
    final PrivilegeService privilegeService;
    final ConfigService configService;
    final ExportService exportService;
    final ContentUtilService contentUtilService;

    public ExportApplicationsService(PaginationService paginationService, PrivilegeService privilegeService, ConfigService configService, ExportService exportService, ContentUtilService contentUtilService) {
        this.paginationService = paginationService;
        this.privilegeService = privilegeService;
        this.configService = configService;
        this.exportService = exportService;
        this.contentUtilService = contentUtilService;
    }

    public byte[] search(LocalApplication localApplication, Long fromUDate, Long toUDate, Pagination pagination, Map<String, Operator> filterOperator, List<String> projection, List<String> headers, List<String> columns) throws Exception {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        Page<LocalApplication> localApplications;
        String additionalConstraint = "";
        String oracleFormat = "yyyy-MM-dd HH24:MI:ss";
        String javaFormat = "yyyy-MM-dd HH:mm:ss";
        DateFormat f = new SimpleDateFormat(javaFormat);
        String bins = "";
        String date1 = "";
        String date2 = "";
        String searchLimitConstraint = "";

        if (fromUDate == null)
            fromUDate = new Date(*********).getTime();
        if (toUDate == null)
            toUDate = new Date(System.currentTimeMillis() * 100).getTime();


        //put product number in filter to get only user bank's cards
        if(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank()!=null){
            localApplication.setProductNumber(CustomUserDetails.getCurrentInstance().getApplicationUser().getBank().getCode()+"*");
            filterOperator.put("productNumber",Operator.CONTAINS_WITH_STAR);
        }
/*

        date1 = "to_timestamp('" + f.format(DateUtils.getStartDayOfDate(fromUDate)) + "','" + oracleFormat + "')";
        date2 = "to_timestamp('" + f.format(DateUtils.getEndDayOfDate(toUDate)) + "','" + oracleFormat + "')";
*/

        if (applicationUser.getUserType().equals(UserType.EXTERNAL.getType()))
            additionalConstraint = "substring(TransactionDataVW.hpan,0,6) IN (" + bins + ") " +
                    "LocalApplication.creationDate BETWEEN '" +  f.format(fromUDate) + "' AND '" +  f.format(toUDate) +"'"+ searchLimitConstraint;
        else if (applicationUser.getUserType().equals(UserType.INTERNAL.getType()))
            additionalConstraint = "LocalApplication.creationDate BETWEEN '" +  f.format(fromUDate) + "' AND '" +  f.format(toUDate) +"'"+ searchLimitConstraint;


        if (pagination == null)
            pagination = new Pagination();
        if (pagination.getOrderBy() == null || pagination.getOrderBy().isEmpty()) {
            pagination.setOrderBy("LocalApplication.creationDate");
            pagination.setOrderType("DESC");
        }
        File file = contentUtilService.makeExcelFile();
        TempContentModel tempContentModel = contentUtilService.makeTempModel(file);
        localApplications = exportService.dynamicSearch(localApplication
                , pagination
                , additionalConstraint
                , filterOperator
                , projection
                , file.getPath()
                , headers
                , columns);

        return contentUtilService.fetchAsBytes(tempContentModel);

    }

    public byte[] export(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        if ("draft".equals(localApplication.getStatus())) {
            return exportDrafts(localApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("archive".equals(localApplication.getStatus())) {
            return exportArchive(localApplication, fromDate, toDate, pagination, filterOperator);
        } else if ("submit".equals(localApplication.getStatus())) {
            return exportSubmitted(localApplication, fromDate, toDate, pagination, filterOperator);
        }
        return null;
    }

    public byte[] exportArchive(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String>temp= Stream.of("creationDate", "submitDate", "archiveDate", "firstName", "lastName", "cardHolderName", "mobile", "productNumber", "deliveryBranch","branch","numberOfAccounts")
                .collect(Collectors.toList());
//        Stream.of("id", "status", "cardNumber", "applicationNumber", "contractNumber", "cardHolderNumber", "customerNumber", "firstName", "lastName","productNumber","wsdlStatus")
        List<String> projection = new ArrayList<>(temp/*configService.getDraftsColumns()*/);
        if (privileges == null || privileges.isEmpty())
            return null;

        projection = filterProjectionList(projection, privileges);

        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        return search(localApplication, fromDate, toDate, pagination, filterOperator, projection, configService.getApplicationArchivedHeaders(), configService.getApplicationArchivedColumns());
    }

    public byte[] exportDrafts(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String>temp= Stream.of("creationDate", "firstName", "lastName", "cardHolderName", "mobile", "productNumber", "deliveryBranch","branch","numberOfAccounts")
                .collect(Collectors.toList());
//        Stream.of("id", "status", "cardNumber", "applicationNumber", "contractNumber", "cardHolderNumber", "customerNumber", "firstName", "lastName","productNumber","wsdlStatus")
        List<String> projection = new ArrayList<>(temp/*configService.getDraftsColumns()*/);
        if (privileges == null || privileges.isEmpty())
            return null;

        projection = filterProjectionList(projection, privileges);

        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        return search(localApplication, fromDate, toDate,  pagination, filterOperator, projection, configService.getApplicationDraftHeaders(), configService.getApplicationDraftColumns());
    }

    public byte[] exportSubmitted(LocalApplication localApplication, Long fromDate, Long toDate, Pagination pagination, Map<String, Operator> filterOperator) throws Exception {
        List<String> privileges = getPrivilegesNamesByUserId();
        List<String>temp= Stream.of("creationDate", "submitDate", "wsdlStatus", "firstName", "lastName", "cardHolderNumber", "cardHolderName", "mobile", "productNumber", "deliveryBranch","branch","numberOfAccounts")
                .collect(Collectors.toList());
//        Stream.of("id", "status", "cardNumber", "applicationNumber", "contractNumber", "cardHolderNumber", "customerNumber", "firstName", "lastName","productNumber","wsdlStatus")
        List<String> projection = new ArrayList<>(temp/*configService.getDraftsColumns()*/);
        if (privileges == null || privileges.isEmpty())
            return null;

        projection = filterProjectionList(projection, privileges);

        if (pagination == null) {
            pagination = new Pagination();
            pagination.setStart(0);
            pagination.setSize(CustomUserDetails.getCurrentInstance().getApplicationUser().getMaxExportLimit());
        }
        return search(localApplication, fromDate, toDate, pagination, filterOperator, projection, configService.getApplicationSubmitHeaders(), configService.getApplicationSubmitColumns());
    }



    private List<String> filterProjectionList(List<String> projection, List<String> privileges) {
        return projection.stream()
                .distinct()
//                .filter(privileges::contains)
                .map(s -> "LocalApplication." + s + " AS " + s)
                .collect(Collectors.toList());
    }

    private List<String> getPrivilegesNamesByUserId() {
        ApplicationUser applicationUser = CustomUserDetails.getCurrentInstance().getApplicationUser();
        List<String> privileges = privilegeService.getPrivilegesById(PrivilegeType.TRANSACTION_COLUMN.getType(), applicationUser.getId())
                .stream()
                .map(Privilege::getName)
                .collect(Collectors.toList());
        return privileges;
    }
}
