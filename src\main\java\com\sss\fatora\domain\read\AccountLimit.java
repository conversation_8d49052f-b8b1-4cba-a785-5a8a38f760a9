package com.sss.fatora.domain.read;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Setter
@Getter
@NoArgsConstructor
@Table(name = "FAT_FP_ACCOUNT_LIMIT")
public class AccountLimit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private String id;

    @Column(name = "ACCT_NO")
    private String accountNumber;

    @Column(name = "LMT_ID")
    private Long limitId;

    @Column(name = "DSC")
    private String dsc;

    @Column(name = "LMT")
    private Long limit;

    @Column(name = "TDY")
    private Long tdy;
}
