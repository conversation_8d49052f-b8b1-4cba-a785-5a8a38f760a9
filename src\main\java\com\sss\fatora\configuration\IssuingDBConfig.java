package com.sss.fatora.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "issuingDBEntityManagerFactory",
        transactionManagerRef = "issuingDBTransactionManager",
        basePackages = "com.sss.fatora.dao.issuing"
)
public class IssuingDBConfig {


    @Autowired
    Environment env;

    @Bean(name = "issuingDBDataSourceProperties")
    //@Primary
    @ConfigurationProperties("spring.datasource-card-issuing-db")
    public DataSourceProperties issuingDBDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "issuingDBDataSource")
    // @Primary
    public DataSource issuingDBDataSource() {
        DataSourceProperties issuingDBDataSourceProperties = issuingDBDataSourceProperties();
        return DataSourceBuilder.create()
                .driverClassName(issuingDBDataSourceProperties.getDriverClassName())
                .url(issuingDBDataSourceProperties.getUrl())
                .username(issuingDBDataSourceProperties.getUsername())
                .password(issuingDBDataSourceProperties.getPassword())
                .build();
    }

    //@Primary
    @Bean(name = "issuingDBEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            @Qualifier("readingDBEntityManagerFactoryBuilder") EntityManagerFactoryBuilder issuingDBEntityManagerFactoryBuilder) {

        Map<String, String> JpaProperties = new HashMap<>();

        JpaProperties.put("hibernate.format_sql", env.getProperty("spring.jpa.properties.hibernate.format_sql"));
        JpaProperties.put("hibernate.show_sql", env.getProperty("spring.jpa.show-sql"));
        JpaProperties.put("hibernate.hbm2ddl.auto", "update");
        JpaProperties.put("hibernate.dialect", env.getProperty("spring.jpa.datasource-card-issuing-db.dialect"));
        if (env.getProperty("spring.datasource-card-issuing-db.hibernate.schema") != null ||
                !"".equals(env.getProperty("spring.datasource-card-issuing-db.hibernate.schema")))
            JpaProperties.put("hibernate.default_schema", env.getProperty("spring.datasource-card-issuing-db.hibernate.schema"));

        return issuingDBEntityManagerFactoryBuilder
                .dataSource(issuingDBDataSource())
                .packages("com.sss.fatora.domain.issuing")
                //.persistenceUnit("secondaryDataSource")
                .properties(JpaProperties)
                .build();
    }

    // @Primary
    @Bean(name = "issuingDBTransactionManager")
    public PlatformTransactionManager issuingDBTransactionManager(
            @Qualifier("issuingDBEntityManagerFactory") EntityManagerFactory readingDBEntityManagerFactory) {
        return new JpaTransactionManager(readingDBEntityManagerFactory);
    }

//    @Bean("issuingDBEntityManagerFactoryBuilder")
//    public EntityManagerFactoryBuilder entityManagerFactoryBuilder() {
//        return new EntityManagerFactoryBuilder(new HibernateJpaVendorAdapter(), new HashMap<>(), null);
//    }

}
