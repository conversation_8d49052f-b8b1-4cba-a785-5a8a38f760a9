package com.sss.fatora.utils.constants;

public enum ActionType {
    CARD_SEARCH("Card_Search", LogType.SEARCHES.getType()),
    TRANSACTION_SEARCH("Transaction_Search", LogType.SEARCHES.getType()),
    CUSTOMER_SEARCH("Customer_Search", LogType.SEARCHES.getType()),
    CARD_DETAILS("Card_Details", LogType.DETAILS.getType()),
    TRANSACTION_DETAILS("Transaction_Details", LogType.DETAILS.getType()),
    CUSTOMER_DETAILS("Customer_Details", LogType.DETAILS.getType()),
    CARD_EXPORT("Cards_Export", LogType.EXPORTS.getType()),
    CAPTURED_CARD_EXPORT("Captured_Cards_Export", LogType.EXPORTS.getType()),
    CUSTOMER_EXPORT("Customer_Export", LogType.EXPORTS.getType()),
    WITH_DETAILS("With_Details", LogType.EXPORTS.getType()),
    WITHOUT_DETAILS("Without_Details", LogType.EXPORTS.getType()),
    TRANSACTION_EXPORT("Transactions_Export", LogType.EXPORTS.getType()),
    CARD_RESET("Reset_PIN_count", LogType.Requests.getType()),
    CHANGE_MOBILE_NUMBER("Change_Mobile_Number", LogType.Requests.getType()),
    CHANGE_PRODUCT("Change_Product", LogType.Requests.getType()),
    CHANGE_STATUS("Change_Status", LogType.Requests.getType()),
    CHANGE_LANGUAGE("Change_Language", LogType.Requests.getType()),
    VALIDATE_CARD("Validate_Card", LogType.Requests.getType()),
    Close_Card("Close_Card", LogType.Requests.getType()),
    MANAGE_ACCOUNTS("Manage_Accounts", LogType.Requests.getType()),
    REISSUE_CARD("Reissue_Card", LogType.Requests.getType()),
    RENEW_CARD("Renew_Card",LogType.Requests.getType()),
    INSTANT_REISSUE_CARD("Instant_Reissue_Card",LogType.Requests.getType()),
    INSTANT_RENEW_CARD("Instant_Renew_Card",LogType.Requests.getType());

    private String action;
    private String type;

    ActionType(String action, String type) {
        this.action = action;
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}
